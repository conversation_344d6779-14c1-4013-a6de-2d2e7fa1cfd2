@echo off
echo ===== 可视化当前训练模型预测结果 =====

REM 设置变量
set MODEL_PATH=logs\resnet50_20250518_224213\best_model.pth
set CONFIG_PATH=config.yaml
set IMAGE_DIR=VOCdevkit\VOC2025\JPEGImages
set OUTPUT_DIR=visualization_results\current
set NUM_SAMPLES=10

REM 检查模型文件是否存在
if not exist %MODEL_PATH% (
    echo 错误: 模型文件不存在: %MODEL_PATH%
    echo 请确保当前训练已经保存了最佳模型
    exit /b 1
)

REM 创建输出目录
if not exist %OUTPUT_DIR% mkdir %OUTPUT_DIR%

REM 运行可视化脚本
echo 正在可视化当前训练模型的预测结果...
python visualize_predictions.py --model %MODEL_PATH% --config %CONFIG_PATH% --dir %IMAGE_DIR% --num_samples %NUM_SAMPLES% --output_dir %OUTPUT_DIR%

echo 可视化完成! 结果保存在: %OUTPUT_DIR%
echo 请查看可视化结果以评估模型性能

REM 自动打开输出目录
start %OUTPUT_DIR%

pause
