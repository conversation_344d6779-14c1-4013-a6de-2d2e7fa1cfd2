# Configuration for Semantic Segmentation Training - Simple Version

data:
  dataset_path: "."  # 根目录，用于类别权重计算
  root_dir: "VOCdevkit"
  train_list: "VOC2025/ImageSets/Segmentation/train.txt"
  val_list:   "VOC2025/ImageSets/Segmentation/val.txt"
  train_mask_paths:
    - "VOC2025/SegmentationClass/*.png"
  input_size: [512, 512]
  num_classes: 29

  # 数据增强设置 - 基础版
  augmentation:
    # 基础几何变换
    random_resize_crop:
      enabled: true
      scale: [0.7, 1.0]
      ratio: [0.9, 1.1]
      p: 0.5
    horizontal_flip:
      enabled: true
      p: 0.5
    vertical_flip:
      enabled: false
      p: 0.0
    rotate:
      enabled: false
      limit: 10
      p: 0.0

    # 颜色和光照变换 - 基础版
    color_jitter:
      enabled: true
      brightness: 0.2
      contrast: 0.2
      saturation: 0.2
      hue: 0.05
      p: 0.5

model:
  backbone: "resnet50"
  pretrained: true
  pretrained_weights: ""
  use_attention: false
  attention_type: "none"
  dropout_rate: 0.1
  transfer_learning: true
  finetune_mode: "progressive"
  label_alignment: true

loss:
  use_ce: true
  use_dice: true
  use_focal: false
  use_lovasz: false
  weight_ce: 0.5
  weight_dice: 0.5
  weight_focal: 0.0
  weight_lovasz: 0.0
  focal_gamma: 2.0
  focal_alpha: 0.5
  use_class_weights: true
  class_weight_method: "inverse_frequency"
  label_smoothing: 0.1
  ignore_index: 255

optimizer:
  type: "adamw"
  beta1: 0.9
  beta2: 0.999
  momentum: 0.9

# 学习率策略
scheduler:
  type: "cosine_warmup"
  max_lr: 0.001
  warmup_epochs: 5
  cycles: 1
  pct_start: 0.1
  div_factor: 10.0
  final_div_factor: 100.0
  min_lr: 1e-6

train:
  use_cuda: true
  total_epochs: 100
  # 分层学习率设置
  init_lr: 0.001
  encoder_lr: 5e-4
  decoder_lr: 1e-3
  head_lr: 2e-3
  weight_decay: 1e-4

  # 渐进式解冻设置
  progressive_unfreezing: true
  freeze_epochs: 0
  unfreeze_schedule:
    0: ["segmentation_head", "decoder"]
    5: ["encoder.layer4"]
    10: ["encoder.layer3", "encoder.layer2", "encoder.layer1"]

  # 批量大小设置
  freeze_batch_size: 8
  unfreeze_batch_size: 4
  num_workers: 4

  # 其他训练设置
  save_dir: "logs"
  eval_period: 1
  early_stopping: 20
  mixed_precision: true
  gradient_accumulation: 2
  gradient_clip: 1.0
  use_channels_last: true
  benchmark_cudnn: true
  verbose: true
