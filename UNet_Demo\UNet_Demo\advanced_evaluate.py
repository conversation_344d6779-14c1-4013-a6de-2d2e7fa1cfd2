#!/usr/bin/env python3
"""
高级评估脚本
提供详细的模型性能评估，包括多种指标和可视化
"""

import os
import sys
import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import argparse
import yaml
from tqdm import tqdm
import cv2
import pandas as pd

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.deeplabv3plus import unet
from utils.utils import cvtColor, resize_image, preprocess_input
from utils.utils_metrics import compute_mIoU

class AdvancedEvaluator:
    def __init__(self, model_path, config, device='cuda'):
        """
        初始化评估器
        
        Args:
            model_path: 模型权重路径
            config: 模型配置
            device: 设备类型
        """
        self.device = device
        self.config = config
        self.num_classes = config['num_classes']
        
        # 加载模型
        self.model = self.load_model(model_path, config)
        self.model.eval()
        
        # 类别名称（如果有的话）
        self.class_names = config.get('class_names', [f'Class_{i}' for i in range(self.num_classes)])
        
        print(f"模型加载完成，类别数: {self.num_classes}")
    
    def load_model(self, model_path, config):
        """加载模型"""
        # 创建模型
        model = unet(
            num_classes=config['num_classes'],
            backbone=config['backbone'],
            pretrained=False,
            dropout_rate=config.get('dropout_rate', 0.2),
            use_attention=config.get('use_attention', True),
            attention_type=config.get('attention_type', 'cbam')
        )
        
        # 加载权重
        if torch.cuda.is_available():
            model_dict = torch.load(model_path, map_location=self.device)
        else:
            model_dict = torch.load(model_path, map_location='cpu')
        
        # 处理不同的保存格式
        if 'model_state_dict' in model_dict:
            model.load_state_dict(model_dict['model_state_dict'])
        else:
            model.load_state_dict(model_dict)
        
        model = model.to(self.device)
        return model
    
    def predict_image(self, image_path, input_shape=(512, 512)):
        """预测单张图像"""
        # 读取图像
        image = Image.open(image_path)
        image = cvtColor(image)
        orininal_h = np.array(image).shape[0]
        orininal_w = np.array(image).shape[1]
        
        # 预处理
        image_data, nw, nh = resize_image(image, input_shape)
        image_data = np.expand_dims(np.transpose(preprocess_input(np.array(image_data, np.float32)), (2, 0, 1)), 0)
        
        # 转换为tensor
        images = torch.from_numpy(image_data).to(self.device)
        
        # 预测
        with torch.no_grad():
            outputs = self.model(images)[0]
            pr = torch.softmax(outputs.permute(1, 2, 0), dim=-1).cpu().numpy()
        
        # 后处理
        pr = pr[int((input_shape[0] - nh) // 2) : int((input_shape[0] - nh) // 2 + nh), \
                int((input_shape[1] - nw) // 2) : int((input_shape[1] - nw) // 2 + nw)]
        
        # 调整到原始尺寸
        pr = cv2.resize(pr, (orininal_w, orininal_h), interpolation=cv2.INTER_LINEAR)
        
        return pr
    
    def evaluate_dataset(self, image_dir, label_dir, output_dir):
        """评估整个数据集"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取所有图像文件
        image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        all_predictions = []
        all_labels = []
        per_class_iou = []
        per_image_metrics = []
        
        print("开始评估...")
        
        for image_file in tqdm(image_files, desc="评估中"):
            image_path = os.path.join(image_dir, image_file)
            
            # 对应的标签文件
            label_file = image_file.replace('.jpg', '.png').replace('.jpeg', '.png')
            label_path = os.path.join(label_dir, label_file)
            
            if not os.path.exists(label_path):
                print(f"标签文件不存在: {label_path}")
                continue
            
            # 预测
            prediction = self.predict_image(image_path)
            pred_mask = np.argmax(prediction, axis=-1)
            
            # 读取真实标签
            label = np.array(Image.open(label_path))
            
            # 确保尺寸一致
            if pred_mask.shape != label.shape:
                label = cv2.resize(label, (pred_mask.shape[1], pred_mask.shape[0]), interpolation=cv2.INTER_NEAREST)
            
            # 计算单张图像的指标
            image_metrics = self.compute_image_metrics(pred_mask, label)
            image_metrics['image_name'] = image_file
            per_image_metrics.append(image_metrics)
            
            # 收集所有预测和标签
            all_predictions.extend(pred_mask.flatten())
            all_labels.extend(label.flatten())
            
            # 保存预测结果
            pred_output_path = os.path.join(output_dir, f"pred_{image_file}")
            Image.fromarray(pred_mask.astype(np.uint8)).save(pred_output_path)
        
        # 计算整体指标
        overall_metrics = self.compute_overall_metrics(all_predictions, all_labels)
        
        # 保存结果
        self.save_evaluation_results(overall_metrics, per_image_metrics, output_dir)
        
        return overall_metrics, per_image_metrics
    
    def compute_image_metrics(self, pred, label):
        """计算单张图像的指标"""
        # 忽略背景类（如果需要）
        valid_mask = label != 255  # 假设255是忽略的像素
        
        pred_valid = pred[valid_mask]
        label_valid = label[valid_mask]
        
        # 计算准确率
        accuracy = np.mean(pred_valid == label_valid)
        
        # 计算每类IoU
        ious = []
        for class_id in range(self.num_classes):
            pred_class = (pred_valid == class_id)
            label_class = (label_valid == class_id)
            
            intersection = np.logical_and(pred_class, label_class).sum()
            union = np.logical_or(pred_class, label_class).sum()
            
            if union == 0:
                iou = 1.0 if intersection == 0 else 0.0
            else:
                iou = intersection / union
            
            ious.append(iou)
        
        return {
            'accuracy': accuracy,
            'mean_iou': np.mean(ious),
            'per_class_iou': ious
        }
    
    def compute_overall_metrics(self, predictions, labels):
        """计算整体指标"""
        predictions = np.array(predictions)
        labels = np.array(labels)
        
        # 忽略背景类
        valid_mask = labels != 255
        predictions = predictions[valid_mask]
        labels = labels[valid_mask]
        
        # 整体准确率
        accuracy = np.mean(predictions == labels)
        
        # 混淆矩阵
        cm = confusion_matrix(labels, predictions, labels=range(self.num_classes))
        
        # 每类指标
        per_class_metrics = []
        for i in range(self.num_classes):
            tp = cm[i, i]
            fp = cm[:, i].sum() - tp
            fn = cm[i, :].sum() - tp
            tn = cm.sum() - tp - fp - fn
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            iou = tp / (tp + fp + fn) if (tp + fp + fn) > 0 else 0
            
            per_class_metrics.append({
                'class_id': i,
                'class_name': self.class_names[i],
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'iou': iou,
                'support': cm[i, :].sum()
            })
        
        # 平均指标
        mean_precision = np.mean([m['precision'] for m in per_class_metrics])
        mean_recall = np.mean([m['recall'] for m in per_class_metrics])
        mean_f1 = np.mean([m['f1_score'] for m in per_class_metrics])
        mean_iou = np.mean([m['iou'] for m in per_class_metrics])
        
        return {
            'accuracy': accuracy,
            'mean_precision': mean_precision,
            'mean_recall': mean_recall,
            'mean_f1': mean_f1,
            'mean_iou': mean_iou,
            'per_class_metrics': per_class_metrics,
            'confusion_matrix': cm
        }
    
    def save_evaluation_results(self, overall_metrics, per_image_metrics, output_dir):
        """保存评估结果"""
        # 保存整体指标
        with open(os.path.join(output_dir, 'overall_metrics.txt'), 'w') as f:
            f.write("整体评估指标\n")
            f.write("=" * 50 + "\n")
            f.write(f"准确率: {overall_metrics['accuracy']:.4f}\n")
            f.write(f"平均精确率: {overall_metrics['mean_precision']:.4f}\n")
            f.write(f"平均召回率: {overall_metrics['mean_recall']:.4f}\n")
            f.write(f"平均F1分数: {overall_metrics['mean_f1']:.4f}\n")
            f.write(f"平均IoU: {overall_metrics['mean_iou']:.4f}\n\n")
            
            f.write("每类指标\n")
            f.write("-" * 50 + "\n")
            for metrics in overall_metrics['per_class_metrics']:
                f.write(f"类别 {metrics['class_id']} ({metrics['class_name']}):\n")
                f.write(f"  精确率: {metrics['precision']:.4f}\n")
                f.write(f"  召回率: {metrics['recall']:.4f}\n")
                f.write(f"  F1分数: {metrics['f1_score']:.4f}\n")
                f.write(f"  IoU: {metrics['iou']:.4f}\n")
                f.write(f"  样本数: {metrics['support']}\n\n")
        
        # 保存每张图像的指标
        df = pd.DataFrame(per_image_metrics)
        df.to_csv(os.path.join(output_dir, 'per_image_metrics.csv'), index=False)
        
        # 绘制混淆矩阵
        self.plot_confusion_matrix(overall_metrics['confusion_matrix'], output_dir)
        
        # 绘制每类IoU
        self.plot_per_class_iou(overall_metrics['per_class_metrics'], output_dir)
        
        print(f"评估结果保存至: {output_dir}")
    
    def plot_confusion_matrix(self, cm, output_dir):
        """绘制混淆矩阵"""
        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=self.class_names, yticklabels=self.class_names)
        plt.title('混淆矩阵')
        plt.xlabel('预测类别')
        plt.ylabel('真实类别')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_per_class_iou(self, per_class_metrics, output_dir):
        """绘制每类IoU"""
        class_names = [m['class_name'] for m in per_class_metrics]
        ious = [m['iou'] for m in per_class_metrics]
        
        plt.figure(figsize=(15, 8))
        bars = plt.bar(range(len(class_names)), ious)
        plt.xlabel('类别')
        plt.ylabel('IoU')
        plt.title('每类IoU')
        plt.xticks(range(len(class_names)), class_names, rotation=45, ha='right')
        
        # 添加数值标签
        for bar, iou in zip(bars, ious):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{iou:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'per_class_iou.png'), dpi=300, bbox_inches='tight')
        plt.close()

def main():
    parser = argparse.ArgumentParser(description='高级模型评估')
    parser.add_argument('--model', type=str, required=True, help='模型权重路径')
    parser.add_argument('--config', type=str, required=True, help='模型配置文件')
    parser.add_argument('--image_dir', type=str, required=True, help='测试图像目录')
    parser.add_argument('--label_dir', type=str, required=True, help='测试标签目录')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--device', type=str, default='cuda', help='设备类型')
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建评估器
    evaluator = AdvancedEvaluator(args.model, config, args.device)
    
    # 评估
    overall_metrics, per_image_metrics = evaluator.evaluate_dataset(
        args.image_dir, args.label_dir, args.output_dir
    )
    
    print("评估完成!")
    print(f"整体准确率: {overall_metrics['accuracy']:.4f}")
    print(f"平均IoU: {overall_metrics['mean_iou']:.4f}")

if __name__ == '__main__':
    main()
