#!/usr/bin/env python3
"""
测试后处理对mIoU提升的效果
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader
import logging
from datetime import datetime
import argparse

# 添加路径
sys.path.append('UNet_Demo/UNet_Demo')

from UNet_Demo.UNet_Demo.advanced_postprocessing import AdvancedPostProcessor
from UNet_Demo.UNet_Demo.utils.dataloader import SegmentationDataset
from UNet_Demo.UNet_Demo.utils.utils_metrics import compute_mIoU_tensor
from UNet_Demo.UNet_Demo.nets.deeplabv3plus import unet
import albumentations as A
from albumentations.pytorch import ToTensorV2

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'postprocessing_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_best_model():
    """加载最佳模型"""
    # 查找最佳智能优化模型
    model_files = [f for f in os.listdir('.') if f.startswith('best_smart_optimized_miou_')]
    if not model_files:
        logger.error("未找到智能优化模型文件")
        return None, 0
    
    # 按mIoU排序，选择最佳的
    model_files.sort(key=lambda x: float(x.split('_')[-1].replace('.pth', '')), reverse=True)
    best_model_file = model_files[0]
    best_miou = float(best_model_file.split('_')[-1].replace('.pth', ''))
    
    logger.info(f"加载最佳模型: {best_model_file} (mIoU: {best_miou:.4f})")
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = unet(
        num_classes=29,
        backbone='resnet50',
        pretrained=False,
        dropout_rate=0.3,
        use_attention=True,
        attention_type='cbam'
    ).to(device)
    
    # 加载权重
    try:
        checkpoint = torch.load(best_model_file, map_location=device)
        model.load_state_dict(checkpoint)
        model.eval()
        logger.info("模型加载成功")
        return model, best_miou
    except Exception as e:
        logger.error(f"模型加载失败: {e}")
        return None, 0

def create_val_loader():
    """创建验证数据加载器"""
    # 验证集变换
    val_transform = A.Compose([
        A.Resize(height=512, width=512),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ], additional_targets={"mask": "mask"})
    
    # 创建验证数据集
    val_dataset = SegmentationDataset(
        images_dir="UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/JPEGImages",
        masks_dir="UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/SegmentationClass",
        transform=val_transform,
        split='val'
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=4,
        shuffle=False,
        num_workers=2,
        pin_memory=True
    )
    
    logger.info(f"验证集大小: {len(val_dataset)}")
    return val_loader

def evaluate_basic_prediction(model, val_loader, device, num_samples=50):
    """评估基础预测性能"""
    model.eval()
    miou_list = []
    
    logger.info("评估基础预测性能...")
    
    with torch.no_grad():
        for i, (images, targets) in enumerate(val_loader):
            if i * val_loader.batch_size >= num_samples:
                break
                
            images = images.to(device)
            targets = targets.to(device)
            
            # 基础预测
            outputs = model(images)
            predictions = torch.argmax(outputs, dim=1)
            
            # 计算mIoU
            for b in range(predictions.shape[0]):
                miou = compute_mIoU_tensor(
                    predictions[b:b+1], 
                    targets[b:b+1], 
                    num_classes=29
                )
                miou_list.append(miou)
            
            if i % 5 == 0:
                logger.info(f"已处理 {i * val_loader.batch_size} 个样本")
    
    avg_miou = np.mean(miou_list)
    logger.info(f"基础预测平均mIoU: {avg_miou:.4f}")
    return avg_miou

def evaluate_postprocessed_prediction(model, val_loader, device, num_samples=50):
    """评估后处理预测性能"""
    model.eval()
    postprocessor = AdvancedPostProcessor(num_classes=29, device=device)
    miou_list = []
    
    logger.info("评估后处理预测性能...")
    
    with torch.no_grad():
        for i, (images, targets) in enumerate(val_loader):
            if i * val_loader.batch_size >= num_samples:
                break
                
            images = images.to(device)
            targets = targets.to(device)
            
            # 使用TTA预测
            if postprocessor.config['use_tta']:
                outputs = postprocessor.test_time_augmentation(model, images)
            else:
                outputs = model(images)
            
            # 对每个样本应用后处理
            for b in range(outputs.shape[0]):
                processed_pred, confidence_map = postprocessor.process_prediction(
                    outputs[b], 
                    original_size=None
                )
                
                processed_pred_tensor = torch.from_numpy(processed_pred).to(device)
                
                # 计算mIoU
                miou = compute_mIoU_tensor(
                    processed_pred_tensor.unsqueeze(0), 
                    targets[b:b+1], 
                    num_classes=29
                )
                miou_list.append(miou)
            
            if i % 5 == 0:
                logger.info(f"已处理 {i * val_loader.batch_size} 个样本")
    
    avg_miou = np.mean(miou_list)
    logger.info(f"后处理预测平均mIoU: {avg_miou:.4f}")
    return avg_miou

def test_individual_techniques(model, val_loader, device, num_samples=20):
    """测试各个后处理技术的单独效果"""
    logger.info("测试各个后处理技术的单独效果...")
    
    techniques = {
        'TTA': {'use_tta': True, 'use_morphology': False, 'use_connected_components': False, 
                'use_watershed': False, 'use_boundary_refinement': False, 'use_multi_scale_fusion': False,
                'use_confidence_filtering': False},
        'Morphology': {'use_tta': False, 'use_morphology': True, 'use_connected_components': False, 
                      'use_watershed': False, 'use_boundary_refinement': False, 'use_multi_scale_fusion': False,
                      'use_confidence_filtering': False},
        'Connected Components': {'use_tta': False, 'use_morphology': False, 'use_connected_components': True, 
                               'use_watershed': False, 'use_boundary_refinement': False, 'use_multi_scale_fusion': False,
                               'use_confidence_filtering': False},
        'Multi-scale Fusion': {'use_tta': False, 'use_morphology': False, 'use_connected_components': False, 
                             'use_watershed': False, 'use_boundary_refinement': False, 'use_multi_scale_fusion': True,
                             'use_confidence_filtering': False},
        'Confidence Filtering': {'use_tta': False, 'use_morphology': False, 'use_connected_components': False, 
                               'use_watershed': False, 'use_boundary_refinement': False, 'use_multi_scale_fusion': False,
                               'use_confidence_filtering': True},
    }
    
    results = {}
    
    for tech_name, config in techniques.items():
        logger.info(f"测试技术: {tech_name}")
        
        postprocessor = AdvancedPostProcessor(num_classes=29, device=device)
        postprocessor.config.update(config)
        
        miou_list = []
        
        with torch.no_grad():
            for i, (images, targets) in enumerate(val_loader):
                if i * val_loader.batch_size >= num_samples:
                    break
                    
                images = images.to(device)
                targets = targets.to(device)
                
                # 预测
                if config['use_tta']:
                    outputs = postprocessor.test_time_augmentation(model, images)
                else:
                    outputs = model(images)
                
                # 后处理
                for b in range(outputs.shape[0]):
                    processed_pred, _ = postprocessor.process_prediction(outputs[b])
                    processed_pred_tensor = torch.from_numpy(processed_pred).to(device)
                    
                    miou = compute_mIoU_tensor(
                        processed_pred_tensor.unsqueeze(0), 
                        targets[b:b+1], 
                        num_classes=29
                    )
                    miou_list.append(miou)
        
        avg_miou = np.mean(miou_list)
        results[tech_name] = avg_miou
        logger.info(f"{tech_name} mIoU: {avg_miou:.4f}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='测试后处理对mIoU的提升效果')
    parser.add_argument('--num_samples', type=int, default=50, help='测试样本数量')
    parser.add_argument('--test_individual', action='store_true', help='测试各个技术的单独效果')
    args = parser.parse_args()
    
    logger.info("🔬 开始测试后处理对mIoU的提升效果")
    logger.info("=" * 60)
    
    # 检查CUDA
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载最佳模型
    model, original_miou = load_best_model()
    if model is None:
        return
    
    # 创建验证数据加载器
    val_loader = create_val_loader()
    
    # 评估基础预测
    basic_miou = evaluate_basic_prediction(model, val_loader, device, args.num_samples)
    
    # 评估后处理预测
    postprocessed_miou = evaluate_postprocessed_prediction(model, val_loader, device, args.num_samples)
    
    # 计算改善
    improvement = postprocessed_miou - basic_miou
    improvement_pct = (improvement / basic_miou) * 100 if basic_miou > 0 else 0
    
    # 输出结果
    logger.info("\n" + "=" * 60)
    logger.info("📊 后处理效果评估结果")
    logger.info("=" * 60)
    logger.info(f"原始模型mIoU (训练时): {original_miou:.4f}")
    logger.info(f"基础预测mIoU (验证集): {basic_miou:.4f}")
    logger.info(f"后处理mIoU (验证集): {postprocessed_miou:.4f}")
    logger.info(f"绝对改善: +{improvement:.4f}")
    logger.info(f"相对改善: +{improvement_pct:.2f}%")
    
    if improvement > 0:
        logger.info("✅ 后处理成功提升了mIoU!")
        if improvement >= 0.01:
            logger.info("🎉 显著改善! (≥1%)")
        elif improvement >= 0.005:
            logger.info("👍 中等改善! (≥0.5%)")
        else:
            logger.info("📈 轻微改善")
    else:
        logger.info("❌ 后处理未能提升mIoU")
    
    # 测试各个技术的单独效果
    if args.test_individual:
        logger.info("\n" + "=" * 60)
        logger.info("🔍 各个后处理技术的单独效果")
        logger.info("=" * 60)
        
        individual_results = test_individual_techniques(model, val_loader, device, 20)
        
        # 排序显示
        sorted_results = sorted(individual_results.items(), key=lambda x: x[1], reverse=True)
        
        for tech_name, miou in sorted_results:
            improvement = miou - basic_miou
            improvement_pct = (improvement / basic_miou) * 100 if basic_miou > 0 else 0
            logger.info(f"{tech_name:20s}: {miou:.4f} (+{improvement:+.4f}, {improvement_pct:+.2f}%)")
    
    logger.info(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
