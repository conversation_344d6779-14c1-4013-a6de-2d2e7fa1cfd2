"""
ADE20K数据集标签信息
"""

# ADE20K数据集的150个类别（包括背景）
ADE20K_CLASSES = [
    'background', 'wall', 'building', 'sky', 'floor', 'tree', 'ceiling', 'road', 'bed', 'windowpane',
    'grass', 'cabinet', 'sidewalk', 'person', 'earth', 'door', 'table', 'mountain', 'plant', 'curtain',
    'chair', 'car', 'water', 'painting', 'sofa', 'shelf', 'house', 'sea', 'mirror', 'rug',
    'field', 'armchair', 'seat', 'fence', 'desk', 'rock', 'wardrobe', 'lamp', 'bathtub', 'railing',
    'cushion', 'base', 'box', 'column', 'signboard', 'chest of drawers', 'counter', 'sand', 'sink', 'skyscraper',
    'fireplace', 'refrigerator', 'grandstand', 'path', 'stairs', 'runway', 'case', 'pool table', 'pillow', 'screen door',
    'stairway', 'river', 'bridge', 'bookcase', 'blind', 'coffee table', 'toilet', 'flower', 'book', 'hill',
    'bench', 'countertop', 'stove', 'palm', 'kitchen island', 'computer', 'swivel chair', 'boat', 'bar', 'arcade machine',
    'hovel', 'bus', 'towel', 'light', 'truck', 'tower', 'chandelier', 'awning', 'streetlight', 'booth',
    'television receiver', 'airplane', 'dirt track', 'apparel', 'pole', 'land', 'bannister', 'escalator', 'ottoman', 'bottle',
    'buffet', 'poster', 'stage', 'van', 'ship', 'fountain', 'conveyer belt', 'canopy', 'washer', 'plaything',
    'swimming pool', 'stool', 'barrel', 'basket', 'waterfall', 'tent', 'bag', 'minibike', 'cradle', 'oven',
    'ball', 'food', 'step', 'tank', 'trade name', 'microwave', 'pot', 'animal', 'bicycle', 'lake',
    'dishwasher', 'screen', 'blanket', 'sculpture', 'hood', 'sconce', 'vase', 'traffic light', 'tray', 'ashcan',
    'fan', 'pier', 'crt screen', 'plate', 'monitor', 'bulletin board', 'shower', 'radiator', 'glass', 'clock',
    'flag'
]

# 我们的数据集类别
OUR_CLASSES = [
    "_background_", "tree", "grass", "sky", "mountain", "signboard", "shrub", "farmland", 
    "vegetable garden", "street lamp", "artistic wall paintings", "pole", "buildings", "road", 
    "historic building", "landscape sculpture", "cultural landscape wall", "safety facility", 
    "sanitation facility", "service facility", "fence", "pavement", "tree pit", "steps", 
    "retaining wall", "peach tree", "water body", "miscellaneous elements"
]

# 创建从我们的类别到ADE20K类别的映射
# 值为-1表示没有对应的ADE20K类别
# 这个映射是基于语义相似性手动创建的
LABEL_MAPPING = {
    0: 0,    # _background_ -> background
    1: 5,    # tree -> tree
    2: 10,   # grass -> grass
    3: 3,    # sky -> sky
    4: 17,   # mountain -> mountain
    5: 44,   # signboard -> signboard
    6: 18,   # shrub -> plant
    7: 30,   # farmland -> field
    8: 30,   # vegetable garden -> field (近似)
    9: 98,   # street lamp -> streetlight
    10: 23,  # artistic wall paintings -> painting
    11: 93,  # pole -> pole
    12: 2,   # buildings -> building
    13: 7,   # road -> road
    14: 2,   # historic building -> building (部分匹配)
    15: 133, # landscape sculpture -> sculpture
    16: 1,   # cultural landscape wall -> wall (部分匹配)
    17: -1,  # safety facility -> 无明确对应
    18: -1,  # sanitation facility -> 无明确对应
    19: -1,  # service facility -> 无明确对应
    20: 33,  # fence -> fence
    21: 12,  # pavement -> sidewalk
    22: -1,  # tree pit -> 无明确对应
    23: 54,  # steps -> stairs
    24: 1,   # retaining wall -> wall (部分匹配)
    25: 5,   # peach tree -> tree (部分匹配)
    26: 22,  # water body -> water
    27: -1,  # miscellaneous elements -> 无明确对应
}

# 创建从ADE20K类别到我们的类别的反向映射
# 这个映射用于可视化和调试
REVERSE_MAPPING = {}
for our_idx, ade_idx in LABEL_MAPPING.items():
    if ade_idx != -1:
        if ade_idx not in REVERSE_MAPPING:
            REVERSE_MAPPING[ade_idx] = []
        REVERSE_MAPPING[ade_idx].append(our_idx)

# 打印映射信息
def print_mapping_info():
    """打印标签映射信息"""
    print("标签映射信息:")
    print("-" * 80)
    print(f"{'我们的类别':<25} | {'ADE20K类别':<25} | {'映射类型':<15}")
    print("-" * 80)
    
    for our_idx, our_class in enumerate(OUR_CLASSES):
        ade_idx = LABEL_MAPPING[our_idx]
        if ade_idx != -1:
            ade_class = ADE20K_CLASSES[ade_idx]
            if our_class.lower() == ade_class.lower():
                mapping_type = "完全匹配"
            elif our_class.lower() in ade_class.lower() or ade_class.lower() in our_class.lower():
                mapping_type = "部分匹配"
            else:
                mapping_type = "语义相似"
        else:
            ade_class = "无对应"
            mapping_type = "无匹配"
        
        print(f"{our_class:<25} | {ade_class:<25} | {mapping_type:<15}")
    
    print("-" * 80)
    
    # 计算匹配率
    matched = sum(1 for idx in LABEL_MAPPING.values() if idx != -1)
    total = len(OUR_CLASSES)
    match_rate = matched / total * 100
    
    print(f"匹配率: {match_rate:.2f}% ({matched}/{total})")
    print("-" * 80)

if __name__ == "__main__":
    print_mapping_info()
