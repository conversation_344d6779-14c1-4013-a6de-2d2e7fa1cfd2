# 融合优化配置 - 目标mIoU > 0.4
# 平衡性能提升与训练效率的最佳方案

# 数据配置
data:
  num_classes: 29
  input_shape: [512, 512, 3]
  train_annotation_path: "VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt"
  val_annotation_path: "VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt"

# 模型配置 - 适度升级backbone
model:
  backbone: "efficientnet-b4"    # 从resnet50升级到efficientnet-b4 (平衡性能与效率)
  pretrained: true
  num_classes: 29
  
  # 模型增强 - 保守策略
  use_attention: true             # 启用CBAM注意力
  attention_type: "cbam"
  use_deep_supervision: false    # 暂时禁用，避免复杂化
  dropout_rate: 0.3              # 适度dropout防止过拟合

# 数据增强 - 强化但不过度
augmentation:
  # 基础几何变换 - 增强
  random_resize_crop:
    enabled: true
    scale: [0.4, 1.1]            # 适度扩大范围
    ratio: [0.7, 1.4]
    p: 0.9
  horizontal_flip:
    enabled: true
    p: 0.5
  vertical_flip:
    enabled: true
    p: 0.2                       # 保守的垂直翻转
  rotation:
    enabled: true
    degrees: [-20, 20]           # 适度旋转
    p: 0.6
  
  # 颜色变换 - 适度增强
  color_jitter:
    enabled: true
    brightness: 0.3
    contrast: 0.3
    saturation: 0.3
    hue: 0.1
    p: 0.7
  
  # 高级增强 - 选择性启用
  gaussian_blur:
    enabled: true
    kernel_size: [3, 5]
    sigma: [0.1, 1.5]
    p: 0.2
  
  # 批量级增强 - 轻度使用
  cutmix:
    enabled: true
    alpha: 1.0
    p: 0.2                       # 降低概率，避免过度增强
  
  mixup:
    enabled: true
    alpha: 0.3
    p: 0.1                       # 轻度使用

# 损失函数 - 保持成功的极端平衡策略
loss:
  use_ce: true
  use_dice: true
  use_focal: true
  use_lovasz: true
  
  # 权重配置 - 保持成功的配置
  weight_ce: 0.1                 # 保持低CE权重
  weight_dice: 2.5               # 保持高Dice权重
  weight_focal: 3.0              # 保持高Focal权重
  weight_lovasz: 1.8             # 保持Lovász权重
  
  # Focal Loss配置
  focal_gamma: 6.0               # 保持极高gamma值
  focal_alpha: 0.6
  
  # 类别权重 - 保持成功的极端权重策略
  use_class_weights: true
  class_weight_method: "manual"
  manual_weights: [0.01, 1.0, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 100.0, 1.0, 90.0, 1.0, 1.0, 0.01, 1.0, 1.0, 1.0, 1.0, 80.0, 1.0, 1.0, 0.01, 1.0, 70.0, 1.0, 1.0, 60.0, 50.0, 40.0]
  
  label_smoothing: 0.15          # 保持标签平滑
  ignore_index: 255

# 优化器配置
optimizer:
  type: "adamw"
  beta1: 0.9
  beta2: 0.999
  weight_decay: 1e-4             # 适度权重衰减

# 学习率调度 - 融合策略
scheduler:
  type: "cosine_warmup"          # 使用余弦退火+预热
  max_lr: 0.0008                 # 适中的最大学习率
  min_lr: 1e-7                   # 更低的最小学习率
  warmup_epochs: 15              # 增加预热轮数
  cycles: 2                      # 2个周期，平衡训练时间

# 训练配置 - 平衡时间与性能
train:
  use_cuda: true
  total_epochs: 400              # 延长训练时间，但不过度
  
  # 分层学习率 - 精细调优
  init_lr: 0.0008
  encoder_lr: 0.0002             # 编码器较低学习率
  decoder_lr: 0.0005             # 解码器中等学习率
  head_lr: 0.001                 # 分割头较高学习率
  
  # 渐进式解冻 - 优化策略
  progressive_unfreezing: true
  freeze_epochs: 0
  unfreeze_schedule:
    0: ["segmentation_head", "decoder"]
    8: ["encoder.layer4"]          # 稍微延后解冻
    16: ["encoder.layer3"]
    24: ["encoder.layer2"]
    32: ["encoder.layer1"]
  
  # 批量大小 - 考虑GPU内存
  freeze_batch_size: 8           # 适度增加
  unfreeze_batch_size: 6         # 平衡内存使用
  num_workers: 4
  
  # 训练策略
  save_dir: "logs"
  eval_period: 1
  early_stopping: 60             # 适中的耐心值
  mixed_precision: true          # 启用混合精度
  gradient_accumulation: 4       # 梯度累积
  gradient_clip: 1.2             # 适度梯度裁剪
  
  # 性能优化
  use_channels_last: true
  benchmark_cudnn: true
  
  # 模型保存策略
  save_best_only: false          # 保存所有检查点
  save_frequency: 20             # 每20轮保存一次

# 验证和测试配置
validation:
  # 测试时增强 - 轻量级TTA
  tta:
    enabled: true
    scales: [0.9, 1.0, 1.1]      # 减少尺度数量
    flip: true                   # 只使用翻转
    rotate: false                # 禁用旋转以节省时间
  
  # 验证频率
  eval_frequency: 5              # 每5轮验证一次
  detailed_metrics: true         # 详细指标记录

# 预训练配置 - 保守策略
pretrain:
  enabled: true
  dataset: "ADE20K"              # 使用ADE20K预训练
  weights_path: ""               # 自动下载
  finetune_method: "gradual"     # 渐进式微调
  
  # 权重初始化
  init_method: "xavier_normal"   # 使用Xavier初始化
  
# 监控和日志
monitoring:
  tensorboard: true
  log_frequency: 10              # 每10个batch记录一次
  plot_frequency: 50             # 每50轮绘制图表
  
  # 监控指标
  track_metrics:
    - "miou"
    - "pixel_accuracy"
    - "class_accuracy"
    - "loss_components"
  
  # 困难类别特别监控
  difficult_classes: [8, 10, 18, 23, 26, 27, 28]
  monitor_difficult_classes: true

# 硬件配置
hardware:
  pin_memory: true
  non_blocking: true
  num_workers: 4
  prefetch_factor: 2

# 实验配置
experiment:
  name: "fusion_optimized_v1"
  description: "融合四大优化策略的平衡方案"
  tags: ["fusion", "efficientnet-b4", "extreme_balance", "tta"]
  
  # 随机种子
  seed: 42
  deterministic: true

# 集成学习配置 - 可选
ensemble:
  enabled: false                 # 初始禁用，单模型训练完成后再考虑
  num_models: 3                  # 如果启用，使用3个模型
  voting_method: "soft"
  
  # 模型变体配置
  variants:
    - backbone: "efficientnet-b4"
      augmentation_strength: "medium"
    - backbone: "resnet50"
      augmentation_strength: "high"
    - backbone: "efficientnet-b3"
      augmentation_strength: "low"
