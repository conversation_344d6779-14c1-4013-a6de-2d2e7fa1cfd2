"""
优化的数据增强模块
针对类别不平衡问题的特殊增强策略
"""

import cv2
import numpy as np
import torch
import random
from PIL import Image, ImageEnhance
import albumentations as A
from albumentations.pytorch import ToTensorV2

class RareClassAugmentation:
    """针对稀有类别的特殊增强"""

    def __init__(self, rare_class_threshold=0.01, augmentation_factor=3):
        self.rare_class_threshold = rare_class_threshold
        self.augmentation_factor = augmentation_factor

    def __call__(self, image, mask):
        """
        对包含稀有类别的样本进行特殊增强
        """
        # 计算每个类别的像素占比
        unique_classes, counts = np.unique(mask, return_counts=True)
        total_pixels = mask.size

        # 检查是否包含稀有类别
        has_rare_class = False
        for cls, count in zip(unique_classes, counts):
            if count / total_pixels < self.rare_class_threshold:
                has_rare_class = True
                break

        if has_rare_class:
            # 对稀有类别样本进行额外增强
            augmented_samples = []

            for _ in range(self.augmentation_factor):
                # 随机选择增强方式
                aug_type = random.choice(['rotate', 'flip', 'crop', 'brightness'])

                if aug_type == 'rotate':
                    angle = random.uniform(-30, 30)
                    aug_image, aug_mask = self._rotate(image, mask, angle)
                elif aug_type == 'flip':
                    aug_image, aug_mask = self._flip(image, mask)
                elif aug_type == 'crop':
                    aug_image, aug_mask = self._random_crop(image, mask)
                elif aug_type == 'brightness':
                    aug_image = self._adjust_brightness(image)
                    aug_mask = mask.copy()

                augmented_samples.append((aug_image, aug_mask))

            return augmented_samples

        return [(image, mask)]

    def _rotate(self, image, mask, angle):
        """旋转增强"""
        h, w = image.shape[:2]
        center = (w // 2, h // 2)

        # 计算旋转矩阵
        M = cv2.getRotationMatrix2D(center, angle, 1.0)

        # 旋转图像和掩码
        rotated_image = cv2.warpAffine(image, M, (w, h), borderMode=cv2.BORDER_REFLECT)
        rotated_mask = cv2.warpAffine(mask, M, (w, h), borderMode=cv2.BORDER_CONSTANT, borderValue=255)

        return rotated_image, rotated_mask

    def _flip(self, image, mask):
        """翻转增强"""
        flip_type = random.choice([0, 1, -1])  # 垂直、水平、双向翻转

        flipped_image = cv2.flip(image, flip_type)
        flipped_mask = cv2.flip(mask, flip_type)

        return flipped_image, flipped_mask

    def _random_crop(self, image, mask):
        """随机裁剪增强"""
        h, w = image.shape[:2]
        crop_size = min(h, w) // 2

        # 随机选择裁剪位置
        x = random.randint(0, w - crop_size)
        y = random.randint(0, h - crop_size)

        # 裁剪并调整大小
        cropped_image = image[y:y+crop_size, x:x+crop_size]
        cropped_mask = mask[y:y+crop_size, x:x+crop_size]

        # 调整回原始大小
        resized_image = cv2.resize(cropped_image, (w, h), interpolation=cv2.INTER_LINEAR)
        resized_mask = cv2.resize(cropped_mask, (w, h), interpolation=cv2.INTER_NEAREST)

        return resized_image, resized_mask

    def _adjust_brightness(self, image):
        """亮度调整"""
        factor = random.uniform(0.7, 1.3)

        if len(image.shape) == 3:
            # RGB图像
            pil_image = Image.fromarray(image)
            enhancer = ImageEnhance.Brightness(pil_image)
            enhanced_image = enhancer.enhance(factor)
            return np.array(enhanced_image)
        else:
            # 灰度图像
            return np.clip(image * factor, 0, 255).astype(np.uint8)

class MixUpAugmentation:
    """MixUp数据增强"""

    def __init__(self, alpha=0.2):
        self.alpha = alpha

    def __call__(self, batch_images, batch_masks):
        """
        对批次数据进行MixUp增强
        """
        batch_size = len(batch_images)
        if batch_size < 2:
            return batch_images, batch_masks

        # 生成混合权重
        lam = np.random.beta(self.alpha, self.alpha)

        # 随机打乱索引
        indices = np.random.permutation(batch_size)

        mixed_images = []
        mixed_masks = []

        for i in range(batch_size):
            # 混合图像
            mixed_image = lam * batch_images[i] + (1 - lam) * batch_images[indices[i]]

            # 混合掩码（使用硬混合）
            if lam > 0.5:
                mixed_mask = batch_masks[i]
            else:
                mixed_mask = batch_masks[indices[i]]

            mixed_images.append(mixed_image)
            mixed_masks.append(mixed_mask)

        return mixed_images, mixed_masks

class CutMixAugmentation:
    """CutMix数据增强"""

    def __init__(self, alpha=1.0):
        self.alpha = alpha

    def __call__(self, batch_images, batch_masks):
        """
        对批次数据进行CutMix增强
        """
        batch_size = len(batch_images)
        if batch_size < 2:
            return batch_images, batch_masks

        # 生成混合权重
        lam = np.random.beta(self.alpha, self.alpha)

        # 随机打乱索引
        indices = np.random.permutation(batch_size)

        mixed_images = []
        mixed_masks = []

        for i in range(batch_size):
            image1 = batch_images[i]
            image2 = batch_images[indices[i]]
            mask1 = batch_masks[i]
            mask2 = batch_masks[indices[i]]

            # 计算裁剪区域
            h, w = image1.shape[:2]
            cut_ratio = np.sqrt(1 - lam)
            cut_w = int(w * cut_ratio)
            cut_h = int(h * cut_ratio)

            # 随机选择裁剪位置
            cx = np.random.randint(w)
            cy = np.random.randint(h)

            bbx1 = np.clip(cx - cut_w // 2, 0, w)
            bby1 = np.clip(cy - cut_h // 2, 0, h)
            bbx2 = np.clip(cx + cut_w // 2, 0, w)
            bby2 = np.clip(cy + cut_h // 2, 0, h)

            # 执行CutMix
            mixed_image = image1.copy()
            mixed_mask = mask1.copy()

            mixed_image[bby1:bby2, bbx1:bbx2] = image2[bby1:bby2, bbx1:bbx2]
            mixed_mask[bby1:bby2, bbx1:bbx2] = mask2[bby1:bby2, bbx1:bbx2]

            mixed_images.append(mixed_image)
            mixed_masks.append(mixed_mask)

        return mixed_images, mixed_masks

class AdvancedAugmentation:
    """高级数据增强管道"""

    def __init__(self, config):
        self.config = config

        # 基础增强
        self.basic_transform = A.Compose([
            A.HorizontalFlip(p=config.get('horizontal_flip', 0.5)),
            A.VerticalFlip(p=config.get('vertical_flip', 0.2)),
            A.Rotate(limit=config.get('rotation', 15), p=0.5),
            A.RandomScale(scale_limit=config.get('scale', [0.8, 1.2]), p=0.5),
        ])

        # 颜色增强
        if config.get('color_jitter', {}).get('enabled', True):
            color_config = config['color_jitter']
            self.color_transform = A.Compose([
                A.ColorJitter(
                    brightness=color_config.get('brightness', 0.2),
                    contrast=color_config.get('contrast', 0.2),
                    saturation=color_config.get('saturation', 0.2),
                    hue=color_config.get('hue', 0.1),
                    p=0.5
                )
            ])
        else:
            self.color_transform = None

        # 稀有类别增强
        if config.get('rare_class_augmentation', {}).get('enabled', False):
            rare_config = config['rare_class_augmentation']
            self.rare_class_aug = RareClassAugmentation(
                rare_class_threshold=rare_config.get('rare_class_threshold', 0.01),
                augmentation_factor=rare_config.get('augmentation_factor', 3)
            )
        else:
            self.rare_class_aug = None

        # MixUp增强
        if config.get('mixup', {}).get('enabled', False):
            self.mixup = MixUpAugmentation(alpha=config['mixup'].get('alpha', 0.2))
        else:
            self.mixup = None

        # CutMix增强
        if config.get('cutmix', {}).get('enabled', False):
            self.cutmix = CutMixAugmentation(alpha=config['cutmix'].get('alpha', 1.0))
        else:
            self.cutmix = None

    def __call__(self, image, mask):
        """应用数据增强"""
        # 基础增强
        if self.basic_transform:
            transformed = self.basic_transform(image=image, mask=mask)
            image, mask = transformed['image'], transformed['mask']

        # 颜色增强
        if self.color_transform:
            transformed = self.color_transform(image=image)
            image = transformed['image']

        # 稀有类别增强
        if self.rare_class_aug:
            augmented_samples = self.rare_class_aug(image, mask)
            return augmented_samples

        return [(image, mask)]

    def apply_batch_augmentation(self, batch_images, batch_masks):
        """应用批次级增强"""
        # MixUp增强
        if self.mixup and random.random() < 0.5:
            batch_images, batch_masks = self.mixup(batch_images, batch_masks)

        # CutMix增强
        if self.cutmix and random.random() < 0.5:
            batch_images, batch_masks = self.cutmix(batch_images, batch_masks)

        return batch_images, batch_masks

class ClassSpecificAugmentation:
    """针对特定类别的增强策略"""

    def __init__(self, difficult_classes=[8, 18, 28], oversample_ratio=3.0):
        self.difficult_classes = difficult_classes
        self.oversample_ratio = oversample_ratio

        # 针对困难类别的特殊增强
        self.difficult_aug = A.Compose([
            # 更强的几何变换
            A.RandomRotate90(p=0.8),
            A.Rotate(limit=30, p=0.8),
            A.HorizontalFlip(p=0.8),
            A.VerticalFlip(p=0.5),

            # 更强的颜色变换
            A.ColorJitter(brightness=0.4, contrast=0.4, saturation=0.4, hue=0.2, p=0.9),
            A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.8),

            # 噪声和模糊
            A.GaussNoise(var_limit=(10, 50), p=0.5),
            A.GaussianBlur(blur_limit=(3, 7), p=0.3),

            # 弹性变换
            A.ElasticTransform(alpha=1, sigma=50, alpha_affine=50, p=0.3),
            A.GridDistortion(num_steps=5, distort_limit=0.3, p=0.3),
        ])

    def should_apply_difficult_aug(self, mask):
        """判断是否应该对困难类别应用特殊增强"""
        unique_classes = np.unique(mask)
        return any(cls in self.difficult_classes for cls in unique_classes)

    def apply_difficult_augmentation(self, image, mask):
        """对包含困难类别的样本应用特殊增强"""
        if self.should_apply_difficult_aug(mask):
            # 应用特殊增强
            augmented = self.difficult_aug(image=image, mask=mask)
            return augmented['image'], augmented['mask']
        return image, mask


def get_augmentation_pipeline(config):
    """获取数据增强管道"""
    if config.get('data_augmentation'):
        return AdvancedAugmentation(config['data_augmentation'])
    else:
        return None


def get_class_specific_augmentation(config):
    """获取类别特定的数据增强"""
    class_config = config.get('data', {}).get('data_augmentation', {}).get('class_specific', {})
    if class_config.get('enabled', False):
        return ClassSpecificAugmentation(
            difficult_classes=class_config.get('difficult_classes', [8, 18, 28]),
            oversample_ratio=class_config.get('oversample_ratio', 3.0)
        )
    return None


class SmartAugmentation:
    """智能数据增强 - 根据类别表现动态调整增强策略"""

    def __init__(self, class_performance=None, difficult_classes=[8, 18, 28]):
        self.class_performance = class_performance or {}
        self.difficult_classes = difficult_classes

        # 基础增强
        self.base_aug = A.Compose([
            A.HorizontalFlip(p=0.5),
            A.RandomRotate90(p=0.5),
            A.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1, p=0.5),
        ])

        # 困难类别增强
        self.difficult_aug = A.Compose([
            A.HorizontalFlip(p=0.8),
            A.VerticalFlip(p=0.5),
            A.RandomRotate90(p=0.8),
            A.Rotate(limit=30, p=0.8),
            A.ColorJitter(brightness=0.4, contrast=0.4, saturation=0.4, hue=0.2, p=0.9),
            A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.8),
            A.GaussNoise(var_limit=(10, 50), p=0.5),
            A.GaussianBlur(blur_limit=(3, 7), p=0.3),
            A.ElasticTransform(alpha=1, sigma=50, alpha_affine=50, p=0.3),
            A.GridDistortion(num_steps=5, distort_limit=0.3, p=0.3),
        ])

        # 极端增强（用于零IoU类别）
        self.extreme_aug = A.Compose([
            A.HorizontalFlip(p=1.0),
            A.VerticalFlip(p=0.8),
            A.RandomRotate90(p=1.0),
            A.Rotate(limit=45, p=1.0),
            A.ColorJitter(brightness=0.5, contrast=0.5, saturation=0.5, hue=0.3, p=1.0),
            A.RandomBrightnessContrast(brightness_limit=0.4, contrast_limit=0.4, p=1.0),
            A.GaussNoise(var_limit=(20, 80), p=0.7),
            A.GaussianBlur(blur_limit=(3, 9), p=0.5),
            A.ElasticTransform(alpha=2, sigma=60, alpha_affine=60, p=0.5),
            A.GridDistortion(num_steps=7, distort_limit=0.4, p=0.5),
            A.OpticalDistortion(distort_limit=0.3, shift_limit=0.3, p=0.3),
        ])

    def get_augmentation_strategy(self, mask):
        """根据掩码中的类别选择增强策略"""
        unique_classes = np.unique(mask)

        # 检查是否包含零IoU类别
        zero_iou_classes = [cls for cls in unique_classes
                           if cls in self.class_performance and self.class_performance[cls] == 0.0]

        if zero_iou_classes:
            return 'extreme'

        # 检查是否包含困难类别
        difficult_present = any(cls in self.difficult_classes for cls in unique_classes)

        if difficult_present:
            return 'difficult'

        return 'base'

    def apply_augmentation(self, image, mask):
        """应用智能增强"""
        strategy = self.get_augmentation_strategy(mask)

        if strategy == 'extreme':
            augmented = self.extreme_aug(image=image, mask=mask)
        elif strategy == 'difficult':
            augmented = self.difficult_aug(image=image, mask=mask)
        else:
            augmented = self.base_aug(image=image, mask=mask)

        return augmented['image'], augmented['mask']


class ProgressiveAugmentation:
    """渐进式数据增强 - 随着训练进行逐渐增强"""

    def __init__(self, total_epochs=200):
        self.total_epochs = total_epochs
        self.current_epoch = 0

    def set_epoch(self, epoch):
        """设置当前轮次"""
        self.current_epoch = epoch

    def get_augmentation_strength(self):
        """根据训练进度获取增强强度"""
        progress = self.current_epoch / self.total_epochs

        if progress < 0.3:
            return 'mild'
        elif progress < 0.7:
            return 'moderate'
        else:
            return 'strong'

    def get_augmentation(self):
        """获取当前轮次的增强策略"""
        strength = self.get_augmentation_strength()

        if strength == 'mild':
            return A.Compose([
                A.HorizontalFlip(p=0.3),
                A.ColorJitter(brightness=0.1, contrast=0.1, p=0.3),
            ])
        elif strength == 'moderate':
            return A.Compose([
                A.HorizontalFlip(p=0.5),
                A.RandomRotate90(p=0.3),
                A.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, p=0.5),
                A.GaussNoise(var_limit=(5, 25), p=0.2),
            ])
        else:  # strong
            return A.Compose([
                A.HorizontalFlip(p=0.7),
                A.VerticalFlip(p=0.3),
                A.RandomRotate90(p=0.5),
                A.Rotate(limit=20, p=0.5),
                A.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.1, p=0.7),
                A.GaussNoise(var_limit=(10, 40), p=0.3),
                A.GaussianBlur(blur_limit=(3, 5), p=0.2),
                A.ElasticTransform(alpha=0.5, sigma=25, alpha_affine=25, p=0.2),
            ])
