#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import os
import glob

# 获取最新模型信息
best_models = glob.glob('best_enhanced_model_miou_*.pth')
if best_models:
    latest_model = max(best_models, key=lambda x: float(x.split('_')[-1].replace('.pth', '')))
    print(f'最新模型: {latest_model}')
    
    # 加载模型检查点
    checkpoint = torch.load(latest_model, map_location='cpu', weights_only=False)
    
    print(f'训练轮数: {checkpoint.get("epoch", "未知")}')
    print(f'最佳mIoU: {checkpoint.get("best_miou", "未知")}')
    print(f'训练损失: {checkpoint.get("train_loss", "未知")}')
    print(f'验证损失: {checkpoint.get("val_loss", "未知")}')
    
    # 模型参数统计
    if 'model_state_dict' in checkpoint:
        model_state = checkpoint['model_state_dict']
        total_params = sum(p.numel() for p in model_state.values() if isinstance(p, torch.Tensor))
        print(f'总参数量: {total_params:,}')
        print(f'模型大小: {total_params * 4 / (1024*1024):.1f} MB')
        
        # 分析各层参数分布
        layer_info = {}
        for name, param in model_state.items():
            if isinstance(param, torch.Tensor):
                layer_type = name.split('.')[0] if '.' in name else name
                if layer_type not in layer_info:
                    layer_info[layer_type] = {'params': 0, 'layers': 0}
                layer_info[layer_type]['params'] += param.numel()
                layer_info[layer_type]['layers'] += 1
        
        print('\n各层参数分布:')
        for layer_type, info in sorted(layer_info.items(), key=lambda x: x[1]['params'], reverse=True):
            percentage = info['params'] / total_params * 100
            print(f'  {layer_type:20s}: {info["params"]:>10,} 参数 ({percentage:5.1f}%) | {info["layers"]:3d} 层')
else:
    print('未找到模型文件')
