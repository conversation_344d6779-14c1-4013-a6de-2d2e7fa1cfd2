#!/usr/bin/env python3
"""
自动超参数调优脚本
使用Optuna进行自动超参数搜索
"""

import os
import sys
import yaml
import torch
import optuna
import logging
from datetime import datetime
import json
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train_advanced import main as train_main

class HyperparameterTuner:
    def __init__(self, base_config_path, study_name, storage_url=None):
        """
        初始化超参数调优器
        
        Args:
            base_config_path: 基础配置文件路径
            study_name: 研究名称
            storage_url: 存储URL（用于分布式调优）
        """
        self.base_config_path = base_config_path
        self.study_name = study_name
        self.storage_url = storage_url
        
        # 加载基础配置
        with open(base_config_path, 'r', encoding='utf-8') as f:
            self.base_config = yaml.safe_load(f)
        
        # 创建研究
        if storage_url:
            self.study = optuna.create_study(
                study_name=study_name,
                storage=storage_url,
                direction='maximize',
                load_if_exists=True
            )
        else:
            self.study = optuna.create_study(
                study_name=study_name,
                direction='maximize'
            )
        
        print(f"创建研究: {study_name}")
    
    def suggest_hyperparameters(self, trial):
        """建议超参数"""
        # 学习率相关
        init_lr = trial.suggest_float('init_lr', 1e-5, 1e-2, log=True)
        weight_decay = trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True)
        
        # 优化器类型
        optimizer_type = trial.suggest_categorical('optimizer_type', ['adamw', 'adam', 'sgd'])
        
        # 学习率调度器
        scheduler_type = trial.suggest_categorical('scheduler_type', ['cosine', 'cosine_warmup', 'reduce_on_plateau'])
        
        # 批次大小
        freeze_batch_size = trial.suggest_categorical('freeze_batch_size', [4, 6, 8, 12, 16])
        unfreeze_batch_size = trial.suggest_categorical('unfreeze_batch_size', [2, 4, 6, 8])
        
        # 损失函数权重
        weight_ce = trial.suggest_float('weight_ce', 0.1, 1.0)
        weight_dice = trial.suggest_float('weight_dice', 0.5, 2.0)
        weight_focal = trial.suggest_float('weight_focal', 0.0, 1.5)
        weight_lovasz = trial.suggest_float('weight_lovasz', 0.5, 2.5)
        
        # Dropout率
        dropout_rate = trial.suggest_float('dropout_rate', 0.1, 0.5)
        
        # 数据增强强度
        augmentation_strength = trial.suggest_categorical('augmentation_strength', ['light', 'medium', 'strong'])
        
        # 标签平滑
        label_smoothing = trial.suggest_float('label_smoothing', 0.0, 0.2)
        
        # Focal Loss参数
        focal_gamma = trial.suggest_float('focal_gamma', 1.0, 4.0)
        focal_alpha = trial.suggest_float('focal_alpha', 0.25, 0.75)
        
        return {
            'init_lr': init_lr,
            'weight_decay': weight_decay,
            'optimizer_type': optimizer_type,
            'scheduler_type': scheduler_type,
            'freeze_batch_size': freeze_batch_size,
            'unfreeze_batch_size': unfreeze_batch_size,
            'weight_ce': weight_ce,
            'weight_dice': weight_dice,
            'weight_focal': weight_focal,
            'weight_lovasz': weight_lovasz,
            'dropout_rate': dropout_rate,
            'augmentation_strength': augmentation_strength,
            'label_smoothing': label_smoothing,
            'focal_gamma': focal_gamma,
            'focal_alpha': focal_alpha
        }
    
    def create_trial_config(self, trial):
        """创建试验配置"""
        # 获取建议的超参数
        hyperparams = self.suggest_hyperparameters(trial)
        
        # 复制基础配置
        trial_config = self.base_config.copy()
        
        # 更新超参数
        trial_config['train']['init_lr'] = hyperparams['init_lr']
        trial_config['train']['weight_decay'] = hyperparams['weight_decay']
        trial_config['train']['freeze_batch_size'] = hyperparams['freeze_batch_size']
        trial_config['train']['unfreeze_batch_size'] = hyperparams['unfreeze_batch_size']
        
        trial_config['optimizer']['type'] = hyperparams['optimizer_type']
        trial_config['scheduler']['type'] = hyperparams['scheduler_type']
        
        trial_config['loss']['weight_ce'] = hyperparams['weight_ce']
        trial_config['loss']['weight_dice'] = hyperparams['weight_dice']
        trial_config['loss']['weight_focal'] = hyperparams['weight_focal']
        trial_config['loss']['weight_lovasz'] = hyperparams['weight_lovasz']
        trial_config['loss']['label_smoothing'] = hyperparams['label_smoothing']
        trial_config['loss']['focal_gamma'] = hyperparams['focal_gamma']
        trial_config['loss']['focal_alpha'] = hyperparams['focal_alpha']
        
        trial_config['model']['dropout_rate'] = hyperparams['dropout_rate']
        
        # 根据增强强度调整数据增强
        if hyperparams['augmentation_strength'] == 'light':
            trial_config['data']['augmentation']['random_resize_crop']['p'] = 0.5
            trial_config['data']['augmentation']['color_jitter']['p'] = 0.3
        elif hyperparams['augmentation_strength'] == 'medium':
            trial_config['data']['augmentation']['random_resize_crop']['p'] = 0.7
            trial_config['data']['augmentation']['color_jitter']['p'] = 0.5
        else:  # strong
            trial_config['data']['augmentation']['random_resize_crop']['p'] = 0.9
            trial_config['data']['augmentation']['color_jitter']['p'] = 0.8
        
        # 减少训练轮数以加快调优
        trial_config['train']['total_epochs'] = 50
        
        return trial_config
    
    def objective(self, trial):
        """目标函数"""
        try:
            # 创建试验配置
            trial_config = self.create_trial_config(trial)
            
            # 创建试验目录
            trial_dir = f"hyperparameter_tuning/{self.study_name}/trial_{trial.number}"
            os.makedirs(trial_dir, exist_ok=True)
            
            # 保存试验配置
            config_path = os.path.join(trial_dir, 'config.yaml')
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(trial_config, f, default_flow_style=False)
            
            # 更新保存目录
            trial_config['train']['save_dir'] = trial_dir
            
            # 运行训练
            # 这里需要修改train_main函数以接受配置字典而不是文件路径
            # 或者创建一个包装函数
            final_miou = self.run_training_trial(trial_config, trial_dir)
            
            # 记录试验结果
            trial_result = {
                'trial_number': trial.number,
                'hyperparameters': self.suggest_hyperparameters(trial),
                'final_miou': final_miou,
                'status': 'completed'
            }
            
            with open(os.path.join(trial_dir, 'trial_result.json'), 'w') as f:
                json.dump(trial_result, f, indent=2)
            
            return final_miou
            
        except Exception as e:
            print(f"试验 {trial.number} 失败: {str(e)}")
            
            # 记录失败的试验
            trial_result = {
                'trial_number': trial.number,
                'hyperparameters': self.suggest_hyperparameters(trial),
                'error': str(e),
                'status': 'failed'
            }
            
            trial_dir = f"hyperparameter_tuning/{self.study_name}/trial_{trial.number}"
            os.makedirs(trial_dir, exist_ok=True)
            with open(os.path.join(trial_dir, 'trial_result.json'), 'w') as f:
                json.dump(trial_result, f, indent=2)
            
            # 返回一个很低的分数
            return 0.0
    
    def run_training_trial(self, config, trial_dir):
        """运行训练试验"""
        # 这里需要实现实际的训练逻辑
        # 可以调用现有的训练函数或者重新实现
        
        # 简化版本：直接返回一个模拟的mIoU值
        # 在实际使用中，这里应该运行完整的训练过程
        
        # 模拟训练过程
        import time
        import random
        
        print(f"开始试验训练，保存目录: {trial_dir}")
        
        # 模拟训练时间
        time.sleep(10)  # 实际训练会需要更长时间
        
        # 模拟mIoU结果（基于超参数的简单启发式）
        lr = config['train']['init_lr']
        batch_size = config['train']['freeze_batch_size']
        dropout = config['model']['dropout_rate']
        
        # 简单的启发式评分
        score = 0.5 + 0.3 * (1 - abs(lr - 0.001) / 0.001) + 0.1 * (batch_size / 16) + 0.1 * (1 - dropout)
        score += random.uniform(-0.1, 0.1)  # 添加一些随机性
        score = max(0.0, min(1.0, score))  # 限制在[0, 1]范围内
        
        print(f"试验完成，mIoU: {score:.4f}")
        
        return score
    
    def optimize(self, n_trials=100, timeout=None):
        """开始优化"""
        print(f"开始超参数优化，试验次数: {n_trials}")
        
        # 设置日志
        optuna.logging.get_logger("optuna").addHandler(logging.StreamHandler(sys.stdout))
        
        # 开始优化
        self.study.optimize(
            self.objective,
            n_trials=n_trials,
            timeout=timeout,
            show_progress_bar=True
        )
        
        # 输出最佳结果
        print("优化完成!")
        print(f"最佳试验: {self.study.best_trial.number}")
        print(f"最佳分数: {self.study.best_value:.4f}")
        print("最佳超参数:")
        for key, value in self.study.best_params.items():
            print(f"  {key}: {value}")
        
        # 保存最佳配置
        best_config = self.create_trial_config(self.study.best_trial)
        best_config_path = f"hyperparameter_tuning/{self.study_name}/best_config.yaml"
        os.makedirs(os.path.dirname(best_config_path), exist_ok=True)
        
        with open(best_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(best_config, f, default_flow_style=False)
        
        print(f"最佳配置保存至: {best_config_path}")
        
        return self.study.best_params, self.study.best_value
    
    def get_study_statistics(self):
        """获取研究统计信息"""
        trials = self.study.trials
        completed_trials = [t for t in trials if t.state == optuna.trial.TrialState.COMPLETE]
        
        if not completed_trials:
            return None
        
        values = [t.value for t in completed_trials]
        
        stats = {
            'total_trials': len(trials),
            'completed_trials': len(completed_trials),
            'best_value': max(values),
            'worst_value': min(values),
            'mean_value': sum(values) / len(values),
            'std_value': (sum((v - sum(values) / len(values))**2 for v in values) / len(values))**0.5
        }
        
        return stats

def main():
    parser = argparse.ArgumentParser(description='自动超参数调优')
    parser.add_argument('--config', type=str, required=True, help='基础配置文件路径')
    parser.add_argument('--study_name', type=str, required=True, help='研究名称')
    parser.add_argument('--n_trials', type=int, default=100, help='试验次数')
    parser.add_argument('--timeout', type=int, default=None, help='超时时间（秒）')
    parser.add_argument('--storage', type=str, default=None, help='存储URL')
    args = parser.parse_args()
    
    # 创建调优器
    tuner = HyperparameterTuner(args.config, args.study_name, args.storage)
    
    # 开始优化
    best_params, best_value = tuner.optimize(args.n_trials, args.timeout)
    
    # 输出统计信息
    stats = tuner.get_study_statistics()
    if stats:
        print("\n研究统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")

if __name__ == '__main__':
    main()
