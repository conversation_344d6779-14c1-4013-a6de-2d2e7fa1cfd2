# 优化配置文件 - 基于深度分析的改进方案
# 解决类别不平衡、提升小目标识别、优化训练策略

# 数据配置
data:
  root_dir: "VOCdevkit"
  train_list: "VOC2025/ImageSets/Segmentation/train.txt"
  val_list: "VOC2025/ImageSets/Segmentation/val.txt"
  num_classes: 29
  input_size: [512, 512]

# 模型配置
model:
  backbone: "resnet50"
  pretrained: true
  dropout_rate: 0.3
  use_attention: true
  attention_type: "cbam"
  
  # 优化：启用迁移学习
  transfer_learning: true
  finetune_mode: "progressive"
  use_label_alignment: false

# 训练配置
train:
  total_epochs: 150
  freeze_epochs: 0
  freeze_batch_size: 8
  unfreeze_batch_size: 4
  
  # 优化：分层学习率策略
  init_lr: 0.001
  encoder_lr_scale: 0.2
  
  weight_decay: 0.0001
  
  # 硬件配置
  use_cuda: true
  mixed_precision: true
  benchmark_cudnn: true
  use_channels_last: true
  
  num_workers: 4
  eval_period: 1
  verbose: true
  save_dir: "logs"
  early_stopping: 20

# 学习率调度器配置
scheduler:
  type: "cosine_warmup"
  warmup_epochs: 10
  max_lr: 0.001
  min_lr: 0.000001
  cycles: 2

# 优化：损失函数配置
loss:
  use_ce: true
  use_dice: true
  use_focal: true
  use_lovasz: true
  use_class_weights: true
  class_weight_method: "log_smooth"
  
  # 调整权重比例
  weight_ce: 0.4
  weight_dice: 1.0
  weight_focal: 1.2
  weight_lovasz: 1.4
  
  # 优化Focal Loss参数
  focal_gamma: 2.0
  focal_alpha: 0.25
  
  label_smoothing: 0.05
  ignore_index: 255

# 优化器配置
optimizer:
  type: "adamw"
  beta1: 0.9
  beta2: 0.999
