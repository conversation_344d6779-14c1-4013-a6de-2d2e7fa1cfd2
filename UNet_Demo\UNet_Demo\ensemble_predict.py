#!/usr/bin/env python3
"""
模型集成预测脚本
使用多个训练好的模型进行集成预测，提高准确性
"""

import os
import sys
import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import argparse
import yaml
from tqdm import tqdm
import cv2

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.deeplabv3plus import unet
from utils.utils import cvtColor, resize_image, preprocess_input

class EnsemblePredictor:
    def __init__(self, model_configs, device='cuda'):
        """
        初始化集成预测器
        
        Args:
            model_configs: 模型配置列表，每个配置包含模型路径和权重
            device: 设备类型
        """
        self.device = device
        self.models = []
        self.weights = []
        
        # 加载所有模型
        for config in model_configs:
            model = self.load_model(config['model_path'], config['config'])
            model.eval()
            self.models.append(model)
            self.weights.append(config.get('weight', 1.0))
        
        # 归一化权重
        total_weight = sum(self.weights)
        self.weights = [w / total_weight for w in self.weights]
        
        print(f"加载了 {len(self.models)} 个模型进行集成")
        print(f"模型权重: {self.weights}")
    
    def load_model(self, model_path, config):
        """加载单个模型"""
        # 创建模型
        model = unet(
            num_classes=config['num_classes'],
            backbone=config['backbone'],
            pretrained=False,
            dropout_rate=config.get('dropout_rate', 0.2),
            use_attention=config.get('use_attention', True),
            attention_type=config.get('attention_type', 'cbam')
        )
        
        # 加载权重
        if torch.cuda.is_available():
            model_dict = torch.load(model_path, map_location=self.device)
        else:
            model_dict = torch.load(model_path, map_location='cpu')
        
        # 处理不同的保存格式
        if 'model_state_dict' in model_dict:
            model.load_state_dict(model_dict['model_state_dict'])
        else:
            model.load_state_dict(model_dict)
        
        model = model.to(self.device)
        return model
    
    def predict_single_image(self, image_path, input_shape=(512, 512)):
        """预测单张图像"""
        # 读取图像
        image = Image.open(image_path)
        
        # 预处理
        image = cvtColor(image)
        orininal_h = np.array(image).shape[0]
        orininal_w = np.array(image).shape[1]
        
        image_data, nw, nh = resize_image(image, input_shape)
        image_data = np.expand_dims(np.transpose(preprocess_input(np.array(image_data, np.float32)), (2, 0, 1)), 0)
        
        # 转换为tensor
        images = torch.from_numpy(image_data).to(self.device)
        
        # 集成预测
        ensemble_output = None
        
        with torch.no_grad():
            for i, model in enumerate(self.models):
                output = model(images)[0]
                output = F.softmax(output.permute(1, 2, 0), dim=-1)
                
                if ensemble_output is None:
                    ensemble_output = output * self.weights[i]
                else:
                    ensemble_output += output * self.weights[i]
        
        # 后处理
        pr = ensemble_output.cpu().numpy()
        
        # 裁剪灰条
        pr = pr[int((input_shape[0] - nh) // 2) : int((input_shape[0] - nh) // 2 + nh), \
                int((input_shape[1] - nw) // 2) : int((input_shape[1] - nw) // 2 + nw)]
        
        # 调整到原始尺寸
        pr = cv2.resize(pr, (orininal_w, orininal_h), interpolation=cv2.INTER_LINEAR)
        
        return pr
    
    def predict_with_tta(self, image_path, input_shape=(512, 512)):
        """使用测试时增强(TTA)进行预测"""
        # 读取图像
        image = Image.open(image_path)
        image = cvtColor(image)
        orininal_h = np.array(image).shape[0]
        orininal_w = np.array(image).shape[1]
        
        # 不同的增强方式
        augmentations = [
            lambda x: x,  # 原图
            lambda x: x.transpose(Image.FLIP_LEFT_RIGHT),  # 水平翻转
            lambda x: x.transpose(Image.FLIP_TOP_BOTTOM),   # 垂直翻转
            lambda x: x.transpose(Image.FLIP_LEFT_RIGHT).transpose(Image.FLIP_TOP_BOTTOM),  # 水平+垂直翻转
        ]
        
        # 对应的逆变换
        inverse_augmentations = [
            lambda x: x,  # 原图
            lambda x: np.fliplr(x),  # 水平翻转
            lambda x: np.flipud(x),   # 垂直翻转
            lambda x: np.flipud(np.fliplr(x)),  # 水平+垂直翻转
        ]
        
        ensemble_predictions = []
        
        for aug, inv_aug in zip(augmentations, inverse_augmentations):
            # 应用增强
            aug_image = aug(image)
            
            # 预处理
            image_data, nw, nh = resize_image(aug_image, input_shape)
            image_data = np.expand_dims(np.transpose(preprocess_input(np.array(image_data, np.float32)), (2, 0, 1)), 0)
            images = torch.from_numpy(image_data).to(self.device)
            
            # 集成预测
            ensemble_output = None
            
            with torch.no_grad():
                for i, model in enumerate(self.models):
                    output = model(images)[0]
                    output = F.softmax(output.permute(1, 2, 0), dim=-1)
                    
                    if ensemble_output is None:
                        ensemble_output = output * self.weights[i]
                    else:
                        ensemble_output += output * self.weights[i]
            
            # 后处理
            pr = ensemble_output.cpu().numpy()
            
            # 裁剪灰条
            pr = pr[int((input_shape[0] - nh) // 2) : int((input_shape[0] - nh) // 2 + nh), \
                    int((input_shape[1] - nw) // 2) : int((input_shape[1] - nw) // 2 + nw)]
            
            # 调整到原始尺寸
            pr = cv2.resize(pr, (orininal_w, orininal_h), interpolation=cv2.INTER_LINEAR)
            
            # 应用逆变换
            pr = inv_aug(pr)
            
            ensemble_predictions.append(pr)
        
        # 平均所有TTA预测
        final_prediction = np.mean(ensemble_predictions, axis=0)
        
        return final_prediction
    
    def predict_batch(self, image_dir, output_dir, use_tta=False, input_shape=(512, 512)):
        """批量预测"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取所有图像文件
        image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        for image_file in tqdm(image_files, desc="预测中"):
            image_path = os.path.join(image_dir, image_file)
            
            # 预测
            if use_tta:
                prediction = self.predict_with_tta(image_path, input_shape)
            else:
                prediction = self.predict_single_image(image_path, input_shape)
            
            # 获取类别预测
            seg_img = np.argmax(prediction, axis=-1)
            
            # 保存结果
            output_path = os.path.join(output_dir, f"pred_{image_file}")
            Image.fromarray(seg_img.astype(np.uint8)).save(output_path)
            
            # 保存概率图（可选）
            prob_output_path = os.path.join(output_dir, f"prob_{image_file.replace('.jpg', '.npy').replace('.png', '.npy')}")
            np.save(prob_output_path, prediction)

def main():
    parser = argparse.ArgumentParser(description='模型集成预测')
    parser.add_argument('--config', type=str, required=True, help='集成配置文件')
    parser.add_argument('--input', type=str, required=True, help='输入图像路径或目录')
    parser.add_argument('--output', type=str, required=True, help='输出目录')
    parser.add_argument('--tta', action='store_true', help='使用测试时增强')
    parser.add_argument('--device', type=str, default='cuda', help='设备类型')
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建集成预测器
    predictor = EnsemblePredictor(config['models'], args.device)
    
    # 预测
    if os.path.isfile(args.input):
        # 单张图像预测
        if args.tta:
            prediction = predictor.predict_with_tta(args.input)
        else:
            prediction = predictor.predict_single_image(args.input)
        
        # 保存结果
        seg_img = np.argmax(prediction, axis=-1)
        os.makedirs(args.output, exist_ok=True)
        output_path = os.path.join(args.output, "prediction.png")
        Image.fromarray(seg_img.astype(np.uint8)).save(output_path)
        print(f"预测结果保存至: {output_path}")
    
    elif os.path.isdir(args.input):
        # 批量预测
        predictor.predict_batch(args.input, args.output, args.tta)
        print(f"批量预测完成，结果保存至: {args.output}")
    
    else:
        print(f"输入路径不存在: {args.input}")

if __name__ == '__main__':
    main()
