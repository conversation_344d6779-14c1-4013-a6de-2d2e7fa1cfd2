# 学习率策略优化

## 优化概述

本文档详细说明了对农村景观语义分割模型的学习率策略优化。学习率是深度学习中最重要的超参数之一，直接影响模型的收敛速度和最终性能。我们实施了以下优化策略：

1. **学习率预热策略**
2. **增加初始学习率**
3. **实现余弦退火学习率调度**
4. **分层学习率优化**
5. **加速渐进式解冻**

## 详细优化说明

### 1. 学习率预热策略

学习率预热是一种在训练初期使用较小学习率，然后逐渐增加到目标学习率的技术。这有助于模型在初始阶段稳定训练，避免由于大学习率导致的不稳定性。

**优化前**：
- 没有明确的预热策略
- 直接使用固定学习率开始训练

**优化后**：
- 添加了5个轮次的预热期
- 学习率从初始值的1/10逐渐增加到全值
- 配置参数：`warmup_epochs: 5`

### 2. 增加初始学习率

适当增加学习率可以加快模型收敛速度，特别是在训练初期。

**优化前**：
- 基础学习率：0.002
- 编码器学习率：5e-4
- 解码器学习率：1e-3
- 分割头学习率：2e-3

**优化后**：
- 基础学习率：0.003（增加50%）
- 编码器学习率：1e-3（增加100%）
- 解码器学习率：2e-3（增加100%）
- 分割头学习率：3e-3（增加50%）

### 3. 实现余弦退火学习率调度

余弦退火是一种学习率调度策略，它使学习率按照余弦函数从初始值逐渐降低到最小值，然后可以周期性地重启。这种策略有助于模型跳出局部最小值，找到更好的解。

**优化前**：
- 使用ReduceLROnPlateau调度器
- 仅在验证损失停滞时降低学习率
- 参数：factor=0.5, patience=3, threshold=0.01

**优化后**：
- 使用余弦退火+预热调度器
- 周期性调整学习率，有3个完整周期
- 参数：
  - `type: "cosine_warmup"`
  - `max_lr: 0.003`
  - `cycles: 3`
  - `pct_start: 0.1`
  - `div_factor: 10.0`
  - `final_div_factor: 100.0`

### 4. 分层学习率优化

分层学习率是为网络不同部分设置不同学习率的策略。通常，预训练部分（编码器）使用较小的学习率，而新添加的部分（解码器和分割头）使用较大的学习率。

**优化前**：
- 编码器：5e-4
- 解码器：1e-3
- 分割头：2e-3
- 比例关系：1:2:4

**优化后**：
- 编码器：1e-3
- 解码器：2e-3
- 分割头：3e-3
- 比例关系：1:2:3（更平衡的比例）

### 5. 加速渐进式解冻

渐进式解冻是一种先冻结预训练部分，然后逐步解冻的策略。加速这一过程可以让模型更快地适应新数据。

**优化前**：
```yaml
unfreeze_schedule:
  0: ["segmentation_head", "decoder"]
  3: ["encoder.layer4"]
  6: ["encoder.layer3"]
  9: ["encoder.layer2"]
  12: ["encoder.layer1"]
```

**优化后**：
```yaml
unfreeze_schedule:
  0: ["segmentation_head", "decoder"]
  2: ["encoder.layer4"]
  4: ["encoder.layer3"]
  6: ["encoder.layer2"]
  8: ["encoder.layer1"]
```

这一变化使得完全解冻的时间从第12轮缩短到第8轮，加速了模型对新数据的适应。

## 预期效果

通过实施上述学习率策略优化，我们预期会看到以下效果：

1. **更快的初期收敛**：由于增加了学习率和添加了预热策略，模型在初期应该能够更快地收敛。

2. **更好的最终性能**：余弦退火调度器有助于模型跳出局部最小值，找到更好的全局最小值，从而提高最终性能。

3. **更稳定的训练过程**：学习率预热和余弦退火的组合应该会使训练过程更加稳定，减少损失曲线的波动。

4. **更高的mIoU和F-score**：通过更好的学习率策略，模型应该能够学习到更好的特征表示，从而提高分割性能指标。

5. **更快的特征适应**：加速渐进式解冻使模型能够更快地适应新数据的特征，特别是对于那些与预训练数据集差异较大的类别。

## 如何使用

要使用这一优化策略，只需运行以下命令：

```bash
python train.py --cfg config_optimized.yaml
```

或者直接运行提供的批处理脚本：

```bash
train_optimized.bat
```

## 后续优化方向

在观察这一优化的效果后，我们可以考虑以下后续优化：

1. **进一步调整学习率参数**：根据训练曲线，可能需要进一步调整学习率的最大值、最小值和周期数。

2. **组合其他优化策略**：将学习率优化与其他优化策略（如类别权重优化、数据增强等）结合，可能会产生更好的效果。

3. **自适应学习率策略**：实现更复杂的自适应学习率策略，如根据不同类别的性能动态调整学习率。
