#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from datetime import datetime
import logging

# 添加UNet_Demo路径
sys.path.append('UNet_Demo/UNet_Demo')

try:
    from train import unet
    from utils.utils_fit import fit_one_epoch
    from utils.utils import get_lr
    from utils.dataloader_medical import UnetDataset, unet_dataset_collate_fn
    from utils.utils_metrics import f_score
    from nets.unet_training import CE_Loss, Dice_loss, Focal_Loss
except ImportError as e:
    print(f"导入错误: {e}")
    print("尝试使用备用导入...")
    try:
        # 备用导入
        from train import unet
        from utils.utils_fit import fit_one_epoch
        from utils.utils import get_lr
        from utils.utils_metrics import f_score, compute_mIoU_tensor
        from nets.unet_training import CE_Loss, Dice_loss, Focal_Loss

        # 简化的数据集类
        from torch.utils.data import Dataset
        import cv2

        class SimpleUnetDataset(Dataset):
            def __init__(self, annotation_lines, input_shape, num_classes, train, dataset_path):
                self.annotation_lines = annotation_lines
                self.length = len(annotation_lines)
                self.input_shape = input_shape
                self.num_classes = num_classes
                self.train = train
                self.dataset_path = dataset_path

            def __len__(self):
                return self.length

            def __getitem__(self, index):
                annotation_line = self.annotation_lines[index]
                name = annotation_line.split()[0]

                # 读取图像
                jpg = cv2.imread(os.path.join(os.path.join(self.dataset_path, "VOC2025/JPEGImages"), name + ".jpg"))
                png = cv2.imread(os.path.join(os.path.join(self.dataset_path, "VOC2025/SegmentationClass"), name + ".png"), cv2.IMREAD_GRAYSCALE)

                # 调整大小
                jpg = cv2.resize(jpg, self.input_shape)
                png = cv2.resize(png, self.input_shape, interpolation=cv2.INTER_NEAREST)

                # 转换为tensor
                jpg = torch.from_numpy(jpg.transpose(2, 0, 1)).float() / 255.0
                png = torch.from_numpy(png).long()

                return jpg, png

        UnetDataset = SimpleUnetDataset
        unet_dataset_collate_fn = None

    except ImportError as e2:
        print(f"备用导入也失败: {e2}")
        sys.exit(1)

def setup_logging():
    """设置日志"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f'logs/extreme_simple_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'{log_dir}/training.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    return log_dir, logging.getLogger(__name__)

def create_extreme_weights():
    """创建极端类别权重"""
    class_weights = np.ones(29)

    # 极端权重配置
    extreme_weights = {
        8: 100.0, 10: 90.0, 18: 80.0, 23: 70.0,
        26: 60.0, 27: 50.0, 28: 50.0,
        5: 15.0, 7: 12.0, 9: 10.0, 11: 8.0, 15: 6.0,
        17: 5.0, 19: 20.0, 22: 15.0, 25: 10.0,
        0: 0.01, 3: 0.01, 13: 0.01, 2: 0.05, 21: 0.05,
        1: 0.5, 4: 0.8, 6: 0.6, 12: 0.4, 14: 0.3,
        16: 0.7, 20: 0.6, 24: 0.5
    }

    for cls, weight in extreme_weights.items():
        class_weights[cls] = weight

    print("📊 极端类别权重分布:")
    for i, weight in enumerate(class_weights):
        if weight != 1.0:
            print(f"  类别 {i}: {weight:.2f}x")

    return torch.FloatTensor(class_weights)

def main():
    """主训练函数"""
    print("🚀 启动极端权重平衡训练 (简化版)")
    print("=" * 60)
    print("🎯 核心策略:")
    print("  ✅ 极端类别权重平衡 (50-100倍)")
    print("  ✅ Focal Loss + Dice Loss + CE Loss组合")
    print("  ✅ ResNet50 + 注意力机制")
    print("  ✅ 更高学习率 + 余弦退火")
    print("=" * 60)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 设置日志
    log_dir, logger = setup_logging()

    # 创建模型
    print("🧠 创建模型...")
    model = unet(
        num_classes=29,
        backbone='resnet50',
        pretrained=True,
        dropout_rate=0.3,
        use_attention=True,
        attention_type='cbam'
    )
    model = model.to(device)

    # 创建极端权重
    class_weights = create_extreme_weights()
    if torch.cuda.is_available():
        class_weights = class_weights.cuda()

    # 创建优化器
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=3e-4,
        weight_decay=1e-4
    )

    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=200, eta_min=1e-6
    )

    # 准备数据
    print("📊 准备数据...")
    VOCdevkit_path = "UNet_Demo/UNet_Demo/VOCdevkit"

    with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/train.txt"), "r") as f:
        train_lines = f.readlines()
    with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/val.txt"), "r") as f:
        val_lines = f.readlines()

    print(f"训练集样本数: {len(train_lines)}")
    print(f"验证集样本数: {len(val_lines)}")

    # 创建数据集
    train_dataset = UnetDataset(
        train_lines,
        input_shape=(512, 512),
        num_classes=29,
        train=True,
        dataset_path=VOCdevkit_path
    )

    val_dataset = UnetDataset(
        val_lines,
        input_shape=(512, 512),
        num_classes=29,
        train=False,
        dataset_path=VOCdevkit_path
    )

    # 创建数据加载器
    if unet_dataset_collate_fn:
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=4, shuffle=True, num_workers=0,
            pin_memory=True, drop_last=True, collate_fn=unet_dataset_collate_fn
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset, batch_size=4, shuffle=False, num_workers=0,
            pin_memory=True, drop_last=False, collate_fn=unet_dataset_collate_fn
        )
    else:
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=4, shuffle=True, num_workers=0,
            pin_memory=True, drop_last=True
        )
        val_loader = torch.utils.data.DataLoader(
            val_dataset, batch_size=4, shuffle=False, num_workers=0,
            pin_memory=True, drop_last=False
        )

    # 训练参数
    epochs = 100
    best_miou = 0
    patience = 20
    patience_counter = 0

    print(f"\n🎯 开始训练 {epochs} 轮...")
    logger.info(f"开始极端权重平衡训练，目标: mIoU > 0.45")

    # 训练循环
    for epoch in range(epochs):
        print(f"\nEpoch {epoch+1}/{epochs}")
        print("-" * 40)

        # 训练阶段
        model.train()
        total_loss = 0

        for iteration, batch in enumerate(train_loader):
            if len(batch) == 2:
                images, targets = batch
            else:
                images, targets = batch[0], batch[1]

            images = images.to(device)
            targets = targets.to(device)

            optimizer.zero_grad()

            outputs = model(images)

            # 组合损失 - 简化版，只使用Focal和CE
            focal = Focal_Loss(outputs, targets, class_weights, 29, alpha=0.25, gamma=2.0)
            ce = CE_Loss(outputs, targets, class_weights, 29)

            # 60% Focal + 40% CE，重点关注困难样本
            loss = 0.6 * focal + 0.4 * ce

            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            total_loss += loss.item()

            if iteration % 10 == 0:
                print(f"  Batch {iteration}, Loss: {loss.item():.4f}")

        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        avg_train_loss = total_loss / len(train_loader)

        # 验证阶段
        model.eval()
        val_loss = 0
        val_miou = 0

        with torch.no_grad():
            for batch in val_loader:
                if len(batch) == 2:
                    images, targets = batch
                else:
                    images, targets = batch[0], batch[1]

                images = images.to(device)
                targets = targets.to(device)

                outputs = model(images)

                # 计算损失 - 简化版
                focal = Focal_Loss(outputs, targets, class_weights, 29, alpha=0.25, gamma=2.0)
                ce = CE_Loss(outputs, targets, class_weights, 29)
                loss = 0.6 * focal + 0.4 * ce

                val_loss += loss.item()

                # 计算mIoU
                pred = torch.argmax(outputs, dim=1)
                miou = compute_mIoU_tensor(pred, targets, num_classes=29)
                val_miou += miou

        avg_val_loss = val_loss / len(val_loader)
        avg_val_miou = val_miou / len(val_loader)

        # 记录结果
        print(f"训练损失: {avg_train_loss:.4f}")
        print(f"验证损失: {avg_val_loss:.4f}")
        print(f"验证mIoU: {avg_val_miou:.4f}")
        print(f"学习率: {current_lr:.2e}")

        logger.info(f"Epoch {epoch+1}: train_loss={avg_train_loss:.4f}, val_loss={avg_val_loss:.4f}, val_miou={avg_val_miou:.4f}, lr={current_lr:.2e}")

        # 保存最佳模型
        if avg_val_miou > best_miou:
            best_miou = avg_val_miou
            patience_counter = 0

            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_miou': best_miou,
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss,
            }, f'best_extreme_simple_miou_{avg_val_miou:.4f}.pth')

            print(f"🎉 新的最佳mIoU: {best_miou:.4f}")
            logger.info(f"🎉 新的最佳mIoU: {best_miou:.4f}")

            if best_miou > 0.45:
                print("🎯🎯🎯 恭喜！达到目标 mIoU > 0.45！🎯🎯🎯")
            elif best_miou > 0.40:
                print("🎯 很好！mIoU > 0.40，继续努力！")
        else:
            patience_counter += 1

        # 早停检查
        if patience_counter >= patience:
            print(f"⏹️ 早停触发，最佳mIoU: {best_miou:.4f}")
            break

    print(f"\n🎊 训练完成！")
    print(f"📊 最佳mIoU: {best_miou:.4f}")
    print(f"📁 日志保存在: {log_dir}")

    # 计算改善程度
    baseline_miou = 0.34
    improvement = ((best_miou - baseline_miou) / baseline_miou) * 100
    print(f"📈 相比基线改善: {improvement:.1f}%")

    if best_miou > 0.45:
        print("🏆🏆🏆 恭喜！成功达到目标！🏆🏆🏆")
    elif best_miou > 0.40:
        print("🎯 接近目标，继续优化！")
    else:
        print("💪 有改善，需要进一步优化策略")

if __name__ == "__main__":
    main()
