# 优化配置文件 v2.0 - 基于深度分析的改进方案
# 解决类别不平衡、提升小目标识别、优化训练策略

# 数据配置
data:
  root_dir: "VOCdevkit"
  train_list: "VOC2025/ImageSets/Segmentation/train.txt"
  val_list: "VOC2025/ImageSets/Segmentation/val.txt"
  num_classes: 29
  input_size: [512, 512]

# 模型配置
model:
  backbone: "resnet50"
  pretrained: true
  dropout_rate: 0.3
  use_attention: true
  attention_type: "cbam"

  # 优化：启用迁移学习
  transfer_learning: true
  finetune_mode: "progressive"
  use_label_alignment: false

# 迁移学习配置
transfer_learning:
  use_transfer_learning: true
  finetune_mode: "progressive"
  use_label_alignment: true

  # 优化：更细粒度的渐进式解冻
  unfreeze_schedule:
    0: ["segmentation_head"]
    5: ["decoder"]
    10: ["encoder.layer4"]
    20: ["encoder.layer3"]
    30: ["encoder.layer2"]
    40: ["encoder.layer1"]

  freeze_batch_size: 12
  unfreeze_batch_size: 6

# 训练配置
train:
  total_epochs: 150  # 减少总轮数
  freeze_epochs: 0
  freeze_batch_size: 8
  unfreeze_batch_size: 4

  # 优化：分层学习率策略
  init_lr: 0.001
  encoder_lr_scale: 0.2  # 编码器学习率比例

  weight_decay: 0.0001  # 降低权重衰减

  # 硬件配置
  use_cuda: true
  mixed_precision: true
  benchmark_cudnn: true
  use_channels_last: true

  num_workers: 4
  eval_period: 1
  verbose: true
  save_dir: "logs"
  early_stopping: 20

# 优化：平滑类别权重策略
class_weights:
  use_class_weights: true
  method: "log_smooth"  # 新方法：对数平滑
  min_weight: 0.1
  max_weight: 5.0       # 限制最大权重
  smooth_factor: 1.0

# 学习率调度器配置
scheduler:
  type: "cosine_warmup"
  warmup_epochs: 10
  max_lr: 0.001
  min_lr: 1e-6
  cycles: 2

# 优化：损失函数配置
loss:
  use_ce: true
  use_dice: true
  use_focal: true
  use_lovasz: true
  use_class_weights: true
  class_weight_method: "log_smooth"  # 使用对数平滑权重

  # 调整权重比例
  weight_ce: 0.4        # 降低CE权重
  weight_dice: 1.0      # 降低Dice权重
  weight_focal: 1.2     # 增加Focal权重
  weight_lovasz: 1.4    # 增加Lovász权重

  # 优化Focal Loss参数
  focal_gamma: 2.0      # 降低gamma，减少难易样本差异
  focal_alpha: 0.25     # 平衡正负样本

  label_smoothing: 0.05 # 降低标签平滑
  ignore_index: 255

# 优化器配置
optimizer:
  type: "adamw"
  beta1: 0.9
  beta2: 0.999
