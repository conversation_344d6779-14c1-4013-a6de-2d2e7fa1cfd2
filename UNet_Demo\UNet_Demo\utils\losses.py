import os
import cv2
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from itertools import filterfalse


def compute_class_weights(mask_paths, num_classes, method='median_freq', smoothing=1e-6):
    """
    计算类别权重，支持多种计算方法:

    - 'median_freq': 中值频率平衡
        counts_i = 每个类别的像素总数
        median_count = 中值(counts_i[counts_i>0])
        weight_i = median_count / (counts_i + smoothing)

    - 'effective_samples': 有效样本数平衡
        beta = 0.9999
        effective_num = 1.0 - beta^(counts_i)
        weight_i = (1 - beta) / effective_num

    - 'inverse_freq': 频率倒数平衡
        weight_i = 1 / (counts_i + smoothing)

    Args:
        mask_paths (list[str]): mask 文件路径列表或glob模式
        num_classes (int): 有效类别数，不含 ignore_index
        method (str): 权重计算方法，可选 'median_freq', 'effective_samples', 'inverse_freq'
        smoothing (float): 防止除零的小常数

    Returns:
        torch.Tensor: 大小 (num_classes,) 的权重向量
    """
    # 处理glob模式
    if isinstance(mask_paths, str):
        import glob
        mask_paths = glob.glob(mask_paths)
    elif isinstance(mask_paths, list) and len(mask_paths) == 1 and '*' in mask_paths[0]:
        import glob
        mask_paths = glob.glob(mask_paths[0])

    # 统计每个类别的像素数
    counts = np.zeros(num_classes, dtype=np.float64)
    for p in mask_paths:
        m = cv2.imread(p, cv2.IMREAD_GRAYSCALE)
        if m is None:
            continue
        binc = np.bincount(m.flatten(), minlength=num_classes+1)
        counts += binc[:num_classes]

    # 确保所有类别至少有一个像素
    counts = np.maximum(counts, 1)

    # 根据不同方法计算权重
    if method == 'median_freq':
        # 中值频率平衡
        valid = counts > 0
        if np.any(valid):
            median_count = np.median(counts[valid])
        else:
            median_count = np.mean(counts)
        weights = median_count / (counts + smoothing)

    elif method == 'effective_samples':
        # 有效样本数平衡
        beta = 0.9999
        effective_num = 1.0 - np.power(beta, counts)
        weights = (1.0 - beta) / (effective_num + smoothing)

    elif method == 'inverse_freq':
        # 频率倒数平衡
        weights = 1.0 / (counts + smoothing)

    else:
        raise ValueError(f"不支持的权重计算方法: {method}")

    # 归一化到均值1，便于超参迁移
    weights = weights / np.mean(weights)
    return torch.from_numpy(weights.astype(np.float32))


class DiceLoss(nn.Module):
    """
    Dice Loss for semantic segmentation，支持 ignore_index.
    """
    def __init__(self, smooth=1e-6, ignore_index=None):
        super(DiceLoss, self).__init__()
        self.smooth = smooth
        self.ignore_index = ignore_index

    def forward(self, inputs, targets):
        # inputs: (B,C,H,W) logits
        # targets: (B,H,W) long
        probs = F.softmax(inputs, dim=1)
        B, C, H, W = inputs.shape

        # 检查目标是否包含无效值
        if torch.max(targets) >= C and self.ignore_index is None:
            print(f"警告: 目标包含超出类别数的值: max={torch.max(targets).item()}, C={C}")
            # 将超出范围的值设为忽略索引
            targets = torch.clamp(targets, 0, C-1)

        # one-hot encode targets
        try:
            targets_one_hot = F.one_hot(targets, num_classes=C).permute(0,3,1,2).float()
        except Exception as e:
            print(f"警告: one-hot编码失败: {e}, targets.shape={targets.shape}, targets.min={targets.min().item()}, targets.max={targets.max().item()}, C={C}")
            # 安全处理
            targets = torch.clamp(targets, 0, C-1)
            targets_one_hot = F.one_hot(targets, num_classes=C).permute(0,3,1,2).float()

        if self.ignore_index is not None:
            valid = (targets != self.ignore_index).unsqueeze(1)
            probs = probs * valid.float()
            targets_one_hot = targets_one_hot * valid.float()

        # 计算交集和并集
        intersection = torch.sum(probs * targets_one_hot, dim=(0,2,3))
        cardinality = torch.sum(probs + targets_one_hot, dim=(0,2,3))

        # 确保分母不为零
        denominator = cardinality + self.smooth

        # 计算Dice分数
        dice_score = (2. * intersection + self.smooth) / denominator

        # 检查Dice分数是否有效
        if torch.isnan(dice_score).any() or torch.isinf(dice_score).any():
            print(f"警告: Dice分数包含NaN或Inf值")
            # 替换无效值
            dice_score = torch.nan_to_num(dice_score, nan=0.5, posinf=0.5, neginf=0.5)

        # 返回Dice损失
        return 1.0 - dice_score.mean()


class FocalLoss(nn.Module):
    """
    Focal Loss for semantic segmentation，支持 ignore_index, 多类别.
    alpha: per-class 平衡系数（长度 C 向量或标量）
    gamma: 聚焦参数
    """
    def __init__(self, gamma=2.0, alpha=None, ignore_index=None):
        super(FocalLoss, self).__init__()
        self.gamma = gamma
        self.alpha = alpha
        self.ignore_index = ignore_index

    def forward(self, inputs, targets):
        # inputs: (B,C,H,W) logits
        # targets: (B,H,W) long

        # 检查输入和目标是否有效
        if torch.isnan(inputs).any() or torch.isinf(inputs).any():
            print("警告: Focal Loss输入包含NaN或Inf值")
            inputs = torch.nan_to_num(inputs, nan=0.0, posinf=1e5, neginf=-1e5)

        # 计算交叉熵损失
        try:
            logpt = -F.cross_entropy(
                inputs, targets, reduction='none', ignore_index=self.ignore_index
            )
        except Exception as e:
            print(f"警告: Focal Loss计算交叉熵失败: {e}")
            # 返回一个默认损失值
            return torch.tensor(0.1, device=inputs.device)

        # 计算概率
        pt = torch.exp(torch.clamp(logpt, -100, 100))  # 限制指数范围，防止溢出

        # 应用alpha权重
        if self.alpha is not None:
            # 将 alpha 扩展到与 pt 相同 shape
            at = self.alpha.view(1, -1, 1, 1) if torch.is_tensor(self.alpha) else self.alpha
            logpt = logpt * at

        # 计算Focal Loss
        focal_weight = (1 - pt) ** self.gamma
        # 限制权重范围，防止数值不稳定
        focal_weight = torch.clamp(focal_weight, 0.0, 100.0)

        loss = -focal_weight * logpt

        # 处理忽略索引
        if self.ignore_index is not None:
            valid = targets != self.ignore_index
            loss = loss * valid.float()
            valid_sum = valid.sum().clamp(min=1.0)
            return loss.sum() / valid_sum

        # 检查最终损失是否有效
        if torch.isnan(loss).any() or torch.isinf(loss).any():
            print("警告: Focal Loss结果包含NaN或Inf值")
            # 返回一个默认损失值
            return torch.tensor(0.1, device=inputs.device)

        return loss.mean()

class CombinedLoss(nn.Module):
    def __init__(self, cfg_loss: dict, cls_weights: torch.Tensor = None):
        super().__init__()
        self.use_ce     = cfg_loss.get('use_ce', True)
        self.use_dice   = cfg_loss.get('use_dice', True)
        self.use_focal  = cfg_loss.get('use_focal', False)  # 默认禁用Focal Loss
        self.use_lovasz = cfg_loss.get('use_lovasz', True)  # 默认启用Lovász损失
        self.use_tversky = cfg_loss.get('use_tversky', False)  # 新增Tversky损失

        self.w_ce     = float(cfg_loss.get('weight_ce', 1.0))
        self.w_dice   = float(cfg_loss.get('weight_dice', 1.0))
        self.w_focal  = float(cfg_loss.get('weight_focal', 0.0))
        self.w_lovasz = float(cfg_loss.get('weight_lovasz', 1.0))  # Lovász权重
        self.w_tversky = float(cfg_loss.get('weight_tversky', 0.0))  # Tversky权重

        self.gamma   = float(cfg_loss.get('focal_gamma', 2.0))
        self.alpha   = cfg_loss.get('focal_alpha', None)
        # 确保ignore_index是整数
        ignore_index = cfg_loss.get('ignore_index', -1)
        if ignore_index is None or ignore_index == -1:
            ignore_index = 255  # 默认值
        self.ignore_index = int(ignore_index)

        # 打印损失配置
        print(f"损失函数配置: CE={self.use_ce}({self.w_ce}), Dice={self.use_dice}({self.w_dice}), "
              f"Focal={self.use_focal}({self.w_focal}), Lovász={self.use_lovasz}({self.w_lovasz})")

        # 如果类别权重存在，打印类别权重信息
        if cls_weights is not None:
            print(f"类别权重: min={cls_weights.min().item():.4f}, max={cls_weights.max().item():.4f}, "
                  f"mean={cls_weights.mean().item():.4f}, std={cls_weights.std().item():.4f}")
            # 打印每个类别的权重
            for i, w in enumerate(cls_weights):
                print(f"  类别 {i}: 权重 = {w.item():.4f}")

        # 子损失
        if self.use_ce:
            # 添加标签平滑
            label_smoothing = float(cfg_loss.get('label_smoothing', 0.05))
            self.ce = nn.CrossEntropyLoss(
                weight=cls_weights,
                ignore_index=self.ignore_index,
                label_smoothing=label_smoothing  # 使用标签平滑
            )
        if self.use_dice:
            self.dice = DiceLoss(ignore_index=self.ignore_index)
        if self.use_focal:
            self.focal = FocalLoss(gamma=self.gamma,
                                   alpha=self.alpha,
                                   ignore_index=self.ignore_index)
        if self.use_lovasz:
            self.lovasz = LovaszLoss(classes='present',
                                     ignore_index=self.ignore_index)
        if self.use_tversky:
            # Tversky损失参数
            tversky_alpha = float(cfg_loss.get('tversky_alpha', 0.3))
            tversky_beta = float(cfg_loss.get('tversky_beta', 0.7))
            self.tversky = TverskyLoss(alpha=tversky_alpha, beta=tversky_beta,
                                     ignore_index=self.ignore_index)

    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        loss = 0.0
        loss_components = {}

        # 检查输入是否包含NaN或Inf
        if torch.isnan(inputs).any() or torch.isinf(inputs).any():
            print("警告: 输入包含NaN或Inf值")
            # 替换NaN和Inf值
            inputs = torch.nan_to_num(inputs, nan=0.0, posinf=1e5, neginf=-1e5)

        if self.use_ce:
            ce_loss = self.ce(inputs, targets)
            # 检查损失值是否合理
            if torch.isnan(ce_loss) or torch.isinf(ce_loss) or ce_loss < 0:
                print(f"警告: CE损失异常: {ce_loss.item()}")
                ce_loss = torch.tensor(0.1, device=inputs.device)
            loss_components['ce'] = ce_loss.item()
            loss = loss + self.w_ce * ce_loss

        if self.use_dice:
            dice_loss = self.dice(inputs, targets)
            # 检查损失值是否合理
            if torch.isnan(dice_loss) or torch.isinf(dice_loss) or dice_loss < 0:
                print(f"警告: Dice损失异常: {dice_loss.item()}")
                dice_loss = torch.tensor(0.1, device=inputs.device)
            loss_components['dice'] = dice_loss.item()
            loss = loss + self.w_dice * dice_loss

        if self.use_focal:
            focal_loss = self.focal(inputs, targets)
            # 检查损失值是否合理
            if torch.isnan(focal_loss) or torch.isinf(focal_loss) or focal_loss < 0:
                print(f"警告: Focal损失异常: {focal_loss.item()}")
                focal_loss = torch.tensor(0.1, device=inputs.device)
            loss_components['focal'] = focal_loss.item()
            loss = loss + self.w_focal * focal_loss

        if self.use_lovasz:
            lovasz_loss = self.lovasz(inputs, targets)
            # 检查损失值是否合理
            if torch.isnan(lovasz_loss) or torch.isinf(lovasz_loss) or lovasz_loss < 0:
                print(f"警告: Lovász损失异常: {lovasz_loss.item()}")
                lovasz_loss = torch.tensor(0.1, device=inputs.device)
            loss_components['lovasz'] = lovasz_loss.item()
            loss = loss + self.w_lovasz * lovasz_loss

        if self.use_tversky:
            tversky_loss = self.tversky(inputs, targets)
            if torch.isnan(tversky_loss) or torch.isinf(tversky_loss) or tversky_loss < 0:
                print(f"警告: Tversky损失异常: {tversky_loss.item()}")
                tversky_loss = torch.tensor(0.1, device=inputs.device)
            loss_components['tversky'] = tversky_loss.item()
            loss = loss + self.w_tversky * tversky_loss

        # 打印损失组件
        if len(loss_components) > 0:
            components_str = ", ".join([f"{k}={v:.4f}" for k, v in loss_components.items()])
            print(f"损失组件: {components_str}, 总损失={loss.item():.4f}")

        # 每100次迭代打印一次各损失组件的值
        if torch.rand(1).item() < 0.01:  # 1%的概率打印
            components_str = ", ".join([f"{k}={v:.4f}" for k, v in loss_components.items()])
            print(f"损失组件: {components_str}, 总损失={loss.item():.4f}")

        return loss

# Lovász Loss 实现
def lovasz_grad(gt_sorted):
    """
    计算Lovász扩展的梯度
    """
    p = len(gt_sorted)
    gts = gt_sorted.sum()
    intersection = gts - gt_sorted.float().cumsum(0)
    union = gts + (1 - gt_sorted).float().cumsum(0)
    jaccard = 1. - intersection / union
    if p > 1:  # 如果p=1, jaccard=1, 避免nan
        jaccard[1:p] = jaccard[1:p] - jaccard[0:-1]
    return jaccard


def lovasz_softmax_flat(probs, labels, classes='present', ignore=None):
    """
    Lovász softmax损失的多类别版本
    probs: [P, C] 变形后的预测概率
    labels: [P] 变形后的标签
    classes: 'all' 表示所有, 'present' 表示仅计算存在的类别
    ignore: 忽略的类别索引
    """
    if probs.numel() == 0:
        return torch.tensor(0.0, device=probs.device)

    C = probs.size(1)
    losses = []
    class_to_sum = list(range(C)) if classes in ['all', 'present'] else classes

    # 检查标签是否有效
    if torch.max(labels) >= C and ignore is None:
        print(f"警告: Lovasz损失中标签超出类别范围: max={torch.max(labels).item()}, C={C}")
        # 将超出范围的标签设为忽略值
        if ignore is not None:
            labels = torch.where(labels >= C, torch.tensor(ignore, device=labels.device), labels)
        else:
            labels = torch.clamp(labels, 0, C-1)

    for c in class_to_sum:
        try:
            fg = (labels == c).float()  # 前景
            if fg.sum() == 0 and classes == 'present':
                continue
            if C == 1:
                if len(classes) > 1:
                    raise ValueError('单类别预测中，Lovasz损失不应该用于多类别')
                class_pred = probs[:, 0]
            else:
                class_pred = probs[:, c]

            # 忽略指定类别
            if ignore is not None:
                valid = (labels != ignore)
                class_pred = class_pred[valid]
                fg = fg[valid]

            # 如果没有有效样本，跳过
            if fg.numel() == 0:
                continue

            # 按预测值排序
            errors = (fg - class_pred).abs()
            errors_sorted, perm = torch.sort(errors, 0, descending=True)
            fg_sorted = fg[perm]

            # 计算Lovász梯度
            grad = lovasz_grad(fg_sorted)

            # 检查梯度是否有效
            if torch.isnan(grad).any() or torch.isinf(grad).any():
                print(f"警告: Lovasz梯度包含NaN或Inf值")
                continue

            # 计算损失
            loss = torch.dot(errors_sorted, grad)

            # 检查损失是否有效
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"警告: Lovasz类别{c}损失无效: {loss.item()}")
                continue

            losses.append(loss)
        except Exception as e:
            print(f"警告: 计算Lovasz类别{c}损失时出错: {e}")
            continue

    # 如果没有有效损失，返回默认值
    if len(losses) == 0:
        return torch.tensor(0.1, device=probs.device)

    # 返回平均损失
    return torch.stack(losses).mean()


class TverskyLoss(nn.Module):
    """
    Tversky损失函数 - 处理类别不平衡，偏向召回率或精确率
    """
    def __init__(self, alpha=0.3, beta=0.7, smooth=1e-6, ignore_index=255):
        super(TverskyLoss, self).__init__()
        self.alpha = alpha  # 假阳性权重
        self.beta = beta    # 假阴性权重
        self.smooth = smooth
        self.ignore_index = ignore_index

    def forward(self, pred, target):
        """
        Args:
            pred: 预测结果 [B, C, H, W]
            target: 真实标签 [B, H, W]
        """
        # 创建有效掩码
        valid_mask = (target != self.ignore_index)

        # 应用softmax
        pred_soft = F.softmax(pred, dim=1)

        # 转换为one-hot编码
        num_classes = pred.size(1)
        target_one_hot = F.one_hot(target.long(), num_classes=num_classes).permute(0, 3, 1, 2).float()

        # 应用有效掩码
        pred_soft = pred_soft * valid_mask.unsqueeze(1).float()
        target_one_hot = target_one_hot * valid_mask.unsqueeze(1).float()

        # 计算Tversky系数
        tp = torch.sum(pred_soft * target_one_hot, dim=(2, 3))
        fp = torch.sum(pred_soft * (1 - target_one_hot), dim=(2, 3))
        fn = torch.sum((1 - pred_soft) * target_one_hot, dim=(2, 3))

        tversky = (tp + self.smooth) / (tp + self.alpha * fp + self.beta * fn + self.smooth)

        # 返回损失 (1 - Tversky)
        loss = 1 - torch.mean(tversky)

        return loss


class LovaszLoss(nn.Module):
    """
    Lovász损失，优化mIoU
    """
    def __init__(self, classes='present', ignore_index=None):
        super(LovaszLoss, self).__init__()
        self.classes = classes
        self.ignore_index = ignore_index

    def forward(self, inputs, targets):
        # inputs: [B, C, H, W] logits
        # targets: [B, H, W] long
        probs = F.softmax(inputs, dim=1)
        B, C, H, W = inputs.shape

        # 变形为[B*H*W, C]和[B*H*W]
        probs = probs.permute(0, 2, 3, 1).contiguous().view(-1, C)  # [B*H*W, C]
        targets = targets.view(-1)  # [B*H*W]

        # 计算Lovász损失
        loss = lovasz_softmax_flat(probs, targets, self.classes, self.ignore_index)
        return loss
