#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动极端类别平衡训练
一键启动专门解决类别不平衡问题的训练
"""

import os
import sys
import subprocess
import time
import yaml
from datetime import datetime
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('extreme_balance_launcher.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def check_environment():
    """检查环境"""
    logger = logging.getLogger(__name__)
    
    # 检查Python版本
    python_version = sys.version_info
    logger.info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查PyTorch
    try:
        import torch
        logger.info(f"PyTorch版本: {torch.__version__}")
        logger.info(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            logger.info(f"CUDA版本: {torch.version.cuda}")
            logger.info(f"GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    except ImportError:
        logger.error("PyTorch未安装！")
        return False
        
    # 检查必要文件
    required_files = [
        'config_extreme_balance.yaml',
        'train_extreme_balance.py',
        'utils/extreme_balance_losses.py',
        'utils/difficult_class_handler.py'
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            logger.error(f"缺少必要文件: {file_path}")
            return False
        else:
            logger.info(f"✓ 找到文件: {file_path}")
            
    return True

def validate_config():
    """验证配置文件"""
    logger = logging.getLogger(__name__)
    
    try:
        with open('config_extreme_balance.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        # 检查关键配置
        required_keys = [
            'data.num_classes',
            'training.total_epochs',
            'training.batch_size',
            'class_balancing.class_weights',
            'loss.components'
        ]
        
        for key_path in required_keys:
            keys = key_path.split('.')
            current = config
            for key in keys:
                if key not in current:
                    logger.error(f"配置文件缺少关键配置: {key_path}")
                    return False
                current = current[key]
                
        logger.info("✓ 配置文件验证通过")
        
        # 显示关键配置信息
        logger.info("=== 关键配置信息 ===")
        logger.info(f"类别数量: {config['data']['num_classes']}")
        logger.info(f"训练轮数: {config['training']['total_epochs']}")
        logger.info(f"批次大小: {config['training']['batch_size']}")
        
        # 显示困难类别权重
        difficult_classes = [8, 10, 18, 23, 26, 27, 28]
        class_weights = config['class_balancing']['class_weights']
        logger.info("困难类别权重:")
        for class_id in difficult_classes:
            weight = class_weights.get(class_id, 1.0)
            logger.info(f"  类别 {class_id}: {weight}")
            
        return True
        
    except Exception as e:
        logger.error(f"配置文件验证失败: {e}")
        return False

def check_data():
    """检查数据"""
    logger = logging.getLogger(__name__)
    
    # 检查数据目录
    data_dirs = [
        'VOCdevkit/VOC2025/JPEGImages',
        'VOCdevkit/VOC2025/SegmentationClass',
        'VOCdevkit/VOC2025/ImageSets/Segmentation'
    ]
    
    for data_dir in data_dirs:
        if not os.path.exists(data_dir):
            logger.error(f"数据目录不存在: {data_dir}")
            return False
        else:
            file_count = len(os.listdir(data_dir))
            logger.info(f"✓ {data_dir}: {file_count} 个文件")
            
    # 检查标注文件
    annotation_files = [
        'VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt',
        'VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt'
    ]
    
    for ann_file in annotation_files:
        if not os.path.exists(ann_file):
            logger.error(f"标注文件不存在: {ann_file}")
            return False
        else:
            with open(ann_file, 'r') as f:
                lines = f.readlines()
            logger.info(f"✓ {ann_file}: {len(lines)} 个样本")
            
    return True

def create_backup():
    """创建配置备份"""
    logger = logging.getLogger(__name__)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backups/extreme_balance_{timestamp}"
    os.makedirs(backup_dir, exist_ok=True)
    
    # 备份配置文件
    import shutil
    shutil.copy2('config_extreme_balance.yaml', f"{backup_dir}/config_extreme_balance.yaml")
    
    logger.info(f"配置备份已创建: {backup_dir}")
    return backup_dir

def monitor_gpu_memory():
    """监控GPU内存"""
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                memory_allocated = torch.cuda.memory_allocated(i) / 1024**3
                memory_reserved = torch.cuda.memory_reserved(i) / 1024**3
                memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                
                print(f"GPU {i} 内存使用:")
                print(f"  已分配: {memory_allocated:.2f}GB")
                print(f"  已保留: {memory_reserved:.2f}GB")
                print(f"  总内存: {memory_total:.2f}GB")
                print(f"  使用率: {memory_allocated/memory_total*100:.1f}%")
    except:
        pass

def start_training():
    """启动训练"""
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 启动极端类别平衡训练...")
    
    # 构建训练命令
    cmd = [
        sys.executable,
        'train_extreme_balance.py'
    ]
    
    # 启动训练进程
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 实时输出训练日志
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
                
        # 获取返回码
        return_code = process.poll()
        
        if return_code == 0:
            logger.info("🎉 训练成功完成！")
        else:
            logger.error(f"❌ 训练失败，返回码: {return_code}")
            
        return return_code == 0
        
    except Exception as e:
        logger.error(f"启动训练失败: {e}")
        return False

def start_tensorboard():
    """启动TensorBoard"""
    logger = logging.getLogger(__name__)
    
    try:
        # 检查是否已有TensorBoard进程
        import psutil
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            if 'tensorboard' in proc.info['name'].lower():
                logger.info("TensorBoard已在运行")
                return
                
        # 启动TensorBoard
        log_dir = "logs/extreme_balance"
        cmd = [sys.executable, '-m', 'tensorboard', '--logdir', log_dir, '--port', '6006']
        
        subprocess.Popen(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        logger.info("TensorBoard已启动: http://localhost:6006")
        
    except Exception as e:
        logger.warning(f"启动TensorBoard失败: {e}")

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    极端类别平衡训练启动器                      ║
    ║                                                              ║
    ║  🎯 目标: 解决严重的类别不平衡问题                            ║
    ║  🔥 困难类别: 8, 10, 18, 23, 26, 27, 28                     ║
    ║  ⚡ 策略: 极端权重 + 多损失函数 + 特殊增强                     ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """主函数"""
    print_banner()
    
    logger = setup_logging()
    logger.info("开始极端类别平衡训练启动流程")
    
    # 1. 检查环境
    logger.info("=== 步骤 1: 检查环境 ===")
    if not check_environment():
        logger.error("环境检查失败，请修复后重试")
        return False
        
    # 2. 验证配置
    logger.info("=== 步骤 2: 验证配置 ===")
    if not validate_config():
        logger.error("配置验证失败，请检查配置文件")
        return False
        
    # 3. 检查数据
    logger.info("=== 步骤 3: 检查数据 ===")
    if not check_data():
        logger.error("数据检查失败，请确保数据完整")
        return False
        
    # 4. 创建备份
    logger.info("=== 步骤 4: 创建备份 ===")
    backup_dir = create_backup()
    
    # 5. 启动TensorBoard
    logger.info("=== 步骤 5: 启动TensorBoard ===")
    start_tensorboard()
    
    # 6. 显示GPU状态
    logger.info("=== 步骤 6: GPU状态 ===")
    monitor_gpu_memory()
    
    # 7. 启动训练
    logger.info("=== 步骤 7: 启动训练 ===")
    
    # 最后确认
    print("\n" + "="*60)
    print("🚨 即将开始极端类别平衡训练")
    print("📊 这将使用极高的类别权重来强制学习困难类别")
    print("⏱️  预计训练时间: 数小时到数天")
    print("💾 训练过程将自动保存最佳模型")
    print("📈 可通过 http://localhost:6006 查看TensorBoard")
    print("="*60)
    
    response = input("确认开始训练? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        logger.info("用户取消训练")
        return False
        
    success = start_training()
    
    if success:
        print("\n" + "🎉" * 20)
        print("训练成功完成！")
        print("🎉" * 20)
        
        # 显示结果位置
        print(f"\n📁 训练结果保存在: logs/extreme_balance/")
        print(f"💾 配置备份保存在: {backup_dir}")
        print(f"📊 TensorBoard: http://localhost:6006")
        
    else:
        print("\n" + "❌" * 20)
        print("训练失败，请检查日志")
        print("❌" * 20)
        
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
