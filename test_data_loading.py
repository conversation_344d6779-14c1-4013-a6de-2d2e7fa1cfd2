#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据加载
"""

import torch
import numpy as np
from enhanced_dataset import create_enhanced_datasets
from torch.utils.data import DataLoader

def test_data_loading():
    """测试数据加载"""
    print("🧪 测试数据加载...")
    
    try:
        # 创建数据集
        train_dataset, val_dataset = create_enhanced_datasets()
        print(f"✅ 数据集创建成功")
        print(f"📊 训练集: {len(train_dataset)} 张图像")
        print(f"📊 验证集: {len(val_dataset)} 张图像")
        
        # 测试单个样本
        print("\n🔍 测试单个样本...")
        image, label = train_dataset[0]
        print(f"📸 图像形状: {image.shape}, 类型: {image.dtype}")
        print(f"🏷️ 标签形状: {label.shape}, 类型: {label.dtype}")
        print(f"🎯 标签范围: {label.min().item()} - {label.max().item()}")
        
        # 测试数据加载器
        print("\n🔄 测试数据加载器...")
        train_loader = DataLoader(
            train_dataset,
            batch_size=2,
            shuffle=False,
            num_workers=0
        )
        
        # 获取一个批次
        for batch_idx, (images, labels) in enumerate(train_loader):
            print(f"✅ 批次 {batch_idx}:")
            print(f"  图像批次形状: {images.shape}, 类型: {images.dtype}")
            print(f"  标签批次形状: {labels.shape}, 类型: {labels.dtype}")
            print(f"  标签范围: {labels.min().item()} - {labels.max().item()}")
            
            if batch_idx >= 2:  # 只测试前3个批次
                break
        
        print("🎉 数据加载测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_data_loading()
