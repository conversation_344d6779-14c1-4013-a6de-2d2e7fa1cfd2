#!/usr/bin/env python3
"""
简化的优化训练脚本 - 基于深度分析的改进方案
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.unet import Unet
from utils.dataloader import SegmentationDataset
from utils.utils_fit import fit_one_epoch
from utils.callbacks import LossHistory, EvalCallback
from utils.class_weights import compute_class_weights
from utils.losses import CombinedLoss

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def main():
    """主训练函数"""
    logger = setup_logging()
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建日志目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"logs/optimized_simple_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    logger.info("开始优化训练 - 简化版")
    
    # 创建模型
    model = Unet(
        num_classes=29,
        backbone='resnet50',
        pretrained=True
    )
    model = model.to(device)
    logger.info("模型创建完成")
    
    # 读取数据集文件列表
    with open("VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt", "r") as f:
        train_lines = f.readlines()
    
    with open("VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt", "r") as f:
        val_lines = f.readlines()
    
    # 创建数据集
    train_dataset = SegmentationDataset(
        file_list=train_lines,
        root_dir="VOCdevkit",
        img_size=(512, 512),
        num_classes=29,
        train=True
    )
    
    val_dataset = SegmentationDataset(
        file_list=val_lines,
        root_dir="VOCdevkit",
        img_size=(512, 512),
        num_classes=29,
        train=False
    )
    
    logger.info(f"训练样本数: {len(train_dataset)}")
    logger.info(f"验证样本数: {len(val_dataset)}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=4,  # 降低批次大小
        shuffle=True,
        num_workers=2,  # 降低工作进程数
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=4,
        shuffle=False,
        num_workers=2,
        pin_memory=True,
        drop_last=False
    )
    
    # 计算类别权重 - 使用优化的对数平滑方法
    logger.info("计算类别权重...")
    class_weights = compute_class_weights(
        dataset_path=".",
        num_classes=29,
        method="log_smooth"  # 使用新的对数平滑方法
    ).to(device)
    logger.info(f"类别权重计算完成")
    
    # 创建损失函数配置
    loss_config = {
        'use_ce': True,
        'use_dice': True,
        'use_focal': True,
        'use_lovasz': True,
        'weight_ce': 0.4,      # 降低CE权重
        'weight_dice': 1.0,    # 降低Dice权重
        'weight_focal': 1.2,   # 增加Focal权重
        'weight_lovasz': 1.4,  # 增加Lovász权重
        'focal_gamma': 2.0,    # 降低gamma
        'focal_alpha': 0.25,   # 平衡正负样本
        'label_smoothing': 0.05,  # 降低标签平滑
        'ignore_index': 255
    }
    
    criterion = CombinedLoss(loss_config, class_weights)
    
    # 创建优化器 - 分层学习率
    encoder_params = []
    decoder_params = []
    head_params = []
    
    for name, param in model.named_parameters():
        if 'resnet' in name or 'vgg' in name:
            encoder_params.append(param)
        elif 'up_concat' in name:
            decoder_params.append(param)
        else:
            head_params.append(param)
    
    param_groups = [
        {'params': encoder_params, 'lr': 0.0002},  # 编码器低学习率
        {'params': decoder_params, 'lr': 0.0005}, # 解码器中等学习率
        {'params': head_params, 'lr': 0.001}      # 分割头高学习率
    ]
    
    optimizer = optim.AdamW(param_groups, weight_decay=0.0001)
    
    # 创建学习率调度器 - 余弦退火
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, 
        T_max=100,  # 总轮数
        eta_min=1e-6
    )
    
    # 创建回调函数
    loss_history = LossHistory(log_dir)
    eval_callback = EvalCallback(
        model, val_loader, 29, device, log_dir, eval_period=1
    )
    
    # 训练循环
    total_epochs = 100
    best_miou = 0.0
    
    logger.info("开始训练...")
    
    for epoch in range(total_epochs):
        logger.info(f"Epoch {epoch+1}/{total_epochs}")
        
        # 训练一个epoch
        train_loss = fit_one_epoch(
            model, criterion, optimizer, train_loader, 
            device, epoch, total_epochs, logger
        )
        
        # 验证
        val_loss, val_miou, val_fscore = eval_callback.on_epoch_end(epoch)
        
        # 更新学习率
        scheduler.step()
        
        # 记录损失
        loss_history.append_loss(epoch, train_loss, val_loss)
        
        # 保存最佳模型
        if val_miou > best_miou:
            best_miou = val_miou
            torch.save(model.state_dict(), os.path.join(log_dir, 'best_model.pth'))
            logger.info(f"保存最佳模型，mIoU: {best_miou:.4f}")
        
        # 记录当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        logger.info(f"当前学习率: {current_lr:.6f}")
        
        # 每10轮保存一次模型
        if (epoch + 1) % 10 == 0:
            torch.save(model.state_dict(), 
                      os.path.join(log_dir, f'epoch_{epoch+1}.pth'))
    
    logger.info("训练完成!")
    logger.info(f"最佳mIoU: {best_miou:.4f}")

if __name__ == "__main__":
    main()
