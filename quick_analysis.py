#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速分析训练结果
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

def analyze_training_results():
    """分析训练结果"""
    print("🔍 第一步：深入分析当前训练结果")
    print("=" * 50)

    # 找到最新的训练日志
    log_base = "UNet_Demo/UNet_Demo/logs"
    latest_log = None
    latest_time = 0

    for item in os.listdir(log_base):
        if item.startswith("resnet50_20250524"):
            item_path = os.path.join(log_base, item)
            if os.path.isdir(item_path):
                # 获取文件夹的修改时间
                mtime = os.path.getmtime(item_path)
                if mtime > latest_time:
                    latest_time = mtime
                    latest_log = item_path

    if not latest_log:
        print("❌ 未找到训练日志")
        return

    print(f"📁 分析日志目录: {latest_log}")

    # 读取mIoU数据
    miou_file = os.path.join(latest_log, "epoch_miou.txt")
    if os.path.exists(miou_file):
        with open(miou_file, 'r') as f:
            lines = f.readlines()
            mious = [float(line.strip()) for line in lines if line.strip()]

        print(f"\n📊 mIoU分析:")
        print(f"  总训练轮数: {len(mious)}")
        print(f"  初始mIoU: {mious[0]:.6f}")
        print(f"  最终mIoU: {mious[-1]:.4f}")
        print(f"  最高mIoU: {max(mious):.4f} (第{mious.index(max(mious))+1}轮)")
        print(f"  提升倍数: {mious[-1]/mious[0]:.0f}x")

        # 分析学习阶段
        print(f"\n📈 学习阶段分析:")

        # 快速学习期 (前30轮)
        if len(mious) >= 30:
            early_improvement = mious[29] - mious[0]
            print(f"  快速学习期 (1-30轮): +{early_improvement:.4f}")

        # 稳定期 (30-100轮)
        if len(mious) >= 100:
            mid_improvement = mious[99] - mious[29]
            print(f"  稳定提升期 (30-100轮): +{mid_improvement:.4f}")

        # 后期 (100轮后)
        if len(mious) > 100:
            late_improvement = mious[-1] - mious[99]
            print(f"  后期调优 (100轮后): +{late_improvement:.4f}")

        # 检查收敛情况
        if len(mious) >= 20:
            recent_variance = np.var(mious[-20:])
            print(f"  最近20轮方差: {recent_variance:.6f}")
            if recent_variance < 0.0001:
                print("  ✅ 模型已收敛")
            else:
                print("  ⚠️ 模型仍在学习")

    # 读取损失数据
    loss_file = os.path.join(latest_log, "epoch_loss.txt")
    val_loss_file = os.path.join(latest_log, "epoch_val_loss.txt")

    if os.path.exists(loss_file) and os.path.exists(val_loss_file):
        with open(loss_file, 'r') as f:
            train_losses = [float(line.strip()) for line in f.readlines() if line.strip()]

        with open(val_loss_file, 'r') as f:
            val_losses = [float(line.strip()) for line in f.readlines() if line.strip()]

        print(f"\n📉 损失分析:")
        print(f"  初始训练损失: {train_losses[0]:.4f}")
        print(f"  最终训练损失: {train_losses[-1]:.4f}")
        print(f"  损失下降: {(train_losses[0] - train_losses[-1])/train_losses[0]*100:.1f}%")
        print(f"  初始验证损失: {val_losses[0]:.4f}")
        print(f"  最终验证损失: {val_losses[-1]:.4f}")

        # 检查过拟合
        if train_losses[-1] < 1.0 and val_losses[-1] > 5.0:
            print("  ⚠️ 可能存在过拟合")
        else:
            print("  ✅ 训练验证损失合理")

    # 读取F-score数据
    fscore_file = os.path.join(latest_log, "epoch_val_fscore.txt")
    if os.path.exists(fscore_file):
        with open(fscore_file, 'r') as f:
            fscores = [float(line.strip()) for line in f.readlines() if line.strip()]

        print(f"\n🎯 F-score分析:")
        print(f"  最终F-score: {fscores[-1]:.4f}")
        print(f"  最高F-score: {max(fscores):.4f}")

    # 问题诊断
    print(f"\n🔍 问题诊断:")

    if mious[-1] < 0.4:
        print("❌ 主要问题：mIoU过低 (< 0.4)")

        if mious[-1] < 0.2:
            print("  🚨 严重问题：mIoU极低，基本功能缺失")
        elif mious[-1] < 0.3:
            print("  ⚠️ 中等问题：mIoU偏低，需要大幅改进")
        else:
            print("  💡 轻微问题：mIoU接近可用，需要优化")

        # 可能原因分析
        print("\n🤔 可能原因:")

        # 检查学习停滞
        if len(mious) >= 50:
            recent_50 = mious[-50:]
            if max(recent_50) - min(recent_50) < 0.01:
                print("  📉 学习停滞：最近50轮改善微小")

        # 检查类别不平衡
        if train_losses[-1] < 1.0 and mious[-1] < 0.4:
            print("  ⚖️ 类别不平衡：损失低但mIoU低，可能某些类别完全未学习")

        # 检查模型容量
        if len(mious) > 100 and mious[-1] - mious[50] < 0.05:
            print("  🧠 模型容量不足：长期训练改善有限")

        # 检查数据质量
        if val_losses[-1] > train_losses[-1] * 3:
            print("  📊 数据质量问题：验证损失远高于训练损失")

    # 生成改进建议
    print(f"\n💡 改进建议优先级:")

    suggestions = []

    # 基于mIoU水平给出建议
    if mious[-1] < 0.2:
        suggestions.extend([
            "🔥 紧急：检查数据标签质量和预处理",
            "🔥 紧急：验证模型架构是否正确",
            "🔥 紧急：检查损失函数设计"
        ])
    elif mious[-1] < 0.3:
        suggestions.extend([
            "⚡ 高优先级：升级到更强的骨干网络 (EfficientNet-B7, Swin-T)",
            "⚡ 高优先级：重新设计类别平衡策略",
            "⚡ 高优先级：增强数据增强"
        ])
    else:
        suggestions.extend([
            "📈 中优先级：优化训练策略 (学习率调度、更长训练)",
            "📈 中优先级：尝试集成学习",
            "📈 中优先级：后处理优化"
        ])

    # 基于训练特征给出建议
    if len(mious) >= 50:
        recent_trend = mious[-10:]
        if np.mean(recent_trend) < np.mean(mious[-30:-20]):
            suggestions.append("📉 学习率可能过高，考虑降低学习率")

    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion}")

    # 绘制训练曲线
    create_analysis_plots(mious, train_losses, val_losses, latest_log)

    print(f"\n📊 分析图表已保存到: {latest_log}/analysis_plots.png")
    print(f"🎯 下一步：根据优先级建议进行改进")

def create_analysis_plots(mious, train_losses, val_losses, save_dir):
    """创建分析图表"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('训练结果深度分析', fontsize=16)

    epochs = list(range(1, len(mious) + 1))

    # mIoU趋势
    axes[0, 0].plot(epochs, mious, 'b-', linewidth=2, label='mIoU')
    axes[0, 0].axhline(y=0.4, color='r', linestyle='--', label='目标 (0.4)')
    axes[0, 0].axhline(y=0.6, color='g', linestyle='--', label='良好 (0.6)')
    axes[0, 0].set_title(f'mIoU趋势 (最高: {max(mious):.4f})')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('mIoU')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 损失趋势
    loss_epochs = list(range(1, len(train_losses) + 1))
    axes[0, 1].plot(loss_epochs, train_losses, 'b-', label='训练损失')
    axes[0, 1].plot(loss_epochs, val_losses, 'r-', label='验证损失')
    axes[0, 1].set_title('损失趋势')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Loss')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].set_yscale('log')

    # mIoU改善率
    if len(mious) > 10:
        improvements = []
        window = 10
        for i in range(window, len(mious)):
            current_avg = np.mean(mious[i-window:i])
            prev_avg = np.mean(mious[i-window-window:i-window])
            improvement = (current_avg - prev_avg) / prev_avg * 100
            improvements.append(improvement)

        improvement_epochs = list(range(window+1, len(mious)+1))
        axes[1, 0].plot(improvement_epochs, improvements, 'g-', linewidth=2)
        axes[1, 0].axhline(y=0, color='k', linestyle='-', alpha=0.3)
        axes[1, 0].set_title('mIoU改善率 (%)')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('改善率 (%)')
        axes[1, 0].grid(True, alpha=0.3)

    # 性能分布
    axes[1, 1].hist(mious, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 1].axvline(x=np.mean(mious), color='r', linestyle='--', label=f'平均值: {np.mean(mious):.4f}')
    axes[1, 1].axvline(x=np.median(mious), color='g', linestyle='--', label=f'中位数: {np.median(mious):.4f}')
    axes[1, 1].set_title('mIoU分布')
    axes[1, 1].set_xlabel('mIoU')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'analysis_plots.png'), dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    analyze_training_results()
