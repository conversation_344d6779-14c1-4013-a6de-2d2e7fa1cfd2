import segmentation_models_pytorch as smp
from torch import nn
import torch

class CBAM(nn.Module):
    def __init__(self, in_planes, ratio=8, kernel_size=7):
        super(CBAM, self).__init__()
        self.channel_attention = ChannelAttention(in_planes, ratio)
        self.spatial_attention = SpatialAttention(kernel_size)

    def forward(self, x):
        x = self.channel_attention(x) * x
        x = self.spatial_attention(x) * x
        return x

class ChannelAttention(nn.Module):
    def __init__(self, in_planes, ratio=8):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc1 = nn.Conv2d(in_planes, in_planes // ratio, 1, bias=False)
        self.relu1 = nn.ReLU()
        self.fc2 = nn.Conv2d(in_planes // ratio, in_planes, 1, bias=False)

        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc2(self.relu1(self.fc1(self.avg_pool(x))))
        max_out = self.fc2(self.relu1(self.fc1(self.max_pool(x))))
        out = avg_out + max_out
        return self.sigmoid(out)

class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()

        assert kernel_size in (3, 7), 'kernel size must be 3 or 7'
        padding = 3 if kernel_size == 7 else 1

        self.conv1 = nn.Conv2d(2, 1, kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv1(x)
        return self.sigmoid(x)


def DeepLabV3Plus(num_classes=29, backbone='resnet101', pretrained=True):
    """
    构建带 CBAM 的 DeepLabV3+ 模型，使用指定的主干网络。
    """
    base_model = smp.DeepLabV3Plus(
        encoder_name=backbone,
        encoder_weights='imagenet' if pretrained else None,
        in_channels=3,
        classes=num_classes
    )

    class DeepLabV3Plus_CBAM(nn.Module):
        def __init__(self, base):
            super().__init__()
            self.encoder = base.encoder
            self.decoder = base.decoder
            self.segmentation_head = base.segmentation_head
            self.cbam = CBAM(in_planes=base.encoder.out_channels[-1])  # 最后一层通道加入CBAM

        def forward(self, x):
            features = self.encoder(x)
            features[-1] = self.cbam(features[-1])  # 只对最高层特征加CBAM
            decoder_output = self.decoder(*features)
            masks = self.segmentation_head(decoder_output)
            return masks

    return DeepLabV3Plus_CBAM(base_model)


# 兼容 Unet 推理类的 generate() 调用
class UnetWrapper(nn.Module):
    def __init__(self, num_classes=29, backbone='resnet101', pretrained=True):
        super(UnetWrapper, self).__init__()
        self.model = DeepLabV3Plus(num_classes=num_classes, backbone=backbone, pretrained=pretrained)
        self.encoder = self.model.encoder  # ⭐ 显式暴露 encoder 属性（供 train.py 使用）

    def forward(self, x):
        return self.model(x)


# 用于 predict.py 中 Unet 类替代原 unet()
def unet(num_classes=29, backbone='resnet101', pretrained=True):
    return UnetWrapper(num_classes=num_classes, backbone=backbone, pretrained=pretrained)
