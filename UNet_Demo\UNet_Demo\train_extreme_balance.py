#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极端类别平衡训练脚本
专门解决类别不平衡问题，特别是完全未学习的类别 8,10,18,23,26,27,28
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, WeightedRandomSampler
import numpy as np
import time
from datetime import datetime
import logging
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.unet import Unet
from utils.dataloader import SegmentationDataset
from utils.losses import DiceLoss, FocalLoss, CombinedLoss

class ExtremeBalanceTrainer:
    """极端类别平衡训练器"""

    def __init__(self, config_path):
        """初始化训练器"""
        self.config = self.load_config(config_path)
        self.setup_logging()
        self.setup_device()
        self.difficult_classes = [8, 10, 18, 23, 26, 27, 28]
        self.class_learning_history = defaultdict(list)

    def load_config(self, config_path):
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config

    def setup_logging(self):
        """设置日志"""
        log_dir = self.config['logging']['log_dir']
        os.makedirs(log_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f"extreme_balance_training_{timestamp}.log")

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_device(self):
        """设置设备"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger.info(f"使用设备: {self.device}")

        if torch.cuda.is_available():
            self.logger.info(f"GPU: {torch.cuda.get_device_name()}")
            self.logger.info(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")

    def create_model(self):
        """创建模型"""
        model_config = self.config['model']
        num_classes = self.config['data']['num_classes']

        model = Unet(
            num_classes=num_classes,
            backbone=model_config['backbone'],
            pretrained=model_config['pretrained']
        )

        # 添加注意力机制
        if model_config.get('attention', {}).get('enabled', False):
            self.add_attention_modules(model)

        # 添加困难类别专用模块
        if model_config.get('difficult_class_module', {}).get('enabled', False):
            self.add_difficult_class_module(model)

        return model.to(self.device)

    def add_attention_modules(self, model):
        """添加注意力机制"""
        # 这里可以添加CBAM等注意力机制
        self.logger.info("添加注意力机制")

    def add_difficult_class_module(self, model):
        """添加困难类别专用模块"""
        # 为困难类别添加专门的处理模块
        self.logger.info("添加困难类别专用模块")

    def create_extreme_class_weights(self):
        """创建极端类别权重"""
        class_weights = self.config['class_balancing']['class_weights']
        num_classes = self.config['data']['num_classes']

        # 创建权重张量
        weights = torch.ones(num_classes)
        for class_id, weight in class_weights.items():
            weights[class_id] = weight

        self.logger.info("极端类别权重:")
        for i, weight in enumerate(weights):
            if weight != 1.0:
                self.logger.info(f"  类别 {i}: {weight:.2f}")

        return weights.to(self.device)

    def create_loss_function(self):
        """创建损失函数"""
        loss_config = self.config['loss']
        class_weights = self.create_extreme_class_weights()

        # 创建多个损失函数
        losses = {}

        # 交叉熵损失
        if loss_config['components']['cross_entropy']['enabled']:
            ce_config = loss_config['components']['cross_entropy']
            losses['ce'] = CE_Loss(
                num_classes=self.config['data']['num_classes'],
                alpha=class_weights,
                size_average=True
            )

        # Focal损失
        if loss_config['components']['focal_loss']['enabled']:
            focal_config = loss_config['components']['focal_loss']
            losses['focal'] = Focal_Loss(
                alpha=class_weights,
                gamma=focal_config['gamma'],
                num_classes=self.config['data']['num_classes']
            )

        # Dice损失
        if loss_config['components']['dice_loss']['enabled']:
            losses['dice'] = Dice_loss(self.config['data']['num_classes'])

        # 困难类别专用Focal损失
        if loss_config['components']['difficult_class_focal']['enabled']:
            difficult_config = loss_config['components']['difficult_class_focal']
            difficult_weights = torch.ones(self.config['data']['num_classes'])
            for class_id in self.difficult_classes:
                difficult_weights[class_id] = 10.0  # 超高权重

            losses['difficult_focal'] = Focal_Loss(
                alpha=difficult_weights.to(self.device),
                gamma=difficult_config['gamma'],
                num_classes=self.config['data']['num_classes']
            )

        return losses

    def create_optimizer(self, model):
        """创建优化器"""
        train_config = self.config['training']
        lr_config = train_config['learning_rate']

        # 分层学习率
        param_groups = []

        # 骨干网络参数
        backbone_params = []
        decoder_params = []
        classifier_params = []

        for name, param in model.named_parameters():
            if 'backbone' in name:
                backbone_params.append(param)
            elif 'classifier' in name or 'head' in name:
                classifier_params.append(param)
            else:
                decoder_params.append(param)

        param_groups.extend([
            {'params': backbone_params, 'lr': lr_config['backbone_lr']},
            {'params': decoder_params, 'lr': lr_config['decoder_lr']},
            {'params': classifier_params, 'lr': lr_config['classifier_lr']}
        ])

        optimizer_config = train_config['optimizer']
        optimizer = optim.AdamW(
            param_groups,
            weight_decay=optimizer_config['weight_decay'],
            betas=(optimizer_config['beta1'], optimizer_config['beta2']),
            eps=optimizer_config['eps']
        )

        return optimizer

    def create_scheduler(self, optimizer):
        """创建学习率调度器"""
        scheduler_config = self.config['training']['scheduler']

        if scheduler_config['type'] == 'cosine_restart':
            scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
                optimizer,
                T_0=scheduler_config['restart_epochs'][0],
                T_mult=2,
                eta_min=scheduler_config['min_lr']
            )
        else:
            scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.1)

        return scheduler

    def create_balanced_sampler(self, dataset):
        """创建平衡采样器"""
        sampling_config = self.config['sampling']

        if not sampling_config['oversample_strategy']['enabled']:
            return None

        # 计算每个类别的样本权重
        class_counts = defaultdict(int)
        sample_weights = []

        for idx in range(len(dataset)):
            # 这里需要根据实际数据集实现获取标签的方法
            # 暂时使用均匀权重
            sample_weights.append(1.0)

        # 对困难类别进行过采样
        oversample_ratio = sampling_config['oversample_strategy']['oversample_ratio']
        for idx, weight in enumerate(sample_weights):
            # 如果样本包含困难类别，增加权重
            sample_weights[idx] = weight * oversample_ratio

        sampler = WeightedRandomSampler(
            weights=sample_weights,
            num_samples=len(sample_weights),
            replacement=True
        )

        return sampler

    def create_dataloaders(self):
        """创建数据加载器"""
        data_config = self.config['data']
        train_config = self.config['training']

        # 训练数据集
        train_lines = self.get_annotation_lines(data_config['train_annotation_path'])
        train_dataset = SegmentationDataset(
            file_list=train_lines,
            root_dir='VOCdevkit',
            img_size=data_config['input_shape'][:2],
            num_classes=data_config['num_classes'],
            train=True
        )

        # 验证数据集
        val_lines = self.get_annotation_lines(data_config['val_annotation_path'])
        val_dataset = SegmentationDataset(
            file_list=val_lines,
            root_dir='VOCdevkit',
            img_size=data_config['input_shape'][:2],
            num_classes=data_config['num_classes'],
            train=False
        )

        # 创建平衡采样器
        train_sampler = self.create_balanced_sampler(train_dataset)

        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=train_config['batch_size'],
            sampler=train_sampler,
            shuffle=(train_sampler is None),
            num_workers=self.config['hardware']['num_workers'],
            pin_memory=self.config['hardware']['pin_memory'],
            drop_last=True
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=train_config['batch_size'],
            shuffle=False,
            num_workers=self.config['hardware']['num_workers'],
            pin_memory=self.config['hardware']['pin_memory'],
            drop_last=False
        )

        return train_loader, val_loader

    def get_annotation_lines(self, annotation_path):
        """获取标注文件行"""
        with open(annotation_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        return [line.strip() for line in lines]

    def monitor_difficult_classes(self, epoch, metrics):
        """监控困难类别学习进度"""
        if 'class_iou' not in metrics:
            return

        class_ious = metrics['class_iou']

        self.logger.info(f"\n=== 第 {epoch} 轮困难类别监控 ===")

        for class_id in self.difficult_classes:
            if class_id < len(class_ious):
                iou = class_ious[class_id]
                self.class_learning_history[class_id].append(iou)

                # 检查是否有改善
                if len(self.class_learning_history[class_id]) > 1:
                    prev_iou = self.class_learning_history[class_id][-2]
                    improvement = iou - prev_iou
                    status = "📈" if improvement > 0 else "📉" if improvement < 0 else "➡️"
                    self.logger.info(f"  类别 {class_id}: IoU={iou:.4f} {status} (变化: {improvement:+.4f})")
                else:
                    self.logger.info(f"  类别 {class_id}: IoU={iou:.4f}")

                # 零IoU警报
                if iou == 0.0:
                    self.logger.warning(f"⚠️  类别 {class_id} 仍然完全未学习！")
                elif iou > 0.001:
                    self.logger.info(f"🎉 类别 {class_id} 开始学习！IoU={iou:.4f}")

    def train(self):
        """开始训练"""
        self.logger.info("🚀 开始极端类别平衡训练")
        self.logger.info(f"目标困难类别: {self.difficult_classes}")

        # 创建模型和训练组件
        model = self.create_model()
        losses = self.create_loss_function()
        optimizer = self.create_optimizer(model)
        scheduler = self.create_scheduler(optimizer)
        train_loader, val_loader = self.create_dataloaders()

        # 训练循环
        total_epochs = self.config['training']['total_epochs']
        best_miou = 0.0
        patience_counter = 0
        patience = self.config['evaluation']['early_stopping']['patience']

        for epoch in range(1, total_epochs + 1):
            self.logger.info(f"\n{'='*50}")
            self.logger.info(f"第 {epoch}/{total_epochs} 轮训练")
            self.logger.info(f"{'='*50}")

            # 训练一轮
            train_loss = self.train_one_epoch(model, train_loader, losses, optimizer, epoch)

            # 验证
            val_metrics = self.validate(model, val_loader, losses, epoch)

            # 更新学习率
            scheduler.step()

            # 监控困难类别
            self.monitor_difficult_classes(epoch, val_metrics)

            # 检查是否需要早停
            current_miou = val_metrics.get('miou', 0.0)
            if current_miou > best_miou:
                best_miou = current_miou
                patience_counter = 0
                self.save_model(model, epoch, 'best')
                self.logger.info(f"🏆 新的最佳mIoU: {best_miou:.4f}")
            else:
                patience_counter += 1

            if patience_counter >= patience:
                self.logger.info(f"早停触发，最佳mIoU: {best_miou:.4f}")
                break

        self.logger.info("🎯 训练完成！")

    def train_one_epoch(self, model, train_loader, losses, optimizer, epoch):
        """训练一轮"""
        model.train()
        total_loss = 0.0

        for batch_idx, (images, targets) in enumerate(train_loader):
            images = images.to(self.device)
            targets = targets.to(self.device)

            optimizer.zero_grad()

            # 前向传播
            outputs = model(images)

            # 计算损失
            total_batch_loss = 0.0
            loss_components = {}

            for loss_name, loss_fn in losses.items():
                loss_value = loss_fn(outputs, targets)
                loss_components[loss_name] = loss_value.item()
                total_batch_loss += loss_value

            # 反向传播
            total_batch_loss.backward()

            # 梯度裁剪
            if self.config['training']['gradient_clipping'] > 0:
                torch.nn.utils.clip_grad_norm_(
                    model.parameters(),
                    self.config['training']['gradient_clipping']
                )

            optimizer.step()

            total_loss += total_batch_loss.item()

            # 打印进度
            if batch_idx % 10 == 0:
                self.logger.info(
                    f"Epoch {epoch}, Batch {batch_idx}/{len(train_loader)}, "
                    f"Loss: {total_batch_loss.item():.4f}"
                )

        return total_loss / len(train_loader)

    def validate(self, model, val_loader, losses, epoch):
        """验证"""
        model.eval()
        total_loss = 0.0

        with torch.no_grad():
            for images, targets in val_loader:
                images = images.to(self.device)
                targets = targets.to(self.device)

                outputs = model(images)

                # 计算损失
                total_batch_loss = 0.0
                for loss_fn in losses.values():
                    total_batch_loss += loss_fn(outputs, targets)

                total_loss += total_batch_loss.item()

        avg_loss = total_loss / len(val_loader)

        # 计算mIoU等指标
        # 这里需要实现具体的指标计算
        metrics = {
            'val_loss': avg_loss,
            'miou': 0.0,  # 需要实现
            'class_iou': []  # 需要实现
        }

        self.logger.info(f"验证损失: {avg_loss:.4f}")

        return metrics

    def save_model(self, model, epoch, suffix=''):
        """保存模型"""
        save_dir = os.path.join(self.config['logging']['log_dir'], 'models')
        os.makedirs(save_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"extreme_balance_model_epoch_{epoch}_{suffix}_{timestamp}.pth"
        filepath = os.path.join(save_dir, filename)

        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'config': self.config,
            'difficult_classes': self.difficult_classes,
            'class_learning_history': dict(self.class_learning_history)
        }, filepath)

        self.logger.info(f"模型已保存: {filepath}")

def main():
    """主函数"""
    config_path = "config_extreme_balance.yaml"

    if not os.path.exists(config_path):
        print(f"配置文件不存在: {config_path}")
        return

    trainer = ExtremeBalanceTrainer(config_path)
    trainer.train()

if __name__ == "__main__":
    main()
