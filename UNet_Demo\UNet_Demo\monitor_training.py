"""
实时监控训练进度的脚本
启动TensorBoard并显示实时训练结果
"""
import os
import argparse
import subprocess
import time
import webbrowser
import signal
import sys
import threading
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="启动TensorBoard并监控训练进度")
    parser.add_argument('--logs_dir', type=str, default='logs/current', help='日志目录')
    parser.add_argument('--port', type=int, default=6006, help='TensorBoard端口')
    parser.add_argument('--refresh', type=int, default=30, help='刷新间隔（秒）')
    parser.add_argument('--no_browser', action='store_true', help='不自动打开浏览器')
    return parser.parse_args()

def start_tensorboard(logs_dir, port=6006):
    """启动TensorBoard"""
    print(f"启动TensorBoard，监控目录: {logs_dir}")
    cmd = f"tensorboard --logdir={logs_dir} --port={port}"

    # 在新进程中启动TensorBoard
    process = subprocess.Popen(cmd, shell=True)

    # 等待TensorBoard启动
    time.sleep(2)

    return process, f"http://localhost:{port}"

def open_browser(url):
    """打开浏览器"""
    print(f"在浏览器中打开TensorBoard: {url}")
    try:
        webbrowser.open(url)
    except Exception as e:
        print(f"无法打开浏览器: {e}")

def monitor_training_progress(logs_dir, refresh_interval=30):
    """监控训练进度"""
    last_modified_time = 0

    while True:
        try:
            # 检查日志目录是否存在
            if not os.path.exists(logs_dir):
                print(f"日志目录 {logs_dir} 不存在")
                time.sleep(refresh_interval)
                continue

            # 获取日志目录的最后修改时间
            current_modified_time = os.path.getmtime(logs_dir)

            # 如果日志目录有更新
            if current_modified_time > last_modified_time:
                last_modified_time = current_modified_time

                # 读取最新的训练指标
                metrics = read_latest_metrics(logs_dir)

                # 打印训练进度
                print_training_progress(metrics)

            # 等待下一次刷新
            time.sleep(refresh_interval)

        except KeyboardInterrupt:
            print("监控已停止")
            break
        except Exception as e:
            print(f"监控出错: {e}")
            time.sleep(refresh_interval)

def read_latest_metrics(logs_dir):
    """读取最新的训练指标"""
    metrics = {}

    # 读取训练损失
    try:
        loss_file = os.path.join(logs_dir, "epoch_loss.txt")
        if os.path.exists(loss_file):
            with open(loss_file, 'r') as f:
                lines = f.readlines()
                if lines:
                    metrics['train_loss'] = float(lines[-1].strip())
    except:
        pass

    # 读取验证损失
    try:
        val_loss_file = os.path.join(logs_dir, "epoch_val_loss.txt")
        if os.path.exists(val_loss_file):
            with open(val_loss_file, 'r') as f:
                lines = f.readlines()
                if lines:
                    metrics['val_loss'] = float(lines[-1].strip())
    except:
        pass

    # 读取F-score
    try:
        fscore_file = os.path.join(logs_dir, "epoch_val_fscore.txt")
        if os.path.exists(fscore_file):
            with open(fscore_file, 'r') as f:
                lines = f.readlines()
                if lines:
                    metrics['fscore'] = float(lines[-1].strip())
    except:
        pass

    # 读取mIoU
    try:
        miou_file = os.path.join(logs_dir, "epoch_miou.txt")
        if os.path.exists(miou_file):
            with open(miou_file, 'r') as f:
                lines = f.readlines()
                if lines:
                    metrics['miou'] = float(lines[-1].strip())
    except:
        pass

    # 计算当前轮次
    try:
        existing_files = [f for f in ["epoch_loss.txt", "epoch_val_loss.txt"]
                         if os.path.exists(os.path.join(logs_dir, f))]
        if existing_files:
            metrics['epoch'] = max(len(open(os.path.join(logs_dir, f)).readlines())
                                for f in existing_files)
        else:
            metrics['epoch'] = 0
    except Exception as e:
        print(f"计算轮次时出错: {e}")
        metrics['epoch'] = 0

    return metrics

def print_training_progress(metrics):
    """打印训练进度"""
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"\n[{now}] 训练进度更新:")
    print("-" * 50)

    if 'epoch' in metrics:
        print(f"当前轮次: {metrics['epoch']}")

    if 'train_loss' in metrics:
        print(f"训练损失: {metrics['train_loss']:.4f}")

    if 'val_loss' in metrics:
        print(f"验证损失: {metrics['val_loss']:.4f}")

    if 'fscore' in metrics:
        print(f"F-score: {metrics['fscore']:.4f}")

    if 'miou' in metrics:
        print(f"mIoU: {metrics['miou']:.4f}")

    print("-" * 50)

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    print("\n正在关闭监控...")
    sys.exit(0)

def main():
    args = parse_args()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)

    # 启动TensorBoard
    tensorboard_process, url = start_tensorboard(args.logs_dir, args.port)

    # 打开浏览器
    if not args.no_browser:
        open_browser(url)

    try:
        # 创建监控线程
        monitor_thread = threading.Thread(
            target=monitor_training_progress,
            args=(args.logs_dir, args.refresh),
            daemon=True
        )
        monitor_thread.start()

        # 等待TensorBoard进程结束
        tensorboard_process.wait()

    except KeyboardInterrupt:
        print("\n正在关闭TensorBoard和监控...")
    finally:
        # 确保TensorBoard进程被终止
        if tensorboard_process.poll() is None:
            tensorboard_process.terminate()

if __name__ == "__main__":
    main()
