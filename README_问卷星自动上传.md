# 问卷星自动上传工具使用说明

## 📋 功能概述

这个工具可以自动化完成以下任务：
- 批量上传图片到问卷星问卷编辑界面
- 创建循环评价题目
- 每题上传10张按顺序排列的图片作为评价对象
- 每个问卷创建30题循环评价项目
- 自动添加评价选项（非常不满意、不满意、一般、满意、非常满意）

## 🔧 环境要求

### 1. Python环境
```bash
pip install selenium
```

### 2. Chrome浏览器和ChromeDriver
- 安装最新版Chrome浏览器
- 下载对应版本的ChromeDriver：https://chromedriver.chromium.org/
- 将ChromeDriver.exe放在Python Scripts目录或系统PATH中

### 3. 图片准备
- 图片文件夹：`D:\1a_taohuacun`
- 图片格式：支持jpg, jpeg, png, bmp, gif
- 图片数量：至少300张（30题 × 10张/题）
- 图片命名：按文件名自然排序

## 🚀 使用步骤

### 1. 准备工作
1. 确保图片文件夹 `D:\1a_taohuacun` 存在且包含878张图片
2. 确保已安装Python和selenium库
3. 确保Chrome浏览器和ChromeDriver已正确安装

### 2. 运行脚本
```bash
python wenjuanxing_auto_upload.py
```

### 3. 操作流程
1. **启动确认**：脚本会显示配置信息，输入 `y` 确认开始
2. **浏览器打开**：自动打开Chrome浏览器并导航到问卷编辑页面
3. **手动登录**：如果需要登录，请在浏览器中手动完成登录
4. **自动执行**：脚本将自动执行以下操作：
   - 添加题目 → 选择循环评价 → 设置题目标题
   - 评价对象设置 → 选择图片 → 上传10张图片
   - 添加评价选项 → 保存题目
   - 重复30次创建30题

### 4. 监控进度
- 控制台会实时显示执行进度
- 日志文件会记录详细操作信息
- 如果出错会自动截图保存

## 📊 配置参数

### 主要参数
```python
image_folder = r"D:\1a_taohuacun"  # 图片文件夹路径
questions_per_questionnaire = 30    # 每个问卷的题目数量
images_per_question = 10           # 每题的图片数量
```

### 评价选项
```python
evaluation_options = [
    "非常不满意",
    "不满意", 
    "一般",
    "满意",
    "非常满意"
]
```

## 🔍 故障排除

### 常见问题

1. **找不到元素错误**
   - 问卷星页面可能更新了界面
   - 脚本会尝试多种选择器，如果都失败会截图保存错误状态
   - 检查截图文件了解具体问题

2. **图片上传失败**
   - 检查图片文件是否存在且格式正确
   - 检查网络连接是否稳定
   - 图片文件大小不要超过问卷星限制

3. **浏览器驱动问题**
   - 确保ChromeDriver版本与Chrome浏览器版本匹配
   - 将ChromeDriver.exe放在系统PATH中

4. **登录问题**
   - 脚本会检测是否需要登录
   - 如需登录，请手动在浏览器中完成
   - 登录完成后按回车键继续

### 调试模式
如果遇到问题，可以修改脚本中的等待时间：
```python
time.sleep(5)  # 增加等待时间
```

## 📝 日志文件

脚本会生成详细的日志文件：
- 文件名格式：`wenjuanxing_upload_YYYYMMDD_HHMMSS.log`
- 记录所有操作步骤和错误信息
- 可用于问题诊断和进度跟踪

## ⚠️ 注意事项

1. **网络稳定性**：确保网络连接稳定，避免上传中断
2. **页面变化**：问卷星可能更新界面，导致脚本失效
3. **执行时间**：完整执行需要较长时间（约1-2小时）
4. **手动干预**：执行过程中不要手动操作浏览器
5. **数据备份**：建议先在测试问卷中验证脚本功能

## 🔄 自定义修改

### 修改题目数量
```python
# 在main()函数中修改
success = self.create_questionnaire(30)  # 改为需要的题目数量
```

### 修改每题图片数量
```python
# 在create_questionnaire()方法中修改
images_per_question = 10  # 改为需要的图片数量
```

### 修改评价选项
```python
# 在add_evaluation_options()方法中修改
evaluation_options = [
    "选项1",
    "选项2",
    "选项3",
    # 添加更多选项
]
```

## 📞 技术支持

如果遇到问题：
1. 查看日志文件中的错误信息
2. 检查错误截图文件
3. 确认问卷星页面是否有变化
4. 尝试手动执行一遍流程确认步骤

## 🎯 执行效果

成功执行后，您的问卷将包含：
- 30个循环评价题目
- 每题包含10张图片作为评价对象
- 每题包含5个评价选项
- 总计300张图片被上传并组织成评价题目

脚本执行完成后，您可以在问卷星编辑界面中看到所有创建的题目，并可以进行进一步的编辑和发布。
