"""
GPU监控脚本
监控GPU使用情况，包括内存使用、利用率和温度
"""
import os
import time
import argparse
import subprocess
import platform
import datetime
import threading
import signal
import sys
from tabulate import tabulate

# 检查是否安装了必要的包
try:
    import psutil
    import GPUtil
    import numpy as np
    import matplotlib.pyplot as plt
    from matplotlib.animation import FuncAnimation
except ImportError:
    print("请安装必要的包: pip install psutil GPUtil numpy matplotlib tabulate")
    sys.exit(1)

# 全局变量
running = True
data = {
    'time': [],
    'gpu_util': [],
    'gpu_mem': [],
    'gpu_temp': [],
    'cpu_util': [],
    'ram_util': []
}
lock = threading.Lock()

def parse_args():
    parser = argparse.ArgumentParser(description="监控GPU使用情况")
    parser.add_argument('--interval', type=float, default=1.0, help='采样间隔（秒）')
    parser.add_argument('--log', action='store_true', help='是否记录到文件')
    parser.add_argument('--plot', action='store_true', help='是否实时绘图')
    parser.add_argument('--duration', type=int, default=0, help='监控持续时间（秒），0表示一直运行')
    return parser.parse_args()

def get_gpu_info():
    """获取GPU信息"""
    try:
        gpus = GPUtil.getGPUs()
        if not gpus:
            return None, None, None
        
        # 只获取第一个GPU的信息
        gpu = gpus[0]
        return gpu.load * 100, gpu.memoryUsed, gpu.temperature
    except Exception as e:
        print(f"获取GPU信息出错: {e}")
        return None, None, None

def get_system_info():
    """获取系统信息"""
    try:
        cpu_percent = psutil.cpu_percent()
        ram_percent = psutil.virtual_memory().percent
        return cpu_percent, ram_percent
    except Exception as e:
        print(f"获取系统信息出错: {e}")
        return None, None

def collect_data():
    """收集数据"""
    global data, running
    
    args = parse_args()
    interval = args.interval
    log_file = None
    
    if args.log:
        log_dir = "logs/gpu_monitor"
        os.makedirs(log_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = open(f"{log_dir}/gpu_log_{timestamp}.csv", "w")
        log_file.write("时间,GPU利用率(%),GPU内存(MB),GPU温度(°C),CPU利用率(%),内存利用率(%)\n")
    
    print("开始监控GPU使用情况...")
    print("按Ctrl+C停止监控")
    
    start_time = time.time()
    
    try:
        while running:
            current_time = time.time() - start_time
            gpu_util, gpu_mem, gpu_temp = get_gpu_info()
            cpu_util, ram_util = get_system_info()
            
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            
            with lock:
                data['time'].append(current_time)
                data['gpu_util'].append(gpu_util if gpu_util is not None else 0)
                data['gpu_mem'].append(gpu_mem if gpu_mem is not None else 0)
                data['gpu_temp'].append(gpu_temp if gpu_temp is not None else 0)
                data['cpu_util'].append(cpu_util if cpu_util is not None else 0)
                data['ram_util'].append(ram_util if ram_util is not None else 0)
            
            # 打印当前状态
            table = [
                ["GPU利用率", f"{gpu_util:.1f}%" if gpu_util is not None else "N/A"],
                ["GPU内存", f"{gpu_mem:.0f} MB" if gpu_mem is not None else "N/A"],
                ["GPU温度", f"{gpu_temp:.1f}°C" if gpu_temp is not None else "N/A"],
                ["CPU利用率", f"{cpu_util:.1f}%" if cpu_util is not None else "N/A"],
                ["内存利用率", f"{ram_util:.1f}%" if ram_util is not None else "N/A"]
            ]
            
            os.system('cls' if platform.system() == 'Windows' else 'clear')
            print(f"GPU监控 - {timestamp} (运行时间: {int(current_time)}秒)")
            print(tabulate(table, headers=["指标", "值"], tablefmt="grid"))
            
            # 记录到文件
            if log_file:
                log_file.write(f"{timestamp},{gpu_util:.1f},{gpu_mem:.0f},{gpu_temp:.1f},{cpu_util:.1f},{ram_util:.1f}\n")
                log_file.flush()
            
            # 检查是否达到持续时间
            if args.duration > 0 and current_time >= args.duration:
                running = False
                break
            
            time.sleep(interval)
    
    except KeyboardInterrupt:
        print("\n停止监控")
    finally:
        if log_file:
            log_file.close()
            print(f"日志已保存到: {log_file.name}")

def update_plot(frame):
    """更新绘图"""
    global data
    
    with lock:
        time_data = data['time']
        gpu_util_data = data['gpu_util']
        gpu_mem_data = data['gpu_mem']
        gpu_temp_data = data['gpu_temp']
        cpu_util_data = data['cpu_util']
        ram_util_data = data['ram_util']
    
    # 清除当前图形
    plt.clf()
    
    # 创建子图
    ax1 = plt.subplot(3, 1, 1)
    ax2 = plt.subplot(3, 1, 2)
    ax3 = plt.subplot(3, 1, 3)
    
    # 绘制GPU利用率
    ax1.plot(time_data, gpu_util_data, 'r-', label='GPU利用率 (%)')
    ax1.plot(time_data, cpu_util_data, 'b-', label='CPU利用率 (%)')
    ax1.set_ylabel('利用率 (%)')
    ax1.set_title('GPU和CPU利用率')
    ax1.grid(True)
    ax1.legend()
    
    # 绘制GPU内存
    ax2.plot(time_data, gpu_mem_data, 'g-', label='GPU内存 (MB)')
    ax2.plot(time_data, ram_util_data, 'y-', label='内存利用率 (%)')
    ax2.set_ylabel('内存')
    ax2.set_title('GPU内存和系统内存')
    ax2.grid(True)
    ax2.legend()
    
    # 绘制GPU温度
    ax3.plot(time_data, gpu_temp_data, 'm-', label='GPU温度 (°C)')
    ax3.set_xlabel('时间 (秒)')
    ax3.set_ylabel('温度 (°C)')
    ax3.set_title('GPU温度')
    ax3.grid(True)
    ax3.legend()
    
    plt.tight_layout()
    
    return ax1, ax2, ax3

def plot_data():
    """绘制数据"""
    plt.figure(figsize=(10, 8))
    ani = FuncAnimation(plt.gcf(), update_plot, interval=1000)
    plt.tight_layout()
    plt.show()

def signal_handler(sig, frame):
    """处理信号"""
    global running
    running = False
    print("\n停止监控")
    sys.exit(0)

def main():
    args = parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    # 创建数据收集线程
    data_thread = threading.Thread(target=collect_data)
    data_thread.daemon = True
    data_thread.start()
    
    # 如果需要绘图，启动绘图线程
    if args.plot:
        plot_data()
    else:
        # 等待数据收集线程结束
        data_thread.join()
    
    # 保存最终图表
    if len(data['time']) > 0:
        plt.figure(figsize=(12, 9))
        update_plot(0)
        
        # 创建保存目录
        save_dir = "logs/gpu_monitor"
        os.makedirs(save_dir, exist_ok=True)
        
        # 保存图表
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        plt.savefig(f"{save_dir}/gpu_plot_{timestamp}.png")
        print(f"图表已保存到: {save_dir}/gpu_plot_{timestamp}.png")

if __name__ == "__main__":
    main()
