# 立即改进配置 - 针对当前mIoU 0.36的优化方案
# 目标：快速提升到0.4+，解决类别不平衡问题

# 数据配置
data:
  num_classes: 29
  input_shape: [512, 512, 3]
  
  # 数据路径
  train_annotation_path: "VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt"
  val_annotation_path: "VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt"
  
  # 数据增强 - 更激进的策略
  data_augmentation:
    enabled: true
    
    # 基础增强 - 增强强度
    basic:
      horizontal_flip: 0.7  # 增加翻转概率
      vertical_flip: 0.3
      rotation: 25  # 增加旋转角度
      brightness: 0.3  # 增加亮度变化
      contrast: 0.3
      saturation: 0.25
      hue: 0.15
    
    # 几何变换 - 更强的变换
    geometric:
      random_crop: 0.8
      crop_scale: [0.6, 1.0]  # 更大的裁剪范围
      elastic_transform: 0.3  # 弹性变换
      grid_distortion: 0.2
    
    # 针对困难类别的特殊增强
    class_specific:
      enabled: true
      difficult_classes: [8, 18, 28]  # 完全未学习的类别
      oversample_ratio: 3.0  # 对困难类别3倍过采样
      special_augmentation: true

# 模型配置 - 增强版
model:
  backbone: "resnet50"
  pretrained: true
  
  # 注意力机制
  use_attention: true
  attention_type: "cbam"  # CBAM注意力
  attention_reduction: 16
  
  # Dropout增强正则化
  dropout_rate: 0.3  # 增加dropout
  
  # 特征融合增强
  feature_fusion: "fpn"  # 使用FPN特征金字塔
  
  # 深度监督
  deep_supervision: true
  aux_loss_weight: 0.4

# 训练配置 - 激进优化
training:
  total_epochs: 200  # 增加训练轮数
  batch_size: 6  # 适中的批次大小
  
  # 学习率策略 - 更精细的调度
  learning_rate:
    init_lr: 0.002  # 稍高的初始学习率
    scheduler: "cosine_restart"  # 余弦重启
    warmup_epochs: 15  # 更长的预热
    restart_epochs: [50, 100, 150]  # 多次重启
    min_lr: 5e-7
    
  # 分层学习率
  layerwise_lr:
    enabled: true
    backbone_lr_scale: 0.1  # 骨干网络更低学习率
    decoder_lr_scale: 1.0
    head_lr_scale: 2.0  # 分类头更高学习率
  
  # 优化器配置
  optimizer:
    type: "adamw"
    weight_decay: 0.01  # 增加权重衰减
    beta1: 0.9
    beta2: 0.999
    eps: 1e-8
  
  # 梯度处理
  gradient_clipping: 1.0  # 梯度裁剪
  gradient_accumulation: 2  # 梯度累积

# 损失函数 - 激进的类别平衡策略
loss:
  # 主要损失组合
  use_ce: true
  use_dice: true
  use_focal: true
  use_lovasz: true
  use_tversky: true  # 新增Tversky损失
  
  # 损失权重 - 重新平衡
  weight_ce: 0.3  # 降低CE权重
  weight_dice: 1.5  # 增加Dice权重
  weight_focal: 2.0  # 大幅增加Focal权重
  weight_lovasz: 1.8
  weight_tversky: 1.2
  
  # Focal Loss参数 - 更激进
  focal_gamma: 3.0  # 增加gamma，更关注困难样本
  focal_alpha: 0.75  # 调整alpha
  
  # Tversky Loss参数
  tversky_alpha: 0.3  # 偏向召回率
  tversky_beta: 0.7
  
  # 类别权重策略 - 极端平衡
  use_class_weights: true
  class_weight_method: "extreme_balance"  # 新的极端平衡方法
  min_weight: 0.05
  max_weight: 20.0  # 大幅增加最大权重
  
  # 标签平滑
  label_smoothing: 0.1
  ignore_index: 255

# 类别特定优化
class_optimization:
  # 困难类别特殊处理
  difficult_classes: [8, 18, 28]
  
  # 类别特定损失权重
  class_specific_weights:
    8: 15.0   # 极高权重
    18: 12.0
    28: 10.0
    # 其他常见类别降低权重
    0: 0.1    # 背景类
    1: 0.5
    2: 0.8
  
  # 类别平衡采样
  balanced_sampling:
    enabled: true
    strategy: "sqrt_inv_freq"  # 平方根逆频率
    
# 验证和评估
evaluation:
  eval_period: 1
  metrics: ["miou", "pixel_accuracy", "class_accuracy", "dice", "precision", "recall"]
  
  # 早停策略
  early_stopping:
    enabled: true
    patience: 25  # 增加耐心
    monitor: "val_miou"
    min_delta: 0.001
    
  # 模型保存
  save_best_only: true
  save_top_k: 3

# 回调和监控
callbacks:
  # 学习率监控
  lr_monitor: true
  
  # 类别统计监控
  class_stats_monitor:
    enabled: true
    log_frequency: 5
    
  # 预测可视化
  prediction_visualization:
    enabled: true
    save_frequency: 10
    num_samples: 8

# 硬件优化
hardware:
  use_cuda: true
  mixed_precision: true
  benchmark_cudnn: true
  use_channels_last: true
  num_workers: 6
  pin_memory: true
  
  # 内存优化
  gradient_checkpointing: true  # 梯度检查点节省内存
  
# 日志配置
logging:
  log_dir: "logs/immediate_improvements"
  tensorboard: true
  save_predictions: true
  verbose: true
