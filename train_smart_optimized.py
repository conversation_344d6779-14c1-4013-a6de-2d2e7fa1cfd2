#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from datetime import datetime
import logging
import cv2
from torch.utils.data import Dataset

# 添加UNet_Demo路径
sys.path.append('UNet_Demo/UNet_Demo')

try:
    from train import unet
    from utils.utils_metrics import compute_mIoU_tensor
    from nets.unet_training import CE_Loss, Focal_Loss
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

def setup_logging():
    """设置日志"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f'logs/smart_optimized_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'{log_dir}/training.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return log_dir, logging.getLogger(__name__)

class SmartWeightStrategy:
    """智能权重策略 - 基于实际数据分布"""
    
    def __init__(self):
        # 基于测试结果的智能权重设计 (控制在60倍以内)
        self.smart_weights = {
            # 表现优秀但权重极低的类别 - 适度提升权重
            0: 0.1,   # 从0.01提升到0.1 (IoU: 0.3138)
            3: 0.1,   # 从0.01提升到0.1 (IoU: 0.8166)
            13: 0.1,  # 从0.01提升到0.1 (IoU: 0.4580)
            21: 0.2,  # 从0.05提升到0.2 (IoU: 0.3118)
            
            # 表现良好的类别 - 保持或微调权重
            1: 0.5,   # 保持 (IoU: 0.3769)
            4: 0.8,   # 保持 (IoU: 0.3554)
            6: 0.6,   # 保持 (IoU: 0.3273)
            12: 0.4,  # 保持 (IoU: 0.3688)
            16: 0.7,  # 保持 (IoU: 0.3944)
            20: 0.6,  # 保持 (IoU: 0.3316)
            
            # 表现优秀的类别 - 适度增加权重
            14: 2.0,  # 从0.3提升到2 (IoU: 0.6267)
            15: 10.0, # 从6提升到10 (IoU: 0.6753)
            25: 15.0, # 从10提升到15 (IoU: 0.5360)
            
            # 表现一般但有样本的类别 - 适度增加权重
            2: 0.3,   # 从0.05提升到0.3 (IoU: 0.2034)
            5: 8.0,   # 从15降到8 (IoU: 0.1762)
            7: 6.0,   # 从12降到6 (IoU: 0.1697)
            9: 5.0,   # 从10降到5 (IoU: 0.1637)
            11: 4.0,  # 从8降到4 (IoU: 0.2070)
            17: 3.0,  # 从5降到3 (IoU: 0.2701)
            19: 12.0, # 从20降到12 (IoU: 0.3000)
            24: 1.0,  # 从0.5提升到1 (IoU: 0.3027)
            
            # 样本少但需要学习的类别 - 中等权重
            10: 4.0,  # 从90大幅降到4 (IoU: 0.1352, 样本少)
            
            # 验证集无样本的类别 - 低权重但不放弃
            8: 2.0,   # 从100降到2 (无样本)
            18: 2.0,  # 从80降到2 (无样本)
            22: 2.0,  # 从15降到2 (无样本)
            23: 2.0,  # 从70降到2 (无样本)
            26: 2.0,  # 从60降到2 (无样本)
            27: 2.0,  # 从50降到2 (无样本)
            28: 2.0,  # 从50降到2 (无样本)
        }
        
        # 计算权重统计
        weights_array = np.array(list(self.smart_weights.values()))
        max_weight = weights_array.max()
        min_weight = weights_array.min()
        weight_ratio = max_weight / min_weight
        
        print(f"📊 智能权重策略统计:")
        print(f"  最高权重: {max_weight:.1f}x")
        print(f"  最低权重: {min_weight:.1f}x")
        print(f"  权重比例: {weight_ratio:.1f}倍 (目标: <60倍)")
        
    def get_weights(self, stage_multiplier=1.0):
        """获取当前阶段的权重"""
        class_weights = np.ones(29)
        for cls, weight in self.smart_weights.items():
            class_weights[cls] = weight * stage_multiplier
        return torch.FloatTensor(class_weights)

class EnhancedDiceLoss:
    """增强的Dice Loss实现"""
    
    def __init__(self, smooth=1e-6):
        self.smooth = smooth
    
    def __call__(self, outputs, targets):
        # Softmax归一化
        outputs = F.softmax(outputs, dim=1)
        
        # One-hot编码
        targets_one_hot = F.one_hot(targets, num_classes=29).permute(0, 3, 1, 2).float()
        
        # 计算Dice系数
        intersection = (outputs * targets_one_hot).sum(dim=(2, 3))
        union = outputs.sum(dim=(2, 3)) + targets_one_hot.sum(dim=(2, 3))
        
        dice = (2 * intersection + self.smooth) / (union + self.smooth)
        
        # 返回Dice Loss (1 - Dice)
        return 1 - dice.mean()

class SmartCombinedLoss:
    """智能组合损失函数: 40% Focal + 30% Dice + 30% CE"""
    
    def __init__(self, class_weights):
        self.class_weights = class_weights.cuda() if torch.cuda.is_available() else class_weights
        self.dice_loss = EnhancedDiceLoss()
        
    def __call__(self, outputs, targets):
        # 40% Focal Loss - 专注困难样本
        focal = Focal_Loss(outputs, targets, self.class_weights, 29, alpha=0.25, gamma=2.0)
        
        # 30% Dice Loss - 提升分割精度
        dice = self.dice_loss(outputs, targets)
        
        # 30% CE Loss - 基础分类损失
        ce = CE_Loss(outputs, targets, self.class_weights, 29)
        
        # 组合损失
        total_loss = 0.4 * focal + 0.3 * dice + 0.3 * ce
        
        return total_loss, {
            'focal': focal.item(),
            'dice': dice.item(),
            'ce': ce.item(),
            'total': total_loss.item()
        }

class TargetedAugmentationDataset(Dataset):
    """针对性数据增强数据集"""
    
    def __init__(self, annotation_lines, input_shape, num_classes, train, dataset_path):
        self.annotation_lines = annotation_lines
        self.length = len(annotation_lines)
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.train = train
        self.dataset_path = dataset_path
        
        # 表现差的类别需要3倍增强
        self.difficult_classes = [5, 7, 9, 10, 11, 17, 19, 2]
        self.augmentation_factor = 3 if train else 1
        
    def __len__(self):
        return self.length * self.augmentation_factor if self.train else self.length
    
    def apply_strong_augmentation(self, image, mask):
        """应用强化数据增强"""
        # 随机旋转
        if np.random.random() < 0.7:
            angle = np.random.uniform(-45, 45)
            h, w = image.shape[:2]
            center = (w//2, h//2)
            M = cv2.getRotationMatrix2D(center, angle, 1.0)
            image = cv2.warpAffine(image, M, (w, h))
            mask = cv2.warpAffine(mask, M, (w, h), flags=cv2.INTER_NEAREST)
        
        # 颜色增强
        if np.random.random() < 0.8:
            # 亮度调整
            brightness = np.random.uniform(0.7, 1.3)
            image = np.clip(image * brightness, 0, 255).astype(np.uint8)
            
            # 对比度调整
            contrast = np.random.uniform(0.7, 1.3)
            image = np.clip((image - 127.5) * contrast + 127.5, 0, 255).astype(np.uint8)
        
        # 高斯噪声
        if np.random.random() < 0.3:
            noise = np.random.normal(0, 10, image.shape).astype(np.uint8)
            image = np.clip(image + noise, 0, 255).astype(np.uint8)
        
        # 水平翻转
        if np.random.random() < 0.5:
            image = cv2.flip(image, 1)
            mask = cv2.flip(mask, 1)
            
        return image, mask
    
    def __getitem__(self, index):
        # 处理增强索引
        real_index = index % self.length
        is_augmented = index >= self.length and self.train
        
        annotation_line = self.annotation_lines[real_index]
        name = annotation_line.split()[0]
        
        # 读取图像和标签
        jpg_path = os.path.join(self.dataset_path, "VOC2025/JPEGImages", name + ".jpg")
        png_path = os.path.join(self.dataset_path, "VOC2025/SegmentationClass", name + ".png")
        
        image = cv2.imread(jpg_path)
        mask = cv2.imread(png_path, cv2.IMREAD_GRAYSCALE)
        
        # 调整大小
        image = cv2.resize(image, self.input_shape)
        mask = cv2.resize(mask, self.input_shape, interpolation=cv2.INTER_NEAREST)
        
        # 应用增强
        if is_augmented or (self.train and np.random.random() < 0.6):
            image, mask = self.apply_strong_augmentation(image, mask)
        
        # 转换为tensor
        image = torch.from_numpy(image.transpose(2, 0, 1)).float() / 255.0
        mask = torch.from_numpy(mask).long()
        
        return image, mask

class ProgressiveTrainer:
    """渐进式训练器"""
    
    def __init__(self, model, train_loader, val_loader, logger):
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.logger = logger
        self.weight_strategy = SmartWeightStrategy()
        
        # 渐进式训练配置
        self.stages = [
            {
                'name': 'Stage1_Gentle',
                'epochs': 50,
                'weight_multiplier': 0.5,  # 温和权重
                'lr': 2e-4,
                'description': '温和权重，整体学习'
            },
            {
                'name': 'Stage2_Normal', 
                'epochs': 50,
                'weight_multiplier': 1.0,  # 完整权重
                'lr': 1e-4,
                'description': '完整权重，困难类别关注'
            },
            {
                'name': 'Stage3_Enhanced',
                'epochs': 50,
                'weight_multiplier': 1.5,  # 强化权重
                'lr': 5e-5,
                'description': '强化权重，精细调优'
            }
        ]
        
        self.best_miou = 0
        self.global_epoch = 0
        
    def train_stage(self, stage_config):
        """训练单个阶段"""
        stage_name = stage_config['name']
        epochs = stage_config['epochs']
        weight_multiplier = stage_config['weight_multiplier']
        lr = stage_config['lr']
        
        print(f"\n🚀 开始 {stage_name}")
        print(f"📋 {stage_config['description']}")
        print(f"⚙️ 权重倍数: {weight_multiplier}x, 学习率: {lr}")
        
        # 获取当前阶段权重
        class_weights = self.weight_strategy.get_weights(weight_multiplier)
        criterion = SmartCombinedLoss(class_weights)
        
        # 创建优化器
        optimizer = optim.AdamW(self.model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs, eta_min=lr/10)
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        for epoch in range(epochs):
            self.global_epoch += 1
            
            # 训练阶段
            self.model.train()
            total_loss = 0
            loss_components = {'focal': 0, 'dice': 0, 'ce': 0}
            
            for batch_idx, (images, targets) in enumerate(self.train_loader):
                images, targets = images.to(device), targets.to(device)
                
                optimizer.zero_grad()
                outputs = self.model(images)
                
                loss, loss_dict = criterion(outputs, targets)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                optimizer.step()
                
                total_loss += loss.item()
                for key in loss_components:
                    loss_components[key] += loss_dict[key]
                
                if batch_idx % 10 == 0:
                    print(f"  {stage_name} Epoch {epoch+1}/{epochs}, Batch {batch_idx}, Loss: {loss.item():.4f}")
            
            scheduler.step()
            
            # 验证阶段
            avg_train_loss = total_loss / len(self.train_loader)
            avg_loss_components = {k: v / len(self.train_loader) for k, v in loss_components.items()}
            
            val_loss, val_miou = self.validate(criterion)
            
            # 记录结果
            current_lr = optimizer.param_groups[0]['lr']
            
            print(f"{stage_name} Epoch {epoch+1}/{epochs}:")
            print(f"  训练损失: {avg_train_loss:.4f} (Focal: {avg_loss_components['focal']:.4f}, Dice: {avg_loss_components['dice']:.4f}, CE: {avg_loss_components['ce']:.4f})")
            print(f"  验证损失: {val_loss:.4f}")
            print(f"  验证mIoU: {val_miou:.4f}")
            print(f"  学习率: {current_lr:.2e}")
            
            self.logger.info(f"Global_Epoch {self.global_epoch} ({stage_name}): train_loss={avg_train_loss:.4f}, val_loss={val_loss:.4f}, val_miou={val_miou:.4f}, lr={current_lr:.2e}")
            
            # 保存最佳模型
            if val_miou > self.best_miou:
                self.best_miou = val_miou
                
                torch.save({
                    'epoch': self.global_epoch,
                    'stage': stage_name,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'best_miou': self.best_miou,
                    'train_loss': avg_train_loss,
                    'val_loss': val_loss,
                    'loss_components': avg_loss_components,
                    'weight_multiplier': weight_multiplier
                }, f'best_smart_optimized_miou_{val_miou:.4f}.pth')
                
                print(f"🎉 新的最佳mIoU: {self.best_miou:.4f} ({stage_name})")
                self.logger.info(f"🎉 新的最佳mIoU: {self.best_miou:.4f} ({stage_name})")
                
                # 检查目标达成
                if self.best_miou >= 0.45:
                    print("🎯🎯🎯 恭喜！达到目标 mIoU ≥ 0.45！🎯🎯🎯")
                    self.logger.info("🎯 达到目标 mIoU ≥ 0.45")
                elif self.best_miou >= 0.40:
                    print("🎯 很好！mIoU ≥ 0.40，接近目标！")
                elif self.best_miou >= 0.35:
                    print("✅ 进展良好！mIoU ≥ 0.35")
        
        print(f"✅ {stage_name} 完成，当前最佳mIoU: {self.best_miou:.4f}")
        
    def validate(self, criterion):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        total_miou = 0
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        with torch.no_grad():
            for images, targets in self.val_loader:
                images, targets = images.to(device), targets.to(device)
                
                outputs = self.model(images)
                loss, _ = criterion(outputs, targets)
                
                pred = torch.argmax(outputs, dim=1)
                miou = compute_mIoU_tensor(pred, targets, num_classes=29)
                
                total_loss += loss.item()
                total_miou += miou
        
        return total_loss / len(self.val_loader), total_miou / len(self.val_loader)
    
    def train_all_stages(self):
        """执行完整的渐进式训练"""
        print("🎯 开始智能渐进式训练")
        print("=" * 60)
        
        for stage_config in self.stages:
            self.train_stage(stage_config)
        
        print(f"\n🎊 渐进式训练完成！")
        print(f"🏆 最终最佳mIoU: {self.best_miou:.4f}")
        
        # 计算改善程度
        baseline_miou = 0.2842  # 之前的最佳结果
        improvement = ((self.best_miou - baseline_miou) / baseline_miou) * 100
        
        print(f"📈 相比上次改善: {improvement:.1f}%")
        self.logger.info(f"📈 相比上次改善: {improvement:.1f}%")
        
        return self.best_miou

def main():
    """主函数"""
    print("🚀 启动智能优化训练")
    print("=" * 60)
    print("🎯 优化策略:")
    print("  ✅ 智能权重调整 (控制在60倍以内)")
    print("  ✅ 组合损失函数 (40% Focal + 30% Dice + 30% CE)")
    print("  ✅ 渐进式训练 (3阶段权重递增)")
    print("  ✅ 针对性数据增强 (困难类别3倍增强)")
    print("=" * 60)
    
    # 设置日志
    log_dir, logger = setup_logging()
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    model = unet(
        num_classes=29,
        backbone='resnet50',
        pretrained=True,
        dropout_rate=0.3,
        use_attention=True,
        attention_type='cbam'
    ).to(device)
    
    # 准备数据
    VOCdevkit_path = "UNet_Demo/UNet_Demo/VOCdevkit"
    
    with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/train.txt"), "r") as f:
        train_lines = f.readlines()
    with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/val.txt"), "r") as f:
        val_lines = f.readlines()
    
    print(f"📊 数据集信息:")
    print(f"  训练集样本: {len(train_lines)}")
    print(f"  验证集样本: {len(val_lines)}")
    
    # 创建增强数据集
    train_dataset = TargetedAugmentationDataset(
        train_lines, (512, 512), 29, True, VOCdevkit_path
    )
    val_dataset = TargetedAugmentationDataset(
        val_lines, (512, 512), 29, False, VOCdevkit_path
    )
    
    print(f"  增强后训练集: {len(train_dataset)} (3倍增强)")
    
    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=4, shuffle=True, num_workers=0, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=4, shuffle=False, num_workers=0, pin_memory=True
    )
    
    # 开始渐进式训练
    trainer = ProgressiveTrainer(model, train_loader, val_loader, logger)
    final_miou = trainer.train_all_stages()
    
    # 最终评估
    target_miou = 0.45
    progress = (final_miou / target_miou) * 100
    
    print(f"\n🎯 最终评估:")
    print(f"  目标mIoU: {target_miou:.4f}")
    print(f"  实际mIoU: {final_miou:.4f}")
    print(f"  目标完成度: {progress:.1f}%")
    
    if final_miou >= 0.45:
        print("🏆🏆🏆 恭喜！成功达到目标！🏆🏆🏆")
    elif final_miou >= 0.40:
        print("🎯 接近目标，继续优化！")
    elif final_miou >= 0.35:
        print("✅ 显著改善，继续努力！")
    else:
        print("💪 有进步，需要进一步优化")

if __name__ == "__main__":
    main()
