#!/usr/bin/env python3
"""
简化版高级训练脚本
基于现有的训练框架，添加优化功能
"""

import os
import sys
import yaml
import torch
import argparse
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 使用现有的训练函数
from train import main as original_train

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def optimize_config(config_path, output_path):
    """优化配置文件"""
    logger = logging.getLogger(__name__)
    
    # 加载原始配置
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 应用优化设置
    optimizations = {
        # 针对RTX 4070 Ti Super 16GB的批次大小优化
        'train': {
            'freeze_batch_size': 12,
            'unfreeze_batch_size': 6,
            'num_workers': 8,
            'mixed_precision': True,
            'gradient_accumulation': 4,
            'benchmark_cudnn': True,
        },
        
        # 优化的损失函数权重
        'loss': {
            'weight_ce': 0.6,
            'weight_dice': 1.2,
            'weight_focal': 0.8,
            'weight_lovasz': 1.8,
            'use_class_weights': True,
            'class_weight_method': 'inverse_frequency',
            'focal_gamma': 3.0,
            'focal_alpha': 0.6,
            'label_smoothing': 0.1,
        },
        
        # 学习率优化
        'scheduler': {
            'type': 'cosine_warmup',
            'max_lr': 0.002,
            'min_lr': 5e-6,
            'warmup_epochs': 10,
        },
        
        # 模型优化
        'model': {
            'dropout_rate': 0.25,
            'use_attention': True,
            'attention_type': 'cbam',
        }
    }
    
    # 递归更新配置
    def update_dict(d, u):
        for k, v in u.items():
            if isinstance(v, dict):
                d[k] = update_dict(d.get(k, {}), v)
            else:
                d[k] = v
        return d
    
    config = update_dict(config, optimizations)
    
    # 保存优化后的配置
    with open(output_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    logger.info(f"优化配置已保存至: {output_path}")
    return config

def start_gpu_monitor():
    """启动GPU监控"""
    try:
        import subprocess
        subprocess.Popen(['python', 'gpu_memory_monitor.py', '--optimize', '--interval', '2'])
        print("GPU监控已启动")
    except Exception as e:
        print(f"启动GPU监控失败: {e}")

def start_tensorboard(log_dir):
    """启动TensorBoard"""
    try:
        import subprocess
        subprocess.Popen(['tensorboard', '--logdir', log_dir, '--port', '6006'])
        print("TensorBoard已启动，访问地址: http://localhost:6006")
    except Exception as e:
        print(f"启动TensorBoard失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='简化版高级U-net训练')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--epochs', type=int, default=None, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=None, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=None, help='学习率')
    parser.add_argument('--backbone', type=str, default=None, help='骨干网络')
    parser.add_argument('--tensorboard', action='store_true', help='启动TensorBoard')
    parser.add_argument('--gpu_monitor', action='store_true', help='启动GPU监控')
    parser.add_argument('--optimize', action='store_true', help='使用优化配置')
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    logger.info("开始简化版高级训练")
    
    # 创建优化配置
    if args.optimize:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        optimized_config_path = f'config_optimized_{timestamp}.yaml'
        config = optimize_config(args.config, optimized_config_path)
        config_path = optimized_config_path
    else:
        config_path = args.config
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    
    # 应用命令行参数
    if args.epochs:
        config['train']['total_epochs'] = args.epochs
    if args.batch_size:
        config['train']['freeze_batch_size'] = args.batch_size
        config['train']['unfreeze_batch_size'] = max(2, args.batch_size // 2)
    if args.learning_rate:
        config['train']['init_lr'] = args.learning_rate
    if args.backbone:
        config['model']['backbone'] = args.backbone
    
    # 保存最终配置
    final_config_path = f'config_final_{datetime.now().strftime("%Y%m%d_%H%M%S")}.yaml'
    with open(final_config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    # 启动监控工具
    if args.gpu_monitor:
        start_gpu_monitor()
    
    if args.tensorboard:
        log_dir = config.get('train', {}).get('save_dir', 'logs')
        start_tensorboard(log_dir)
    
    # 显示配置信息
    print("=" * 60)
    print("训练配置信息")
    print("=" * 60)
    print(f"配置文件: {final_config_path}")
    print(f"骨干网络: {config['model']['backbone']}")
    print(f"训练轮数: {config['train']['total_epochs']}")
    print(f"批次大小: {config['train']['freeze_batch_size']} (冻结) / {config['train']['unfreeze_batch_size']} (解冻)")
    print(f"学习率: {config['train']['init_lr']}")
    print(f"混合精度: {config['train'].get('mixed_precision', False)}")
    print(f"类别权重: {config['loss'].get('use_class_weights', False)}")
    print("=" * 60)
    
    # 设置环境变量
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
    
    # 调用原始训练函数
    try:
        # 修改sys.argv以传递配置文件
        original_argv = sys.argv.copy()
        sys.argv = ['train.py', '--cfg', final_config_path]
        
        logger.info("开始训练...")
        original_train()
        
        logger.info("训练完成!")
        
    except Exception as e:
        logger.error(f"训练失败: {e}")
        raise
    finally:
        # 恢复原始argv
        sys.argv = original_argv
    
    print("=" * 60)
    print("训练完成!")
    print("=" * 60)
    if args.tensorboard:
        print("TensorBoard: http://localhost:6006")
    print(f"配置文件: {final_config_path}")
    print("=" * 60)

if __name__ == '__main__':
    main()
