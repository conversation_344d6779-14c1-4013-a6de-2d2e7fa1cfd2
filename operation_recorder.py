#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
操作录制器 - 录制手动操作并生成自动化脚本
功能：
1. 录制鼠标点击、键盘输入
2. 截图保存关键步骤
3. 生成可重复执行的自动化脚本
4. 智能识别页面元素
"""

import time
import json
import os
from datetime import datetime
import threading
import keyboard
import mouse
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.options import Options
from selenium.webdriver.edge.service import Service
import logging

class OperationRecorder:
    """操作录制器"""
    
    def __init__(self):
        self.recording = False
        self.operations = []
        self.driver = None
        self.wait = None
        self.start_time = None
        self.screenshot_count = 0
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        log_filename = f"operation_recorder_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_browser(self):
        """设置浏览器"""
        try:
            edge_options = Options()
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-dev-shm-usage')
            
            # 使用已下载的EdgeDriver
            if os.path.exists("msedgedriver.exe"):
                service = Service("msedgedriver.exe")
                self.driver = webdriver.Edge(service=service, options=edge_options)
            else:
                self.driver = webdriver.Edge(options=edge_options)
            
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, 20)
            
            self.logger.info("浏览器设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器设置失败: {e}")
            return False
    
    def start_recording(self, url):
        """开始录制"""
        print("🎬 开始录制操作...")
        print("📋 操作说明：")
        print("  - 按 F9 开始/暂停录制")
        print("  - 按 F10 保存截图")
        print("  - 按 F11 添加注释")
        print("  - 按 F12 停止录制")
        print("  - 按 ESC 紧急退出")
        
        # 设置浏览器
        if not self.setup_browser():
            return False
        
        # 打开页面
        self.driver.get(url)
        time.sleep(3)
        
        # 设置快捷键
        self.setup_hotkeys()
        
        # 开始录制
        self.recording = True
        self.start_time = time.time()
        
        # 初始截图
        self.take_screenshot("initial_page")
        
        print("\n🔴 录制已开始！请在浏览器中进行操作...")
        print("完成后按 F12 停止录制")
        
        # 监听操作
        self.listen_operations()
        
        return True
    
    def setup_hotkeys(self):
        """设置快捷键"""
        keyboard.add_hotkey('f9', self.toggle_recording)
        keyboard.add_hotkey('f10', self.manual_screenshot)
        keyboard.add_hotkey('f11', self.add_comment)
        keyboard.add_hotkey('f12', self.stop_recording)
        keyboard.add_hotkey('esc', self.emergency_exit)
    
    def listen_operations(self):
        """监听操作"""
        try:
            # 监听鼠标事件
            mouse.on_click(self.on_mouse_click)
            
            # 保持录制状态
            while self.recording:
                time.sleep(0.1)
                
        except Exception as e:
            self.logger.error(f"监听操作失败: {e}")
    
    def on_mouse_click(self):
        """鼠标点击事件"""
        if not self.recording:
            return
        
        try:
            # 获取鼠标位置
            x, y = mouse.get_position()
            
            # 获取当前时间戳
            timestamp = time.time() - self.start_time
            
            # 尝试获取点击的元素信息
            element_info = self.get_element_at_position(x, y)
            
            # 记录操作
            operation = {
                'type': 'click',
                'timestamp': timestamp,
                'position': {'x': x, 'y': y},
                'element': element_info,
                'screenshot': f"click_{len(self.operations)}.png"
            }
            
            self.operations.append(operation)
            
            # 截图
            self.take_screenshot(f"click_{len(self.operations)}")
            
            self.logger.info(f"记录点击: ({x}, {y}) - {element_info.get('text', 'Unknown')}")
            
        except Exception as e:
            self.logger.error(f"记录鼠标点击失败: {e}")
    
    def get_element_at_position(self, x, y):
        """获取指定位置的元素信息"""
        try:
            # 使用JavaScript获取元素
            script = f"""
            var element = document.elementFromPoint({x}, {y});
            if (element) {{
                return {{
                    'tagName': element.tagName,
                    'id': element.id,
                    'className': element.className,
                    'text': element.textContent.trim().substring(0, 50),
                    'xpath': getXPath(element)
                }};
            }}
            return null;
            
            function getXPath(element) {{
                if (element.id !== '') {{
                    return "//*[@id='" + element.id + "']";
                }}
                if (element === document.body) {{
                    return '/html/body';
                }}
                var ix = 0;
                var siblings = element.parentNode.childNodes;
                for (var i = 0; i < siblings.length; i++) {{
                    var sibling = siblings[i];
                    if (sibling === element) {{
                        return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                    }}
                    if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {{
                        ix++;
                    }}
                }}
            }}
            """
            
            result = self.driver.execute_script(script)
            return result or {}
            
        except Exception as e:
            self.logger.error(f"获取元素信息失败: {e}")
            return {}
    
    def take_screenshot(self, name):
        """截图"""
        try:
            filename = f"screenshot_{name}_{datetime.now().strftime('%H%M%S')}.png"
            self.driver.save_screenshot(filename)
            self.screenshot_count += 1
            self.logger.info(f"截图保存: {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"截图失败: {e}")
            return None
    
    def toggle_recording(self):
        """切换录制状态"""
        self.recording = not self.recording
        status = "开始" if self.recording else "暂停"
        print(f"\n🎬 录制{status}")
        self.logger.info(f"录制状态: {status}")
    
    def manual_screenshot(self):
        """手动截图"""
        if self.driver:
            filename = self.take_screenshot("manual")
            print(f"\n📸 手动截图: {filename}")
    
    def add_comment(self):
        """添加注释"""
        comment = input("\n💬 请输入注释: ")
        if comment:
            operation = {
                'type': 'comment',
                'timestamp': time.time() - self.start_time,
                'content': comment
            }
            self.operations.append(operation)
            self.logger.info(f"添加注释: {comment}")
    
    def stop_recording(self):
        """停止录制"""
        self.recording = False
        print("\n⏹️ 录制已停止")
        self.save_recording()
        self.generate_script()
    
    def emergency_exit(self):
        """紧急退出"""
        self.recording = False
        print("\n🚨 紧急退出")
        if self.driver:
            self.driver.quit()
        exit()
    
    def save_recording(self):
        """保存录制数据"""
        try:
            filename = f"recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            recording_data = {
                'metadata': {
                    'start_time': self.start_time,
                    'duration': time.time() - self.start_time,
                    'operation_count': len(self.operations),
                    'screenshot_count': self.screenshot_count
                },
                'operations': self.operations
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(recording_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"录制数据保存: {filename}")
            print(f"📁 录制数据已保存: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存录制数据失败: {e}")
    
    def generate_script(self):
        """生成自动化脚本"""
        try:
            script_content = self.create_automation_script()
            
            filename = f"generated_script_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            self.logger.info(f"自动化脚本生成: {filename}")
            print(f"🤖 自动化脚本已生成: {filename}")
            
        except Exception as e:
            self.logger.error(f"生成脚本失败: {e}")
    
    def create_automation_script(self):
        """创建自动化脚本内容"""
        script_template = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动生成的问卷星操作脚本
基于录制的操作序列生成
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.options import Options
from selenium.webdriver.edge.service import Service

class GeneratedAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
    
    def setup_browser(self):
        """设置浏览器"""
        edge_options = Options()
        edge_options.add_argument('--no-sandbox')
        edge_options.add_argument('--disable-dev-shm-usage')
        
        if os.path.exists("msedgedriver.exe"):
            service = Service("msedgedriver.exe")
            self.driver = webdriver.Edge(service=service, options=edge_options)
        else:
            self.driver = webdriver.Edge(options=edge_options)
        
        self.driver.maximize_window()
        self.wait = WebDriverWait(self.driver, 20)
    
    def execute_operations(self):
        """执行录制的操作"""
        try:
{operations_code}
        except Exception as e:
            print(f"执行操作失败: {{e}}")
        finally:
            if self.driver:
                self.driver.quit()
    
    def safe_click(self, xpath, timeout=10):
        """安全点击元素"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((By.XPATH, xpath))
            )
            element.click()
            return True
        except:
            return False
    
    def safe_input(self, xpath, text, timeout=10):
        """安全输入文本"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            element.clear()
            element.send_keys(text)
            return True
        except:
            return False

def main():
    automation = GeneratedAutomation()
    automation.setup_browser()
    automation.execute_operations()

if __name__ == "__main__":
    main()
'''
        
        # 生成操作代码
        operations_code = ""
        for i, op in enumerate(self.operations):
            if op['type'] == 'click':
                element = op.get('element', {})
                xpath = element.get('xpath', '')
                text = element.get('text', '')
                
                operations_code += f'''
            # 操作 {i+1}: 点击 "{text}"
            print("执行点击操作: {text}")
            if self.safe_click("{xpath}"):
                print("✅ 点击成功")
            else:
                print("❌ 点击失败")
            time.sleep(2)
'''
            elif op['type'] == 'comment':
                operations_code += f'''
            # 注释: {op['content']}
            print("📝 {op['content']}")
'''
        
        return script_template.format(operations_code=operations_code)

def main():
    """主函数"""
    print("🎬 问卷星操作录制器")
    print("=" * 50)
    
    url = "https://www.wjx.cn/wjxdesignnew/designnew.aspx?version=7&openType=redesign&curid=316618829&nqt=&sguid=8d03ce0d-b368-47b9-8646-e129a13932f2"
    
    print(f"🌐 目标页面: {url}")
    print("\n📋 使用说明:")
    print("1. 脚本将打开浏览器并导航到问卷编辑页面")
    print("2. 请手动完成登录（如需要）")
    print("3. 按 F9 开始录制")
    print("4. 手动创建一个完整的循环评价题目")
    print("5. 按 F12 停止录制并生成脚本")
    
    confirm = input("\n确认开始录制? (y/n): ").lower()
    if confirm != 'y':
        print("录制已取消")
        return
    
    recorder = OperationRecorder()
    recorder.start_recording(url)

if __name__ == "__main__":
    main()
