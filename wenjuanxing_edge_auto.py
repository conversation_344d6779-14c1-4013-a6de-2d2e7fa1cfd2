#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问卷星自动上传工具 - Edge浏览器专用版本
解决驱动问题，实现完全自动化
"""

import os
import time
import glob
import subprocess
import requests
import zipfile
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
from datetime import datetime

class EdgeDriverManager:
    """Edge浏览器驱动管理器"""
    
    def __init__(self):
        self.driver_path = None
        self.logger = logging.getLogger(__name__)
    
    def get_edge_version(self):
        """获取Edge浏览器版本"""
        try:
            # 方法1: 通过注册表获取
            import winreg
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Edge\BLBeacon")
            version = winreg.QueryValueEx(key, "version")[0]
            winreg.<PERSON>Key(key)
            return version
        except:
            try:
                # 方法2: 通过命令行获取
                result = subprocess.run([
                    r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe", "--version"
                ], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version = result.stdout.strip().split()[-1]
                    return version
            except:
                pass
        
        # 方法3: 默认版本
        return "131.0.2903.86"  # 常见版本
    
    def download_edge_driver(self, version):
        """下载Edge驱动"""
        try:
            # 获取主版本号
            major_version = version.split('.')[0]
            
            # 构建下载URL
            download_url = f"https://msedgedriver.azureedge.net/{version}/edgedriver_win64.zip"
            
            self.logger.info(f"正在下载Edge驱动，版本: {version}")
            
            # 下载驱动
            response = requests.get(download_url, timeout=30)
            if response.status_code == 200:
                # 保存到临时文件
                zip_path = "edgedriver.zip"
                with open(zip_path, 'wb') as f:
                    f.write(response.content)
                
                # 解压
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(".")
                
                # 清理
                os.remove(zip_path)
                
                # 设置驱动路径
                self.driver_path = os.path.abspath("msedgedriver.exe")
                self.logger.info(f"Edge驱动下载成功: {self.driver_path}")
                return True
            else:
                self.logger.error(f"下载失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"下载Edge驱动失败: {e}")
            return False
    
    def setup_driver(self):
        """设置Edge驱动"""
        try:
            # 检查是否已存在驱动
            if os.path.exists("msedgedriver.exe"):
                self.driver_path = os.path.abspath("msedgedriver.exe")
                self.logger.info("使用已存在的Edge驱动")
                return True
            
            # 获取Edge版本
            version = self.get_edge_version()
            self.logger.info(f"检测到Edge版本: {version}")
            
            # 下载对应版本的驱动
            return self.download_edge_driver(version)
            
        except Exception as e:
            self.logger.error(f"设置Edge驱动失败: {e}")
            return False

class WenjuanxingEdgeUploader:
    """问卷星Edge自动上传器"""
    
    def __init__(self, image_folder_path, questionnaire_url):
        self.image_folder = image_folder_path
        self.questionnaire_url = questionnaire_url
        self.driver = None
        self.wait = None
        self.images = []
        self.driver_manager = EdgeDriverManager()
        
        # 设置日志
        self.setup_logging()
        
        # 加载图片
        self.load_images()
    
    def setup_logging(self):
        """设置日志"""
        log_filename = f"wenjuanxing_edge_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_images(self):
        """加载图片列表"""
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif']
        
        for ext in image_extensions:
            pattern = os.path.join(self.image_folder, ext)
            self.images.extend(glob.glob(pattern))
            pattern_upper = os.path.join(self.image_folder, ext.upper())
            self.images.extend(glob.glob(pattern_upper))
        
        self.images.sort()
        self.logger.info(f"找到 {len(self.images)} 张图片")
        
        # 打印前10张图片
        for i, img in enumerate(self.images[:10]):
            self.logger.info(f"图片 {i+1}: {os.path.basename(img)}")
    
    def setup_edge_browser(self):
        """设置Edge浏览器"""
        try:
            # 设置Edge驱动
            if not self.driver_manager.setup_driver():
                self.logger.error("Edge驱动设置失败")
                return False
            
            # Edge选项
            from selenium.webdriver.edge.options import Options
            from selenium.webdriver.edge.service import Service
            
            edge_options = Options()
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-dev-shm-usage')
            edge_options.add_argument('--disable-blink-features=AutomationControlled')
            edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            edge_options.add_experimental_option('useAutomationExtension', False)
            
            # 设置下载和通知选项
            prefs = {
                "profile.default_content_settings.popups": 0,
                "profile.default_content_setting_values.notifications": 2
            }
            edge_options.add_experimental_option("prefs", prefs)
            
            # 创建服务
            service = Service(self.driver_manager.driver_path)
            
            # 创建驱动
            self.driver = webdriver.Edge(service=service, options=edge_options)
            
            # 隐藏自动化标识
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置等待和窗口
            self.wait = WebDriverWait(self.driver, 20)
            self.driver.maximize_window()
            
            self.logger.info("Edge浏览器设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"设置Edge浏览器失败: {e}")
            return False
    
    def navigate_to_questionnaire(self):
        """导航到问卷编辑页面"""
        try:
            self.logger.info("正在打开问卷编辑页面...")
            self.driver.get(self.questionnaire_url)
            time.sleep(5)
            
            # 检查是否需要登录
            if "login" in self.driver.current_url.lower() or "登录" in self.driver.title:
                self.logger.info("需要手动登录...")
                print("\n🔐 检测到需要登录")
                print("请在Edge浏览器中完成登录...")
                input("登录完成后，按回车键继续...")
                
                # 重新导航
                self.driver.get(self.questionnaire_url)
                time.sleep(3)
            
            self.logger.info("成功进入问卷编辑页面")
            return True
            
        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            return False
    
    def find_element_by_multiple_selectors(self, selectors, timeout=10):
        """通过多个选择器查找元素"""
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                return element
            except TimeoutException:
                continue
        return None
    
    def create_loop_evaluation_question(self, question_number, image_paths):
        """创建循环评价题目"""
        try:
            self.logger.info(f"开始创建第 {question_number} 题...")
            
            # 1. 点击添加题目
            add_selectors = [
                "//span[contains(text(), '添加题目')]",
                "//button[contains(text(), '添加题目')]",
                "//div[contains(text(), '添加题目')]",
                "//a[contains(text(), '添加题目')]",
                "//*[contains(@class, 'add-question')]"
            ]
            
            add_btn = self.find_element_by_multiple_selectors(add_selectors)
            if not add_btn:
                raise Exception("找不到添加题目按钮")
            
            add_btn.click()
            time.sleep(3)
            
            # 2. 选择循环评价
            loop_selectors = [
                "//div[contains(text(), '循环评价')]",
                "//span[contains(text(), '循环评价')]",
                "//li[contains(text(), '循环评价')]",
                "//*[contains(@title, '循环评价')]"
            ]
            
            loop_btn = self.find_element_by_multiple_selectors(loop_selectors)
            if loop_btn:
                loop_btn.click()
                time.sleep(3)
                self.logger.info("✅ 选择循环评价题型成功")
            
            # 3. 设置题目标题
            title_selectors = [
                "//input[@placeholder='请输入题目']",
                "//input[contains(@placeholder, '题目')]",
                "//textarea[contains(@placeholder, '题目')]"
            ]
            
            title_input = self.find_element_by_multiple_selectors(title_selectors)
            if title_input:
                title_input.clear()
                title_input.send_keys(f"图片评价题目 {question_number}")
                self.logger.info("✅ 设置题目标题成功")
            
            # 4. 评价对象设置
            object_selectors = [
                "//span[contains(text(), '评价对象设置')]",
                "//button[contains(text(), '评价对象')]",
                "//div[contains(text(), '评价对象')]"
            ]
            
            object_btn = self.find_element_by_multiple_selectors(object_selectors)
            if object_btn:
                object_btn.click()
                time.sleep(3)
                self.logger.info("✅ 进入评价对象设置")
            
            # 5. 选择图片类型
            image_type_selectors = [
                "//div[contains(text(), '图片')]",
                "//span[contains(text(), '图片')]",
                "//li[contains(text(), '图片')]"
            ]
            
            image_btn = self.find_element_by_multiple_selectors(image_type_selectors)
            if image_btn:
                image_btn.click()
                time.sleep(3)
                self.logger.info("✅ 选择图片类型成功")
            
            # 6. 上传图片
            self.upload_images_for_question(image_paths)
            
            # 7. 添加评价选项
            self.add_evaluation_options()
            
            # 8. 保存题目
            save_selectors = [
                "//span[contains(text(), '保存')]",
                "//button[contains(text(), '保存')]",
                "//div[contains(text(), '保存')]"
            ]
            
            save_btn = self.find_element_by_multiple_selectors(save_selectors)
            if save_btn:
                save_btn.click()
                time.sleep(3)
                self.logger.info("✅ 保存题目成功")
            
            self.logger.info(f"第 {question_number} 题创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"创建第 {question_number} 题失败: {e}")
            # 截图保存错误状态
            try:
                screenshot_path = f"error_edge_q{question_number}_{datetime.now().strftime('%H%M%S')}.png"
                self.driver.save_screenshot(screenshot_path)
                self.logger.info(f"错误截图已保存: {screenshot_path}")
            except:
                pass
            return False
    
    def upload_images_for_question(self, image_paths):
        """为题目上传图片"""
        try:
            self.logger.info(f"开始上传 {len(image_paths)} 张图片...")
            
            for i, image_path in enumerate(image_paths):
                self.logger.info(f"上传第 {i+1} 张图片: {os.path.basename(image_path)}")
                
                # 查找上传按钮
                upload_selectors = [
                    "//span[contains(text(), '上传图片')]",
                    "//button[contains(text(), '上传')]",
                    "//div[contains(text(), '上传')]",
                    "//*[contains(@class, 'upload')]"
                ]
                
                upload_btn = self.find_element_by_multiple_selectors(upload_selectors)
                if upload_btn:
                    upload_btn.click()
                    time.sleep(2)
                    
                    # 查找文件输入
                    file_inputs = self.driver.find_elements(By.XPATH, "//input[@type='file']")
                    if file_inputs:
                        file_inputs[0].send_keys(image_path)
                        time.sleep(3)
                        
                        # 确认上传
                        confirm_btns = self.driver.find_elements(By.XPATH, "//span[contains(text(), '确定')]")
                        if confirm_btns:
                            confirm_btns[0].click()
                            time.sleep(2)
                        
                        self.logger.info(f"✅ 图片 {i+1} 上传成功")
                    else:
                        self.logger.warning(f"⚠️ 找不到文件输入框")
                else:
                    self.logger.warning(f"⚠️ 找不到上传按钮")
            
            self.logger.info("图片上传完成")
            
        except Exception as e:
            self.logger.error(f"上传图片失败: {e}")
    
    def add_evaluation_options(self):
        """添加评价选项"""
        try:
            self.logger.info("添加评价选项...")
            
            options = ["非常不满意", "不满意", "一般", "满意", "非常满意"]
            
            for i, option in enumerate(options):
                # 查找添加选项按钮
                add_option_selectors = [
                    "//span[contains(text(), '添加选项')]",
                    "//button[contains(text(), '添加选项')]",
                    "//div[contains(text(), '添加选项')]"
                ]
                
                add_option_btn = self.find_element_by_multiple_selectors(add_option_selectors)
                if add_option_btn:
                    add_option_btn.click()
                    time.sleep(1)
                    
                    # 输入选项内容
                    option_inputs = self.driver.find_elements(By.XPATH, f"//input[@placeholder='选项{i+1}']")
                    if not option_inputs:
                        option_inputs = self.driver.find_elements(By.XPATH, "//input[contains(@placeholder, '选项')]")
                    
                    if option_inputs:
                        option_inputs[-1].clear()  # 使用最后一个输入框
                        option_inputs[-1].send_keys(option)
                        time.sleep(1)
            
            self.logger.info("评价选项添加完成")
            
        except Exception as e:
            self.logger.error(f"添加评价选项失败: {e}")
    
    def create_questionnaire(self, questions_count=30):
        """创建完整问卷"""
        try:
            self.logger.info(f"开始创建问卷，共 {questions_count} 题")
            
            images_per_question = 10
            total_images_needed = questions_count * images_per_question
            
            if len(self.images) < total_images_needed:
                self.logger.error(f"图片数量不足，需要 {total_images_needed} 张，实际 {len(self.images)} 张")
                return False
            
            # 创建题目
            for question_num in range(1, questions_count + 1):
                start_idx = (question_num - 1) * images_per_question
                end_idx = start_idx + images_per_question
                question_images = self.images[start_idx:end_idx]
                
                success = self.create_loop_evaluation_question(question_num, question_images)
                if not success:
                    self.logger.error(f"创建第 {question_num} 题失败")
                    user_choice = input(f"第 {question_num} 题创建失败，是否继续？(y/n): ")
                    if user_choice.lower() != 'y':
                        return False
                
                # 每5题休息一下
                if question_num % 5 == 0:
                    self.logger.info(f"已完成 {question_num} 题，休息5秒...")
                    time.sleep(5)
            
            self.logger.info("问卷创建完成！")
            return True
            
        except Exception as e:
            self.logger.error(f"创建问卷失败: {e}")
            return False
    
    def run(self):
        """运行主程序"""
        try:
            self.logger.info("开始执行问卷星Edge自动上传任务")
            
            # 1. 设置Edge浏览器
            if not self.setup_edge_browser():
                return False
            
            # 2. 导航到问卷页面
            if not self.navigate_to_questionnaire():
                return False
            
            # 3. 创建问卷
            success = self.create_questionnaire(30)
            
            if success:
                self.logger.info("🎉 任务执行成功！")
                print("\n🎉 任务执行成功！")
                input("任务完成，按回车键关闭浏览器...")
            else:
                self.logger.error("❌ 任务执行失败")
                print("\n❌ 任务执行失败")
                input("任务失败，按回车键关闭浏览器...")
            
            return success
            
        except Exception as e:
            self.logger.error(f"执行任务时发生错误: {e}")
            return False
        
        finally:
            if self.driver:
                self.driver.quit()
                self.logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("🚀 问卷星Edge自动上传工具")
    print("=" * 50)
    
    # 配置参数
    image_folder = r"D:\1a_taohuacun"
    questionnaire_url = "https://www.wjx.cn/wjxdesignnew/designnew.aspx?version=7&openType=redesign&curid=316618829&nqt=&sguid=8d03ce0d-b368-47b9-8646-e129a13932f2"
    
    # 检查图片文件夹
    if not os.path.exists(image_folder):
        print(f"❌ 图片文件夹不存在: {image_folder}")
        return
    
    print(f"📁 图片文件夹: {image_folder}")
    print(f"🌐 问卷地址: {questionnaire_url}")
    print(f"🌐 使用浏览器: Microsoft Edge")
    print(f"📋 任务: 创建30题循环评价，每题10张图片")
    
    # 确认执行
    confirm = input("\n确认开始执行? (y/n): ").lower()
    if confirm != 'y':
        print("任务已取消")
        return
    
    # 创建上传器并执行
    uploader = WenjuanxingEdgeUploader(image_folder, questionnaire_url)
    success = uploader.run()
    
    if success:
        print("🎉 任务执行成功！")
    else:
        print("❌ 任务执行失败，请查看日志文件")

if __name__ == "__main__":
    main()
