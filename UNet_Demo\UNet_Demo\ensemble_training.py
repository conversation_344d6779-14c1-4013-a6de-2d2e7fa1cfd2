#!/usr/bin/env python3
"""
集成训练脚本 - 训练多个模型进行集成预测
通过不同的初始化、数据增强和超参数训练多个模型，然后集成预测
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import numpy as np
from datetime import datetime
import logging
from pathlib import Path
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.unet import Unet
from utils.dataloader import SegmentationDataset
from utils.losses import CombinedLoss
from utils.class_weights import compute_class_weights
from torch.utils.data import DataLoader
import torch.optim as optim

class EnsembleTrainer:
    """集成训练器"""
    
    def __init__(self, base_config_path, num_models=3):
        self.base_config_path = base_config_path
        self.num_models = num_models
        self.models = []
        self.model_configs = []
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        log_dir = f"logs/ensemble_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(log_dir, exist_ok=True)
        self.log_dir = log_dir
        
        self.logger = logging.getLogger('ensemble_trainer')
        self.logger.setLevel(logging.INFO)
        
        # 清除已有handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # 文件handler
        log_file = os.path.join(log_dir, 'ensemble_training.log')
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def create_model_variants(self):
        """创建不同的模型变体配置"""
        with open(self.base_config_path, 'r', encoding='utf-8') as f:
            base_config = yaml.safe_load(f)
        
        variants = []
        
        # 变体1：标准配置，重点关注Focal Loss
        config1 = base_config.copy()
        config1['loss']['weight_focal'] = 2.5
        config1['loss']['focal_gamma'] = 3.0
        config1['loss']['weight_dice'] = 1.2
        config1['training']['learning_rate']['init_lr'] = 0.002
        variants.append(('focal_focused', config1))
        
        # 变体2：重点关注Dice Loss和Tversky Loss
        config2 = base_config.copy()
        config2['loss']['weight_dice'] = 2.0
        config2['loss']['weight_tversky'] = 1.8
        config2['loss']['weight_focal'] = 1.5
        config2['training']['learning_rate']['init_lr'] = 0.0015
        variants.append(('dice_tversky_focused', config2))
        
        # 变体3：极端类别权重配置
        config3 = base_config.copy()
        config3['loss']['class_weight_method'] = 'extreme_balance'
        config3['class_optimization']['class_specific_weights'] = {
            8: 30.0, 18: 25.0, 28: 20.0,  # 极高权重给困难类别
            0: 0.05,  # 背景类极低权重
        }
        config3['training']['learning_rate']['init_lr'] = 0.001
        variants.append(('extreme_weights', config3))
        
        return variants
    
    def train_single_model(self, model_name, config, model_index):
        """训练单个模型"""
        self.logger.info(f"开始训练模型 {model_index+1}/{self.num_models}: {model_name}")
        
        # 设置设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建模型
        model = Unet(
            num_classes=config['data']['num_classes'],
            pretrained=config['model']['pretrained'],
            backbone=config['model']['backbone']
        ).to(device)
        
        # 创建数据加载器
        train_dataset = SegmentationDataset(
            annotation_path=config['data']['train_annotation_path'],
            input_shape=config['data']['input_shape'][:2],
            num_classes=config['data']['num_classes'],
            train=True
        )
        
        val_dataset = SegmentationDataset(
            annotation_path=config['data']['val_annotation_path'],
            input_shape=config['data']['input_shape'][:2],
            num_classes=config['data']['num_classes'],
            train=False
        )
        
        train_loader = DataLoader(
            train_dataset, batch_size=config['training']['batch_size'],
            shuffle=True, num_workers=4, pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset, batch_size=config['training']['batch_size'],
            shuffle=False, num_workers=4, pin_memory=True
        )
        
        # 创建类别权重
        class_weights = torch.ones(config['data']['num_classes']).to(device)
        if 'class_specific_weights' in config['class_optimization']:
            for cls, weight in config['class_optimization']['class_specific_weights'].items():
                class_weights[int(cls)] = float(weight)
        
        # 创建损失函数和优化器
        criterion = CombinedLoss(config['loss'], class_weights)
        optimizer = optim.AdamW(
            model.parameters(),
            lr=config['training']['learning_rate']['init_lr'],
            weight_decay=config['training']['optimizer']['weight_decay']
        )
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=config['training']['total_epochs'], eta_min=1e-6
        )
        
        # 训练循环
        best_miou = 0.0
        model_save_path = os.path.join(self.log_dir, f'model_{model_index}_{model_name}.pth')
        
        for epoch in range(config['training']['total_epochs']):
            # 训练
            model.train()
            train_loss = 0.0
            
            for batch_idx, (images, masks) in enumerate(train_loader):
                images, masks = images.to(device), masks.to(device)
                
                optimizer.zero_grad()
                outputs = model(images)
                loss = criterion(outputs, masks)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                
                if batch_idx % 50 == 0:
                    self.logger.info(f'模型{model_index+1} Epoch {epoch+1}, Batch {batch_idx}, Loss: {loss.item():.4f}')
            
            # 验证
            model.eval()
            val_loss = 0.0
            correct_pixels = 0
            total_pixels = 0
            
            with torch.no_grad():
                for images, masks in val_loader:
                    images, masks = images.to(device), masks.to(device)
                    outputs = model(images)
                    loss = criterion(outputs, masks)
                    val_loss += loss.item()
                    
                    # 简单的像素准确率计算
                    predictions = torch.argmax(outputs, dim=1)
                    correct_pixels += (predictions == masks).sum().item()
                    total_pixels += masks.numel()
            
            # 更新学习率
            scheduler.step()
            
            # 计算指标
            avg_train_loss = train_loss / len(train_loader)
            avg_val_loss = val_loss / len(val_loader)
            pixel_accuracy = correct_pixels / total_pixels
            
            # 简单的mIoU估算（基于像素准确率）
            estimated_miou = pixel_accuracy * 0.8  # 粗略估算
            
            self.logger.info(f'模型{model_index+1} Epoch {epoch+1}/{config["training"]["total_epochs"]}: '
                           f'Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}, '
                           f'Pixel Acc: {pixel_accuracy:.4f}, Est. mIoU: {estimated_miou:.4f}')
            
            # 保存最佳模型
            if estimated_miou > best_miou:
                best_miou = estimated_miou
                torch.save(model.state_dict(), model_save_path)
                self.logger.info(f'模型{model_index+1} 保存最佳模型，mIoU: {best_miou:.4f}')
        
        self.logger.info(f'模型{model_index+1} 训练完成，最佳mIoU: {best_miou:.4f}')
        return model_save_path, best_miou
    
    def train_ensemble(self):
        """训练集成模型"""
        self.logger.info("开始集成训练")
        
        # 创建模型变体
        model_variants = self.create_model_variants()
        
        # 训练每个模型
        model_paths = []
        model_performances = []
        
        for i, (model_name, config) in enumerate(model_variants):
            model_path, performance = self.train_single_model(model_name, config, i)
            model_paths.append(model_path)
            model_performances.append(performance)
        
        # 保存集成信息
        ensemble_info = {
            'model_paths': model_paths,
            'model_performances': model_performances,
            'model_names': [name for name, _ in model_variants],
            'training_time': datetime.now().isoformat()
        }
        
        ensemble_info_path = os.path.join(self.log_dir, 'ensemble_info.json')
        with open(ensemble_info_path, 'w') as f:
            json.dump(ensemble_info, f, indent=2)
        
        self.logger.info("集成训练完成")
        self.logger.info(f"模型性能: {model_performances}")
        self.logger.info(f"平均性能: {np.mean(model_performances):.4f}")
        
        return ensemble_info

class EnsemblePredictor:
    """集成预测器"""
    
    def __init__(self, ensemble_info_path):
        with open(ensemble_info_path, 'r') as f:
            self.ensemble_info = json.load(f)
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.models = []
        self.load_models()
    
    def load_models(self):
        """加载所有模型"""
        for model_path in self.ensemble_info['model_paths']:
            model = Unet(num_classes=29, pretrained=False, backbone='resnet50')
            model.load_state_dict(torch.load(model_path, map_location=self.device))
            model = model.to(self.device)
            model.eval()
            self.models.append(model)
    
    def predict(self, images):
        """集成预测"""
        predictions = []
        
        with torch.no_grad():
            for model in self.models:
                output = model(images)
                predictions.append(torch.softmax(output, dim=1))
        
        # 平均集成
        ensemble_output = torch.stack(predictions).mean(dim=0)
        
        return ensemble_output
    
    def weighted_predict(self, images):
        """加权集成预测"""
        predictions = []
        weights = self.ensemble_info['model_performances']
        
        with torch.no_grad():
            for model, weight in zip(self.models, weights):
                output = model(images)
                predictions.append(torch.softmax(output, dim=1) * weight)
        
        # 加权平均
        ensemble_output = torch.stack(predictions).sum(dim=0)
        ensemble_output = ensemble_output / sum(weights)
        
        return ensemble_output

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='集成训练')
    parser.add_argument('--config', default='config_immediate_improvements.yaml', help='基础配置文件')
    parser.add_argument('--num_models', type=int, default=3, help='集成模型数量')
    parser.add_argument('--mode', choices=['train', 'predict'], default='train', help='运行模式')
    parser.add_argument('--ensemble_info', help='集成信息文件路径（预测模式需要）')
    
    args = parser.parse_args()
    
    if args.mode == 'train':
        # 训练集成模型
        trainer = EnsembleTrainer(args.config, args.num_models)
        ensemble_info = trainer.train_ensemble()
        print(f"集成训练完成，信息保存在: {trainer.log_dir}")
    
    elif args.mode == 'predict':
        if not args.ensemble_info:
            print("预测模式需要提供 --ensemble_info 参数")
            return
        
        # 集成预测示例
        predictor = EnsemblePredictor(args.ensemble_info)
        print("集成预测器已准备就绪")
        print(f"加载了 {len(predictor.models)} 个模型")

if __name__ == "__main__":
    main()
