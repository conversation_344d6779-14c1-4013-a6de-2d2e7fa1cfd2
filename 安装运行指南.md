# 问卷星自动上传工具 - 安装运行指南

## 🎯 任务概述

**目标**：自动化批量上传878张图片到问卷星，创建30题循环评价问卷
- 📁 图片路径：`D:\1a_taohuacun`
- 📊 每题10张图片，共30题
- 🔄 循环评价题型，包含评价选项
- 🌐 问卷地址：https://www.wjx.cn/wjxdesignnew/designnew.aspx?version=7&openType=redesign&curid=316618829&nqt=&sguid=8d03ce0d-b368-47b9-8646-e129a13932f2

## 📦 第一步：安装依赖

### 1. 安装Python包
```bash
pip install selenium
```

### 2. 下载ChromeDriver
1. 查看Chrome版本：打开Chrome → 设置 → 关于Chrome
2. 下载对应版本ChromeDriver：https://chromedriver.chromium.org/
3. 解压后将 `chromedriver.exe` 放到以下位置之一：
   - Python安装目录的Scripts文件夹
   - 系统PATH环境变量中的任意目录
   - 或者放在脚本同一目录下

## 🧪 第二步：运行测试版本

**强烈建议先运行测试版本验证功能！**

```bash
python wenjuanxing_test.py
```

### 测试版本功能：
- ✅ 验证页面元素是否能正确识别
- ✅ 测试创建1题循环评价
- ✅ 测试上传3张图片
- ✅ 生成详细日志和截图
- ✅ 验证整个流程是否正常

### 测试成功标志：
- 控制台显示 "🎉 测试成功！"
- 问卷中出现1个测试题目
- 日志文件显示所有步骤成功

## 🚀 第三步：运行完整版本

**只有测试成功后才运行完整版本！**

```bash
python wenjuanxing_auto_upload.py
```

### 完整版本功能：
- 📊 创建30题循环评价
- 🖼️ 上传300张图片（30题×10张）
- ⏱️ 预计执行时间：1-2小时
- 📝 生成详细执行日志

## 📋 执行流程

### 1. 启动阶段
```
🚀 问卷星自动上传工具
==================================================
📁 图片文件夹: D:\1a_taohuacun
🌐 问卷地址: https://www.wjx.cn/...
📋 任务: 创建30题循环评价，每题10张图片

确认开始执行? (y/n): y
```

### 2. 浏览器操作
- 自动打开Chrome浏览器
- 导航到问卷编辑页面
- 如需登录，手动完成后按回车继续

### 3. 自动执行
每题执行以下步骤：
1. 点击"添加题目"
2. 选择"循环评价"题型
3. 设置题目标题
4. 进入"评价对象设置"
5. 选择"图片"类型
6. 上传10张图片
7. 添加评价选项
8. 保存题目

### 4. 进度监控
```
2024-05-24 19:00:01 - INFO - 开始创建第 1 题循环评价...
2024-05-24 19:00:05 - INFO - ✅ 点击添加题目按钮成功
2024-05-24 19:00:08 - INFO - ✅ 选择循环评价题型成功
2024-05-24 19:00:10 - INFO - ✅ 设置题目标题成功
...
2024-05-24 19:02:30 - INFO - 第 1 题创建完成
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. ChromeDriver版本不匹配
**错误**：`selenium.common.exceptions.SessionNotCreatedException`
**解决**：
- 检查Chrome版本：chrome://version/
- 下载匹配的ChromeDriver版本
- 确保ChromeDriver在PATH中

#### 2. 找不到页面元素
**错误**：`找不到添加题目按钮`
**解决**：
- 问卷星可能更新了界面
- 查看错误截图文件
- 手动验证页面元素是否存在

#### 3. 图片上传失败
**错误**：`图片上传测试失败`
**解决**：
- 检查图片文件是否存在
- 确认图片格式正确（jpg, png等）
- 检查网络连接稳定性

#### 4. 登录问题
**现象**：页面跳转到登录界面
**解决**：
- 手动在浏览器中登录问卷星
- 登录完成后按回车键继续
- 确保登录状态保持有效

### 调试技巧

#### 1. 查看日志文件
```
wenjuanxing_upload_20240524_190001.log
```
包含详细的执行步骤和错误信息

#### 2. 查看错误截图
```
error_screenshot_q1_190530.png
page_screenshot_190531.png
```
显示出错时的页面状态

#### 3. 增加等待时间
如果网络较慢，可以修改脚本中的等待时间：
```python
time.sleep(5)  # 从3秒改为5秒
```

## ⚙️ 自定义配置

### 修改题目数量
```python
# 在main()函数中
success = uploader.create_questionnaire(30)  # 改为需要的数量
```

### 修改每题图片数量
```python
# 在create_questionnaire()方法中
images_per_question = 10  # 改为需要的数量
```

### 修改评价选项
```python
evaluation_options = [
    "非常不满意",
    "不满意", 
    "一般",
    "满意",
    "非常满意"
]
```

## 📊 执行结果

### 成功标志
- ✅ 控制台显示"🎉 任务执行成功！"
- ✅ 问卷中出现30个循环评价题目
- ✅ 每题包含10张图片
- ✅ 每题包含5个评价选项

### 验证方法
1. 在问卷星编辑界面查看题目
2. 检查图片是否正确上传
3. 验证评价选项是否完整
4. 测试问卷预览功能

## 🚨 重要提醒

1. **先测试后执行**：务必先运行测试版本
2. **网络稳定**：确保网络连接稳定
3. **不要干预**：执行过程中不要手动操作浏览器
4. **备份数据**：重要图片请提前备份
5. **时间充足**：完整执行需要1-2小时

## 📞 技术支持

如果遇到问题：
1. 📝 查看日志文件详细信息
2. 🖼️ 检查错误截图
3. 🔄 尝试重新运行测试版本
4. 🌐 确认问卷星页面是否有变化

---

**🎯 目标**：成功创建包含878张图片的30题循环评价问卷！
