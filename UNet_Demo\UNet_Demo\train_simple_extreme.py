#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版极端类别平衡训练脚本
快速启动训练，专门解决类别不平衡问题
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import time
from datetime import datetime
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.unet import Unet
from utils.dataloader import SegmentationDataset
from utils.extreme_balance_losses import ExtremeBalanceLoss

def setup_logging():
    """设置日志"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"extreme_balance_simple_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def load_config():
    """加载配置"""
    with open('config_extreme_balance.yaml', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_model(num_classes=29):
    """创建模型"""
    model = Unet(
        num_classes=num_classes,
        backbone='resnet50',
        pretrained=True
    )
    return model

def create_dataloaders(config):
    """创建数据加载器"""
    # 读取文件列表
    with open(config['data']['train_annotation_path'], 'r') as f:
        train_lines = [line.strip() for line in f.readlines()]

    with open(config['data']['val_annotation_path'], 'r') as f:
        val_lines = [line.strip() for line in f.readlines()]

    # 创建数据集
    train_dataset = SegmentationDataset(
        file_list=train_lines,
        root_dir='VOCdevkit',
        img_size=(512, 512),
        num_classes=29,
        train=True
    )

    val_dataset = SegmentationDataset(
        file_list=val_lines,
        root_dir='VOCdevkit',
        img_size=(512, 512),
        num_classes=29,
        train=False
    )

    # 创建数据加载器 - 减少num_workers避免多进程问题
    train_loader = DataLoader(
        train_dataset,
        batch_size=4,
        shuffle=True,
        num_workers=0,  # 设为0避免多进程问题
        pin_memory=True,
        drop_last=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=4,
        shuffle=False,
        num_workers=0,  # 设为0避免多进程问题
        pin_memory=True,
        drop_last=False
    )

    return train_loader, val_loader

def train_one_epoch(model, train_loader, loss_fn, optimizer, device, epoch, logger):
    """训练一轮"""
    model.train()
    total_loss = 0.0
    num_batches = len(train_loader)

    for batch_idx, (images, targets) in enumerate(train_loader):
        images = images.to(device)
        targets = targets.to(device)

        optimizer.zero_grad()

        # 前向传播
        outputs = model(images)

        # 计算损失
        loss, loss_dict = loss_fn(outputs, targets)

        # 反向传播
        loss.backward()

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 2.0)

        optimizer.step()

        total_loss += loss.item()

        # 打印进度
        if batch_idx % 5 == 0:
            logger.info(f"Epoch {epoch}, Batch {batch_idx}/{num_batches}, Loss: {loss.item():.4f}")

            # 打印损失组件
            components_str = ", ".join([f"{k}={v:.4f}" for k, v in loss_dict.items() if k != 'total'])
            logger.info(f"  损失组件: {components_str}")

    avg_loss = total_loss / num_batches
    logger.info(f"Epoch {epoch} 平均训练损失: {avg_loss:.4f}")
    return avg_loss

def validate(model, val_loader, loss_fn, device, epoch, logger):
    """验证"""
    model.eval()
    total_loss = 0.0
    num_batches = len(val_loader)

    with torch.no_grad():
        for images, targets in val_loader:
            images = images.to(device)
            targets = targets.to(device)

            outputs = model(images)
            loss, _ = loss_fn(outputs, targets)

            total_loss += loss.item()

    avg_loss = total_loss / num_batches
    logger.info(f"Epoch {epoch} 验证损失: {avg_loss:.4f}")
    return avg_loss

def save_model(model, epoch, loss, save_dir="models"):
    """保存模型"""
    os.makedirs(save_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"extreme_balance_epoch_{epoch}_loss_{loss:.4f}_{timestamp}.pth"
    filepath = os.path.join(save_dir, filename)

    torch.save({
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'loss': loss,
        'timestamp': timestamp
    }, filepath)

    return filepath

def main():
    """主函数"""
    print("🚀 启动简化版极端类别平衡训练")
    print("=" * 50)

    # 设置日志
    logger = setup_logging()
    logger.info("开始极端类别平衡训练")

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 加载配置
    config = load_config()
    logger.info("配置加载完成")

    # 创建模型
    model = create_model(num_classes=29)
    model = model.to(device)
    logger.info("模型创建完成")

    # 创建损失函数
    loss_fn = ExtremeBalanceLoss(
        num_classes=29,
        difficult_classes=[8, 10, 18, 23, 26, 27, 28]
    )
    logger.info("极端平衡损失函数创建完成")

    # 创建优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=1e-4,
        weight_decay=0.02
    )
    logger.info("优化器创建完成")

    # 创建学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer,
        T_max=100,
        eta_min=1e-7
    )

    # 创建数据加载器
    train_loader, val_loader = create_dataloaders(config)
    logger.info(f"数据加载器创建完成，训练样本: {len(train_loader.dataset)}, 验证样本: {len(val_loader.dataset)}")

    # 训练参数
    total_epochs = 100
    best_loss = float('inf')
    patience = 20
    patience_counter = 0

    logger.info(f"开始训练，总轮数: {total_epochs}")

    # 训练循环
    for epoch in range(1, total_epochs + 1):
        logger.info(f"\n{'='*50}")
        logger.info(f"第 {epoch}/{total_epochs} 轮训练")
        logger.info(f"{'='*50}")

        # 训练
        train_loss = train_one_epoch(
            model, train_loader, loss_fn, optimizer, device, epoch, logger
        )

        # 验证
        val_loss = validate(
            model, val_loader, loss_fn, device, epoch, logger
        )

        # 更新学习率
        scheduler.step()
        current_lr = scheduler.get_last_lr()[0]
        logger.info(f"当前学习率: {current_lr:.2e}")

        # 保存最佳模型
        if val_loss < best_loss:
            best_loss = val_loss
            patience_counter = 0

            model_path = save_model(model, epoch, val_loss)
            logger.info(f"🏆 新的最佳模型已保存: {model_path}")
        else:
            patience_counter += 1
            logger.info(f"验证损失未改善，耐心计数: {patience_counter}/{patience}")

        # 早停检查
        if patience_counter >= patience:
            logger.info(f"早停触发，最佳验证损失: {best_loss:.4f}")
            break

        # 每10轮保存一次检查点
        if epoch % 10 == 0:
            checkpoint_path = save_model(model, epoch, val_loss)
            logger.info(f"检查点已保存: {checkpoint_path}")

    logger.info("🎯 训练完成！")
    logger.info(f"最佳验证损失: {best_loss:.4f}")

if __name__ == "__main__":
    main()
