import os
import torch
import torch.nn.functional as F
import matplotlib
matplotlib.use('Agg')
from matplotlib import pyplot as plt
from torch.utils.tensorboard import SummaryWriter

from .utils_metrics import compute_mIoU_tensor

class LossHistory:
    def __init__(self, log_dir, model, input_shape, val_loss_flag=True):
        self.log_dir       = log_dir
        self.val_loss_flag = val_loss_flag

        self.losses        = []
        self.val_loss      = []

        os.makedirs(self.log_dir, exist_ok=True)
        self.writer = SummaryWriter(self.log_dir)
        try:
            dummy_input = torch.randn(2, 3, input_shape[0], input_shape[1]).to(next(model.parameters()).device)
            self.writer.add_graph(model, dummy_input)
        except:
            pass

    def append_metrics(self, epoch, loss, val_loss, fscore, miou, lr=None, class_ious=None, train_metrics=None):
        # 累加到列表
        self.losses.append(loss)
        if self.val_loss_flag:
            self.val_loss.append(val_loss)

        # 写入文本日志
        with open(os.path.join(self.log_dir, "epoch_loss.txt"), 'a') as f:
            f.write(f"{loss}\n")
        with open(os.path.join(self.log_dir, "epoch_val_loss.txt"), 'a') as f:
            f.write(f"{val_loss}\n")
        with open(os.path.join(self.log_dir, "epoch_val_fscore.txt"), 'a') as f:
            f.write(f"{fscore}\n")
        with open(os.path.join(self.log_dir, "epoch_miou.txt"), 'a') as f:
            f.write(f"{miou}\n")

        # 记录基本指标到TensorBoard
        for writer in [self.writer, getattr(self, 'current_writer', None)]:
            if writer is not None:
                # 基本指标
                writer.add_scalar('loss/train', loss, epoch)
                writer.add_scalar('loss/val', val_loss, epoch)
                writer.add_scalar('metrics/fscore', fscore, epoch)
                writer.add_scalar('metrics/miou', miou, epoch)

                # 学习率
                if lr is not None:
                    writer.add_scalar('learning_rate', lr, epoch)

                # 训练过程中的详细指标
                if train_metrics is not None:
                    for key, value in train_metrics.items():
                        if isinstance(value, (int, float)):
                            writer.add_scalar(f'train_details/{key}', value, epoch)

                # 类别IoU
                if class_ious is not None and isinstance(class_ious, dict):
                    for class_name, iou_value in class_ious.items():
                        writer.add_scalar(f'class_iou/{class_name}', iou_value, epoch)

                # 添加训练进度
                writer.add_scalar('progress/epoch', epoch, epoch)

                # 添加验证和训练损失的比值，可以帮助检测过拟合
                if val_loss != 0 and loss != 0:
                    val_train_ratio = val_loss / loss
                    writer.add_scalar('metrics/val_train_ratio', val_train_ratio, epoch)

        # 绘制并保存 Loss 曲线
        self._plot_loss()

    def _plot_loss(self):
        epochs = list(range(1, len(self.losses) + 1))
        plt.figure()
        plt.plot(epochs, self.losses, 'red', label='train loss')
        if self.val_loss_flag:
            plt.plot(epochs, self.val_loss, 'coral', label='val loss')
        plt.grid(True)
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend(loc='upper right')
        plt.savefig(os.path.join(self.log_dir, 'epoch_loss.png'))
        plt.close()

class EvalCallback:
    def __init__(self, val_loader, num_classes, ignore_index=None, device='cpu', period=1, eval_flag=True):
        self.val_loader   = val_loader
        self.num_classes  = num_classes
        self.ignore_index = ignore_index
        self.device       = device
        self.period       = period
        self.eval_flag    = eval_flag

    def on_epoch_end(self, epoch, model):
        # 仅在指定周期才评估
        if not self.eval_flag or epoch % self.period != 0:
            return 0.0, {}

        model.eval()
        sum_iou = 0.0
        count   = 0

        # 收集所有预测和真实标签
        all_preds = []
        all_targets = []

        with torch.no_grad():
            for imgs, pngs in self.val_loader:
                imgs = imgs.to(self.device, non_blocking=True)
                pngs = pngs.to(self.device, non_blocking=True)

                outputs = model(imgs)
                preds   = torch.argmax(outputs, dim=1)

                # 收集批次的预测和真实标签
                all_preds.append(preds.cpu())
                all_targets.append(pngs.cpu())

                # 计算每个批次的mIoU
                batch_miou = compute_mIoU_tensor(preds, pngs,
                                           num_classes=self.num_classes,
                                           ignore_index=self.ignore_index)
                sum_iou += batch_miou
                count   += 1

        # 计算批次平均mIoU
        batch_mean_iou = sum_iou / max(count, 1)

        # 计算整体mIoU（使用所有样本）和每个类别的IoU
        class_ious = {}
        if all_preds and all_targets:
            all_preds = torch.cat(all_preds, dim=0)
            all_targets = torch.cat(all_targets, dim=0)

            # 计算整体mIoU和每个类别的IoU
            overall_miou, per_class_ious = compute_mIoU_tensor(
                all_preds, all_targets,
                num_classes=self.num_classes,
                ignore_index=self.ignore_index,
                return_per_class=True
            )

            # 将每个类别的IoU添加到字典中
            for i, iou in enumerate(per_class_ious):
                class_name = f"class_{i}"
                class_ious[class_name] = iou.item() if isinstance(iou, torch.Tensor) else float(iou)

            print(f"验证集mIoU: 批次平均={batch_mean_iou:.4f}, 整体={overall_miou:.4f}")

            # 打印每个类别的IoU
            print("每个类别的IoU:")
            for class_name, iou in class_ious.items():
                print(f"  {class_name}: {iou:.4f}")

            mean_iou = overall_miou  # 使用整体mIoU作为返回值
        else:
            print("警告: 验证集为空，无法计算mIoU")
            mean_iou = 0.0

        model.train()
        return mean_iou, class_ious
