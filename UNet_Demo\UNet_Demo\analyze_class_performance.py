#!/usr/bin/env python3
"""
类别性能分析脚本
分析每个类别的IoU表现，识别需要特别关注的困难类别
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_class_names():
    """加载类别名称"""
    class_names_file = "model_data/voc_classes.txt"
    if os.path.exists(class_names_file):
        with open(class_names_file, 'r', encoding='utf-8') as f:
            class_names = [line.strip() for line in f.readlines()]
        return class_names
    else:
        # 默认类别名称
        return [f"class_{i}" for i in range(29)]

def analyze_model_predictions(model_path, data_loader, device, num_classes=29):
    """分析模型在验证集上的类别表现"""
    from nets.unet import Unet
    from utils.utils_metrics import compute_mIoU_tensor
    
    # 加载模型
    model = Unet(num_classes=num_classes, pretrained=False, backbone='resnet50')
    model.load_state_dict(torch.load(model_path, map_location=device))
    model = model.to(device)
    model.eval()
    
    # 初始化统计
    class_ious = []
    class_pixel_counts = np.zeros(num_classes)
    class_correct_counts = np.zeros(num_classes)
    
    print("分析模型预测...")
    with torch.no_grad():
        for batch_idx, (images, masks) in enumerate(data_loader):
            if batch_idx >= 50:  # 限制分析样本数量
                break
                
            images = images.to(device)
            masks = masks.to(device)
            
            # 预测
            outputs = model(images)
            predictions = torch.argmax(outputs, dim=1)
            
            # 计算每个类别的IoU
            batch_ious = []
            for cls in range(num_classes):
                pred_mask = (predictions == cls)
                true_mask = (masks == cls)
                
                intersection = (pred_mask & true_mask).sum().float()
                union = (pred_mask | true_mask).sum().float()
                
                if union > 0:
                    iou = intersection / union
                    batch_ious.append(iou.item())
                else:
                    batch_ious.append(0.0)
                
                # 统计像素数量
                class_pixel_counts[cls] += true_mask.sum().item()
                class_correct_counts[cls] += intersection.item()
            
            class_ious.append(batch_ious)
            
            if batch_idx % 10 == 0:
                print(f"已处理 {batch_idx + 1} 个批次...")
    
    # 计算平均IoU
    class_ious = np.array(class_ious)
    mean_class_ious = np.mean(class_ious, axis=0)
    
    return mean_class_ious, class_pixel_counts, class_correct_counts

def create_class_performance_report(class_ious, class_pixel_counts, class_names, save_dir):
    """创建类别性能报告"""
    os.makedirs(save_dir, exist_ok=True)
    
    # 1. 类别IoU柱状图
    plt.figure(figsize=(15, 8))
    colors = ['red' if iou < 0.1 else 'orange' if iou < 0.3 else 'green' for iou in class_ious]
    
    bars = plt.bar(range(len(class_ious)), class_ious, color=colors, alpha=0.7)
    plt.xlabel('类别')
    plt.ylabel('IoU')
    plt.title('各类别IoU性能分析')
    plt.xticks(range(len(class_names)), [name[:10] for name in class_names], rotation=45, ha='right')
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (bar, iou) in enumerate(zip(bars, class_ious)):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{iou:.3f}', ha='center', va='bottom', fontsize=8)
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='red', alpha=0.7, label='严重问题 (IoU < 0.1)'),
        Patch(facecolor='orange', alpha=0.7, label='需要改进 (0.1 ≤ IoU < 0.3)'),
        Patch(facecolor='green', alpha=0.7, label='表现良好 (IoU ≥ 0.3)')
    ]
    plt.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'class_iou_analysis.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 类别像素分布
    plt.figure(figsize=(15, 6))
    plt.bar(range(len(class_pixel_counts)), class_pixel_counts, alpha=0.7, color='skyblue')
    plt.xlabel('类别')
    plt.ylabel('像素数量')
    plt.title('各类别像素分布')
    plt.xticks(range(len(class_names)), [name[:10] for name in class_names], rotation=45, ha='right')
    plt.yscale('log')  # 使用对数刻度
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'class_pixel_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 问题类别识别
    problem_classes = []
    for i, (iou, pixel_count) in enumerate(zip(class_ious, class_pixel_counts)):
        if iou < 0.1:
            problem_classes.append({
                'class_id': i,
                'class_name': class_names[i],
                'iou': iou,
                'pixel_count': pixel_count,
                'severity': 'critical' if iou == 0 else 'severe'
            })
        elif iou < 0.3:
            problem_classes.append({
                'class_id': i,
                'class_name': class_names[i],
                'iou': iou,
                'pixel_count': pixel_count,
                'severity': 'moderate'
            })
    
    # 保存问题类别报告
    with open(os.path.join(save_dir, 'problem_classes_report.txt'), 'w', encoding='utf-8') as f:
        f.write("类别性能分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"总体统计:\n")
        f.write(f"- 平均mIoU: {np.mean(class_ious):.4f}\n")
        f.write(f"- 零IoU类别数: {sum(1 for iou in class_ious if iou == 0)}\n")
        f.write(f"- 低IoU类别数 (<0.1): {sum(1 for iou in class_ious if iou < 0.1)}\n")
        f.write(f"- 中等IoU类别数 (0.1-0.3): {sum(1 for iou in class_ious if 0.1 <= iou < 0.3)}\n")
        f.write(f"- 良好IoU类别数 (≥0.3): {sum(1 for iou in class_ious if iou >= 0.3)}\n\n")
        
        f.write("问题类别详情:\n")
        f.write("-" * 30 + "\n")
        
        for cls_info in sorted(problem_classes, key=lambda x: x['iou']):
            f.write(f"类别 {cls_info['class_id']:2d} ({cls_info['class_name'][:15]:15s}): ")
            f.write(f"IoU={cls_info['iou']:.4f}, ")
            f.write(f"像素数={cls_info['pixel_count']:8.0f}, ")
            f.write(f"严重程度={cls_info['severity']}\n")
        
        f.write(f"\n建议的类别权重调整:\n")
        f.write("-" * 30 + "\n")
        for cls_info in problem_classes:
            if cls_info['severity'] == 'critical':
                weight = 25.0
            elif cls_info['severity'] == 'severe':
                weight = 15.0
            else:
                weight = 8.0
            f.write(f"类别 {cls_info['class_id']:2d}: 权重 {weight:.1f}\n")
    
    return problem_classes

def generate_improved_config(problem_classes, base_config_path, output_path):
    """基于分析结果生成改进的配置文件"""
    import yaml
    
    # 加载基础配置
    with open(base_config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 更新困难类别列表
    critical_classes = [cls['class_id'] for cls in problem_classes if cls['severity'] == 'critical']
    severe_classes = [cls['class_id'] for cls in problem_classes if cls['severity'] == 'severe']
    
    config['class_optimization']['difficult_classes'] = critical_classes + severe_classes
    
    # 更新类别特定权重
    class_specific_weights = {}
    for cls_info in problem_classes:
        if cls_info['severity'] == 'critical':
            class_specific_weights[cls_info['class_id']] = 25.0
        elif cls_info['severity'] == 'severe':
            class_specific_weights[cls_info['class_id']] = 15.0
        else:
            class_specific_weights[cls_info['class_id']] = 8.0
    
    config['class_optimization']['class_specific_weights'] = class_specific_weights
    
    # 如果问题类别很多，增加Focal Loss权重
    if len(problem_classes) > 10:
        config['loss']['weight_focal'] = 2.5
        config['loss']['focal_gamma'] = 3.5
    
    # 保存改进的配置
    with open(output_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    print(f"改进的配置已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='分析类别性能')
    parser.add_argument('--model_path', required=True, help='模型权重路径')
    parser.add_argument('--config_path', default='config_immediate_improvements.yaml', help='配置文件路径')
    parser.add_argument('--output_dir', default='analysis_results', help='输出目录')
    parser.add_argument('--batch_size', type=int, default=4, help='批次大小')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not os.path.exists(args.model_path):
        print(f"错误：模型文件不存在 {args.model_path}")
        return
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载配置
    import yaml
    with open(args.config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建验证数据集
    from utils.dataloader import SegmentationDataset
    from torch.utils.data import DataLoader
    
    val_dataset = SegmentationDataset(
        annotation_path=config['data']['val_annotation_path'],
        input_shape=config['data']['input_shape'][:2],
        num_classes=config['data']['num_classes'],
        train=False
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=2
    )
    
    print(f"验证集大小: {len(val_dataset)}")
    
    # 分析模型性能
    class_ious, class_pixel_counts, class_correct_counts = analyze_model_predictions(
        args.model_path, val_loader, device, config['data']['num_classes']
    )
    
    # 加载类别名称
    class_names = load_class_names()
    
    # 创建分析报告
    problem_classes = create_class_performance_report(
        class_ious, class_pixel_counts, class_names, args.output_dir
    )
    
    # 生成改进的配置文件
    improved_config_path = os.path.join(args.output_dir, 'improved_config.yaml')
    generate_improved_config(problem_classes, args.config_path, improved_config_path)
    
    # 打印总结
    print("\n" + "="*50)
    print("类别性能分析完成")
    print("="*50)
    print(f"平均mIoU: {np.mean(class_ious):.4f}")
    print(f"问题类别数量: {len(problem_classes)}")
    print(f"分析结果保存在: {args.output_dir}")
    print(f"改进配置文件: {improved_config_path}")
    print("\n建议下一步:")
    print("1. 查看生成的图表和报告")
    print("2. 使用改进的配置文件重新训练")
    print("3. 对问题类别进行数据增强")

if __name__ == "__main__":
    main()
