#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简化的训练脚本
验证基本训练流程
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import segmentation_models_pytorch as smp
import time

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🚀 使用设备: {device}")

# 清理显存
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print("🧹 显存已清理")

def main():
    """主函数"""
    print("🚀 启动最简化训练")
    print("=" * 40)
    
    try:
        # 加载数据
        from enhanced_dataset import create_enhanced_datasets
        
        print("📊 加载数据...")
        train_dataset, val_dataset = create_enhanced_datasets()
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=2,  # 最小批次
            shuffle=True,
            num_workers=0
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=2,
            shuffle=False,
            num_workers=0
        )
        
        print(f"✅ 训练集: {len(train_loader)} 批次")
        print(f"✅ 验证集: {len(val_loader)} 批次")
        
        # 创建最简单的模型
        print("🧠 创建模型...")
        model = smp.Unet(
            encoder_name='resnet18',  # 最小的模型
            encoder_weights='imagenet',
            in_channels=3,
            classes=29,
            activation=None,
        ).to(device)
        
        # 简单的损失函数和优化器
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=1e-4)
        
        print("🎯 开始训练...")
        
        # 训练5轮验证
        for epoch in range(5):
            print(f"\n🔄 Epoch {epoch+1}/5")
            
            # 训练
            model.train()
            train_loss = 0
            train_batches = 0
            
            for batch_idx, (images, masks) in enumerate(train_loader):
                try:
                    images, masks = images.to(device), masks.to(device)
                    
                    optimizer.zero_grad()
                    outputs = model(images)
                    loss = criterion(outputs, masks)
                    loss.backward()
                    optimizer.step()
                    
                    train_loss += loss.item()
                    train_batches += 1
                    
                    if batch_idx % 10 == 0:
                        print(f"  训练批次 {batch_idx+1}/{len(train_loader)}, 损失: {loss.item():.4f}")
                    
                    # 清理显存
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        
                except Exception as e:
                    print(f"❌ 训练批次 {batch_idx} 出错: {e}")
                    continue
            
            avg_train_loss = train_loss / max(train_batches, 1)
            
            # 验证
            model.eval()
            val_loss = 0
            val_batches = 0
            correct_pixels = 0
            total_pixels = 0
            
            with torch.no_grad():
                for batch_idx, (images, masks) in enumerate(val_loader):
                    try:
                        images, masks = images.to(device), masks.to(device)
                        
                        outputs = model(images)
                        loss = criterion(outputs, masks)
                        
                        val_loss += loss.item()
                        val_batches += 1
                        
                        # 计算像素准确率
                        predictions = torch.argmax(outputs, dim=1)
                        correct_pixels += (predictions == masks).sum().item()
                        total_pixels += masks.numel()
                        
                        # 清理显存
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                            
                    except Exception as e:
                        print(f"❌ 验证批次 {batch_idx} 出错: {e}")
                        continue
            
            avg_val_loss = val_loss / max(val_batches, 1)
            pixel_acc = correct_pixels / max(total_pixels, 1)
            
            print(f"✅ Epoch {epoch+1} 完成:")
            print(f"  训练损失: {avg_train_loss:.4f}")
            print(f"  验证损失: {avg_val_loss:.4f}")
            print(f"  像素准确率: {pixel_acc:.4f}")
            
            # 保存模型
            if epoch == 4:  # 最后一轮
                torch.save(model.state_dict(), f'minimal_model_final.pth')
                print("💾 模型已保存")
        
        print("\n🎉 最简化训练完成!")
        print("✅ 基本训练流程验证成功")
        
        if pixel_acc > 0.3:
            print("🎯 像素准确率 > 0.3，配置良好!")
        elif pixel_acc > 0.1:
            print("👍 像素准确率 > 0.1，有改进空间")
        else:
            print("💪 需要进一步调试")
            
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
