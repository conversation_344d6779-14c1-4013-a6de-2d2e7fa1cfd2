"""
多预训练数据集权重混合训练脚本
使用混合多个预训练数据集的权重来提升对目标数据集的标签匹配度和迁移效果
"""
import os
import sys
import yaml
import torch
import torch.nn as nn
import logging
from datetime import datetime
import argparse
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from unet import unet
from utils.multi_pretrain_mixer import MultiPretrainMixer, apply_multi_pretrain_to_model, create_multi_pretrain_config
from utils.transfer_learning import setup_layerwise_learning_rates, progressive_unfreezing, get_unfreeze_schedule_from_config
from utils.dataloader import UnetDataset, unet_dataset_collate_fn
from utils.utils_fit import fit_one_epoch
from utils.utils_metrics import compute_mIoU
from utils.callbacks import LossHistory, EarlyStopping
from utils.losses import compute_class_weights
from utils.scheduler import get_lr_scheduler

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_config(config_path: str) -> dict:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_model_with_multi_pretrain(cfg: dict) -> nn.Module:
    """创建带有多预训练权重混合的模型"""
    logger.info("创建模型...")
    
    # 创建基础模型
    model = unet(
        num_classes=cfg['data']['num_classes'],
        backbone=cfg['model']['backbone'],
        pretrained=False,  # 不使用单一预训练权重
        dropout_rate=cfg['model'].get('dropout_rate', 0.2),
        use_attention=cfg['model'].get('use_attention', True),
        attention_type=cfg['model'].get('attention_type', 'cbam')
    )
    
    # 应用多预训练权重混合
    if cfg.get('multi_pretrain', {}).get('enabled', False):
        logger.info("应用多预训练数据集权重混合...")
        target_classes = cfg['data'].get('class_names', [])
        model = apply_multi_pretrain_to_model(model, cfg, target_classes)
    else:
        logger.info("未启用多预训练权重混合，使用标准预训练权重")
    
    return model

def setup_optimizer_and_scheduler(model: nn.Module, cfg: dict):
    """设置优化器和学习率调度器"""
    # 设置分层学习率
    if 'encoder_lr' in cfg['train'] and 'decoder_lr' in cfg['train'] and 'head_lr' in cfg['train']:
        logger.info("使用分层学习率...")
        param_groups = setup_layerwise_learning_rates(model, cfg)
        optimizer = getattr(torch.optim, cfg['train']['optimizer'])(
            param_groups,
            weight_decay=cfg['train'].get('weight_decay', 0.01)
        )
    else:
        # 使用统一学习率
        base_lr = cfg['train'].get('base_lr', 1e-4)
        optimizer = getattr(torch.optim, cfg['train']['optimizer'])(
            model.parameters(),
            lr=base_lr,
            weight_decay=cfg['train'].get('weight_decay', 0.01)
        )
    
    # 设置学习率调度器
    scheduler = get_lr_scheduler(
        optimizer=optimizer,
        scheduler_type=cfg['train']['scheduler']['type'],
        total_epochs=cfg['train']['total_epochs'],
        **cfg['train']['scheduler']
    )
    
    return optimizer, scheduler

def setup_loss_function(cfg: dict, device: torch.device):
    """设置损失函数"""
    from utils.losses import FocalDiceLoss, DiceLoss, FocalLoss
    
    # 计算类别权重
    if cfg['loss'].get('class_weights', {}).get('method'):
        logger.info("计算类别权重...")
        # 这里需要根据实际数据集路径计算
        # 暂时使用均匀权重
        class_weights = torch.ones(cfg['data']['num_classes']).to(device)
    else:
        class_weights = None
    
    # 创建损失函数
    loss_type = cfg['loss']['main_loss']
    if loss_type == "focal_dice":
        criterion = FocalDiceLoss(
            alpha=cfg['loss']['weights'].get('focal_weight', 0.7),
            gamma=2.0,
            class_weights=class_weights
        )
    elif loss_type == "dice":
        criterion = DiceLoss(class_weights=class_weights)
    elif loss_type == "focal":
        criterion = FocalLoss(
            alpha=cfg['loss']['weights'].get('focal_weight', 0.7),
            gamma=2.0,
            class_weights=class_weights
        )
    else:
        criterion = nn.CrossEntropyLoss(weight=class_weights)
    
    return criterion

def create_data_loaders(cfg: dict):
    """创建数据加载器"""
    from torch.utils.data import DataLoader
    
    # 创建数据集
    dataset_path = cfg['data']['dataset_path']
    input_shape = cfg['data']['input_shape']
    num_classes = cfg['data']['num_classes']
    
    # 训练数据集
    train_dataset = UnetDataset(
        annotation_lines=None,  # 需要根据实际情况设置
        input_shape=input_shape,
        num_classes=num_classes,
        train=True,
        dataset_path=dataset_path
    )
    
    # 验证数据集
    val_dataset = UnetDataset(
        annotation_lines=None,  # 需要根据实际情况设置
        input_shape=input_shape,
        num_classes=num_classes,
        train=False,
        dataset_path=dataset_path
    )
    
    # 数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=cfg['train']['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        collate_fn=unet_dataset_collate_fn
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=cfg['train']['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
        collate_fn=unet_dataset_collate_fn
    )
    
    return train_loader, val_loader

def train_model(cfg: dict, save_dir: str):
    """训练模型"""
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建模型
    model = create_model_with_multi_pretrain(cfg)
    model = model.to(device)
    
    # 设置优化器和调度器
    optimizer, scheduler = setup_optimizer_and_scheduler(model, cfg)
    
    # 设置损失函数
    criterion = setup_loss_function(cfg, device)
    
    # 创建数据加载器
    train_loader, val_loader = create_data_loaders(cfg)
    
    # 设置回调函数
    loss_history = LossHistory(save_dir)
    early_stopping = EarlyStopping(
        patience=cfg['validation']['early_stopping']['patience'],
        min_delta=cfg['validation']['early_stopping']['min_delta'],
        monitor=cfg['validation']['early_stopping']['monitor']
    )
    
    # 获取解冻计划
    unfreeze_schedule = get_unfreeze_schedule_from_config(cfg)
    
    # 训练循环
    best_miou = 0.0
    total_epochs = cfg['train']['total_epochs']
    
    for epoch in range(total_epochs):
        logger.info(f"Epoch {epoch+1}/{total_epochs}")
        
        # 渐进式解冻
        if unfreeze_schedule:
            progressive_unfreezing(model, epoch, unfreeze_schedule)
        
        # 训练一个epoch
        train_loss = fit_one_epoch(
            model_train=model,
            model=model,
            loss_history=loss_history,
            optimizer=optimizer,
            epoch=epoch,
            epoch_step=len(train_loader),
            epoch_step_val=len(val_loader),
            gen=train_loader,
            gen_val=val_loader,
            Epoch=total_epochs,
            cuda=torch.cuda.is_available(),
            dice_loss=criterion,
            focal_loss=None,
            cls_weights=None,
            num_classes=cfg['data']['num_classes'],
            fp16=False,
            scaler=None,
            save_period=cfg['logging']['save_frequency'],
            save_dir=save_dir,
            local_rank=0
        )
        
        # 验证
        if epoch % 5 == 0:  # 每5个epoch验证一次
            val_miou = compute_mIoU(
                model=model,
                val_loader=val_loader,
                num_classes=cfg['data']['num_classes'],
                device=device
            )
            
            logger.info(f"验证 mIoU: {val_miou:.4f}")
            
            # 保存最佳模型
            if val_miou > best_miou:
                best_miou = val_miou
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'best_miou': best_miou,
                    'config': cfg
                }, os.path.join(save_dir, 'best_model.pth'))
                logger.info(f"保存最佳模型，mIoU: {best_miou:.4f}")
            
            # 早停检查
            if early_stopping(val_miou):
                logger.info(f"早停触发，在第 {epoch+1} 轮停止训练")
                break
        
        # 更新学习率
        if scheduler:
            scheduler.step()
    
    logger.info(f"训练完成，最佳 mIoU: {best_miou:.4f}")

def main():
    parser = argparse.ArgumentParser(description='多预训练数据集权重混合训练')
    parser.add_argument('--config', type=str, default='config_multi_pretrain.yaml',
                       help='配置文件路径')
    parser.add_argument('--save_dir', type=str, default=None,
                       help='模型保存目录')
    
    args = parser.parse_args()
    
    # 加载配置
    cfg = load_config(args.config)
    
    # 设置保存目录
    if args.save_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_dir = f"logs/multi_pretrain_{timestamp}"
    else:
        save_dir = args.save_dir
    
    os.makedirs(save_dir, exist_ok=True)
    
    # 保存配置文件
    with open(os.path.join(save_dir, 'config.yaml'), 'w', encoding='utf-8') as f:
        yaml.dump(cfg, f, default_flow_style=False, allow_unicode=True)
    
    # 开始训练
    logger.info(f"开始多预训练权重混合训练，保存目录: {save_dir}")
    train_model(cfg, save_dir)

if __name__ == "__main__":
    main()
