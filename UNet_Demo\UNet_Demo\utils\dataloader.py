import os
import cv2
import numpy as np
import albumentations as A
from albumentations.pytorch import ToTensorV2
import torch
from torch.utils.data import Dataset, DataLoader
import logging
from utils.domain_augmentation import get_domain_specific_augmentation, get_validation_augmentation

os.environ["NO_ALBUMENTATIONS_UPDATE"] = "1"
logger = logging.getLogger(__name__)

class SegmentationDataset(Dataset):
    """
    通用语义分割数据集，支持 ResNet101+DeepLabV3+ 预处理，以及混合损失需求。
    返回: image: FloatTensor (3,H,W), mask: LongTensor (H,W)
    """
    def __init__(self,
                 file_list,
                 root_dir,
                 img_size=(512,512),
                 num_classes=21,
                 train=True,
                 cfg=None):
        """
        参数:
        - file_list: list of image base names (without extension)
        - root_dir: VOC 根目录，包含 JPEGImages/ 和 SegmentationClass/
        - img_size: (H,W) 输入尺寸
        - num_classes: 类别数，不含 ignore index
        - train: 是否为训练模式，决定是否使用增强
        - cfg: 配置字典，用于特定领域数据增强
        """

        # 处理文件列表
        if isinstance(file_list, str) and os.path.isfile(file_list):
            with open(file_list, 'r', encoding='utf-8') as f:
                self.file_list = [line.strip() for line in f if line.strip()]
        else:
            self.file_list = [f.strip() for f in file_list if f.strip()]

        # 验证文件列表
        valid_files = []
        for name in self.file_list:
            img_path = os.path.join(root_dir, 'VOC2025', 'JPEGImages', name + '.jpg')
            mask_path = os.path.join(root_dir, 'VOC2025', 'SegmentationClass', name + '.png')

            if os.path.exists(img_path) and os.path.exists(mask_path):
                valid_files.append(name)
            else:
                print(f"警告: 跳过不存在的文件: {name}")

        self.file_list = valid_files
        print(f"有效文件数量: {len(self.file_list)}")

        self.root_dir    = root_dir
        self.img_h, self.img_w = img_size
        self.num_classes = num_classes
        self.train       = train
        self.cfg         = cfg
        # ImageNet 归一化参数
        self.mean = [0.485, 0.456, 0.406]
        self.std  = [0.229, 0.224, 0.225]
        # ignore_index 用于损失函数
        self.ignore_index = num_classes

        # 使用特定领域数据增强
        if self.train:
            if cfg is not None:
                logger.info("使用特定领域数据增强")
                self.aug = get_domain_specific_augmentation(cfg)
            else:
                logger.info("使用默认数据增强")
                self.aug = A.Compose([
                    # 随机裁剪
                    A.RandomResizedCrop(self.img_h, self.img_w, scale=(0.5, 1.0), ratio=(0.75, 1.33), p=0.8),

                    # 基础几何变换
                    A.HorizontalFlip(p=0.5),
                    A.RandomRotate90(p=0.5),

                    # 颜色变换
                    A.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.1, p=0.7),

                    # 标准化和转换为张量
                    A.Normalize(mean=self.mean, std=self.std),
                    ToTensorV2()
                ], additional_targets={"mask": "mask"})
        else:
            if cfg is not None:
                logger.info("使用验证集数据增强")
                self.aug = get_validation_augmentation(cfg)
            else:
                logger.info("使用默认验证集数据增强")
                self.aug = A.Compose([
                    A.Resize(self.img_h, self.img_w),
                    A.Normalize(mean=self.mean, std=self.std),
                    ToTensorV2()
                ], additional_targets={"mask": "mask"})

    def __len__(self):
        return len(self.file_list)

    def __getitem__(self, idx):
        name = self.file_list[idx].strip()
        img_path  = os.path.join(self.root_dir, 'VOC2025', 'JPEGImages', name + '.jpg')
        mask_path = os.path.join(self.root_dir, 'VOC2025', 'SegmentationClass', name + '.png')

        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"警告: 图像文件不存在: {img_path}")
            # 尝试查找可能的文件
            img_dir = os.path.join(self.root_dir, 'VOC2025', 'JPEGImages')
            possible_files = [f for f in os.listdir(img_dir) if f.startswith(name)]
            if possible_files:
                img_path = os.path.join(img_dir, possible_files[0])
                print(f"使用替代文件: {img_path}")

        if not os.path.exists(mask_path):
            print(f"警告: 掩码文件不存在: {mask_path}")
            # 尝试查找可能的文件
            mask_dir = os.path.join(self.root_dir, 'VOC2025', 'SegmentationClass')
            possible_files = [f for f in os.listdir(mask_dir) if f.startswith(name)]
            if possible_files:
                mask_path = os.path.join(mask_dir, possible_files[0])
                print(f"使用替代文件: {mask_path}")

        # 读取图像和掩码
        img = cv2.imread(img_path)
        if img is None:
            raise ValueError(f"无法读取图像文件: {img_path}")
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        if mask is None:
            raise ValueError(f"无法读取掩码文件: {mask_path}")

        # 不记录原始图像大小，减少日志输出
        # if self.train and idx % 20 == 0:
        #     logger.info(f"原始图像大小: {img.shape}, 掩码大小: {mask.shape}, 文件: {name}")

        try:
            # 应用数据增强
            augmented = self.aug(image=img, mask=mask)

            img_tensor = augmented['image']
            # 先拿到增强后的 mask，可能是 ndarray，也可能是 Tensor
            aug_mask = augmented['mask']

            if isinstance(aug_mask, np.ndarray):
                mask_np = aug_mask.astype(np.int64)
            else:
                # Tensor → NumPy
                mask_np = aug_mask.cpu().numpy().astype(np.int64)

            # 不记录增强后的图像和掩码大小，减少日志输出
            # if self.train and idx % 20 == 0:
            #     if isinstance(img_tensor, torch.Tensor):
            #         logger.info(f"增强后图像大小: {img_tensor.shape}, 掩码大小: {mask_np.shape}, 文件: {name}")
            #     else:
            #         logger.info(f"增强后图像大小: {img_tensor.shape}, 掩码大小: {mask_np.shape}, 文件: {name}")

            mask_np[mask_np >= self.num_classes] = self.ignore_index
            mask_tensor = torch.from_numpy(mask_np).long()

            return img_tensor, mask_tensor

        except Exception as e:
            logger.error(f"数据增强错误: {e}, 文件: {name}, 图像大小: {img.shape}, 掩码大小: {mask.shape}")

            # 如果数据增强失败，手动调整大小并转换
            img_resized = cv2.resize(img, (self.img_w, self.img_h))
            mask_resized = cv2.resize(mask, (self.img_w, self.img_h), interpolation=cv2.INTER_NEAREST)

            # 手动转换为张量
            img_tensor = torch.from_numpy(img_resized.transpose(2, 0, 1)).float() / 255.0
            img_tensor = torch.nn.functional.normalize(img_tensor,
                                                     mean=torch.tensor(self.mean),
                                                     std=torch.tensor(self.std))

            mask_tensor = torch.from_numpy(mask_resized).long()
            mask_tensor[mask_tensor >= self.num_classes] = self.ignore_index

            # 不记录手动调整后的图像大小，减少日志输出
            # logger.info(f"手动调整后图像大小: {img_tensor.shape}, 掩码大小: {mask_tensor.shape}, 文件: {name}")

            return img_tensor, mask_tensor


if __name__ == '__main__':
    # 假设已有 train.txt / val.txt 列表
    root = 'D:/U-net/UNet_Demo/UNet_Demo/VOCdevkit'
    with open(os.path.join(root, 'VOC2025', 'ImageSets', 'Segmentation', 'train.txt')) as f:
        train_names = [line.strip() for line in f]
    train_dataset = SegmentationDataset(train_names, root, img_size=(512,512), num_classes=21, train=True)
    train_loader  = DataLoader(
        train_dataset,
        batch_size=4,
        shuffle=True,
        num_workers=min(8, os.cpu_count()-1),
        pin_memory=True
    )
    for imgs, masks in train_loader:
        print(imgs.shape, masks.shape)
        break
