"""
学习率调度器工具
"""
import math
import torch
from torch.optim.lr_scheduler import _LRScheduler
import logging
from typing import Dict, List, Optional, Union

logger = logging.getLogger(__name__)

class GradualWarmupScheduler(_LRScheduler):
    """
    逐渐预热学习率调度器

    首先将学习率从 base_lr 线性增加到 base_lr*multiplier，
    然后使用指定的调度器进行后续学习率调度

    Args:
        optimizer: 优化器
        multiplier: 预热期间学习率的最大倍数
        total_epoch: 预热的总轮数
        after_scheduler: 预热后使用的调度器
    """
    def __init__(self, optimizer, multiplier, total_epoch, after_scheduler=None):
        self.multiplier = multiplier
        self.total_epoch = total_epoch
        self.after_scheduler = after_scheduler
        self.finished = False
        super().__init__(optimizer)

    def get_lr(self):
        if self.last_epoch > self.total_epoch:
            if self.after_scheduler:
                if not self.finished:
                    self.after_scheduler.base_lrs = [base_lr * self.multiplier for base_lr in self.base_lrs]
                    self.finished = True
                return self.after_scheduler.get_lr()
            return [base_lr * self.multiplier for base_lr in self.base_lrs]

        return [base_lr * ((self.multiplier - 1.) * self.last_epoch / self.total_epoch + 1.) for base_lr in self.base_lrs]

    def step(self, epoch=None):
        if self.finished and self.after_scheduler:
            if epoch is None:
                self.after_scheduler.step(None)
            else:
                self.after_scheduler.step(epoch - self.total_epoch)
        else:
            return super().step(epoch)

class CosineAnnealingWarmupRestarts(_LRScheduler):
    """
    余弦退火预热重启调度器

    Args:
        optimizer: 优化器
        first_cycle_steps: 第一个周期的步数
        cycle_mult: 每个周期后的周期长度乘数
        max_lr: 最大学习率
        min_lr: 最小学习率
        warmup_steps: 预热步数
        gamma: 每个周期后学习率衰减因子
    """
    def __init__(self,
                 optimizer: torch.optim.Optimizer,
                 first_cycle_steps: int,
                 cycle_mult: float = 1.,
                 max_lr: float = 0.1,
                 min_lr: float = 0.001,
                 warmup_steps: int = 0,
                 gamma: float = 1.,
                 last_epoch: int = -1
                 ):
        assert warmup_steps < first_cycle_steps

        self.first_cycle_steps = first_cycle_steps
        self.cycle_mult = cycle_mult
        self.base_max_lr = max_lr
        self.max_lr = max_lr
        self.min_lr = min_lr
        self.warmup_steps = warmup_steps
        self.gamma = gamma

        self.cycle = 0
        self.cycle_steps = first_cycle_steps

        super(CosineAnnealingWarmupRestarts, self).__init__(optimizer, last_epoch)

        # 设置初始学习率
        self.init_lr()

    def init_lr(self):
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = self.min_lr

    def get_lr(self):
        if self.last_epoch < self.warmup_steps:
            # 线性预热
            return [self.min_lr + (self.max_lr - self.min_lr) * self.last_epoch / self.warmup_steps
                    for _ in self.base_lrs]
        else:
            # 余弦退火
            current_step = self.last_epoch - self.warmup_steps
            current_cycle_steps = self.cycle_steps

            # 检查是否需要重启
            if current_step >= current_cycle_steps:
                self.cycle += 1
                current_step = 0
                self.cycle_steps = int(self.cycle_steps * self.cycle_mult)
                self.max_lr = self.max_lr * self.gamma

            # 计算余弦退火学习率
            return [self.min_lr + 0.5 * (self.max_lr - self.min_lr) *
                   (1 + math.cos(math.pi * current_step / current_cycle_steps))
                   for _ in self.base_lrs]

    def step(self, epoch=None):
        if epoch is None:
            epoch = self.last_epoch + 1
        self.last_epoch = epoch

        for param_group, lr in zip(self.optimizer.param_groups, self.get_lr()):
            param_group['lr'] = lr

def build_scheduler(optimizer, cfg: Dict, steps_per_epoch: Optional[int] = None, total_epochs: Optional[int] = None):
    """
    构建学习率调度器

    Args:
        optimizer: 优化器
        cfg: 配置字典
        steps_per_epoch: 每轮的步数
        total_epochs: 总轮数

    Returns:
        学习率调度器
    """
    from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau, OneCycleLR

    if total_epochs is None:
        total_epochs = cfg['train']['total_epochs']

    sched_cfg = cfg['scheduler']
    sched_type = sched_cfg['type']

    logger.info(f"构建学习率调度器: {sched_type}")

    if sched_type == 'cosine':
        # 余弦退火
        scheduler = CosineAnnealingLR(
            optimizer,
            T_max=total_epochs - sched_cfg.get('warmup_epochs', 0),
            eta_min=float(sched_cfg.get('min_lr', 1e-6))
        )
        logger.info(f"使用余弦退火学习率调度，T_max={total_epochs - sched_cfg.get('warmup_epochs', 0)}，"
                   f"最小学习率={float(sched_cfg.get('min_lr', 1e-6))}")

    elif sched_type == 'plateau':
        # 根据验证指标调整学习率
        scheduler = ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=float(sched_cfg.get('factor', 0.5)),
            patience=int(sched_cfg.get('patience', 10)),
            min_lr=float(sched_cfg.get('min_lr', 1e-6)),
            verbose=True
        )
        logger.info(f"使用ReduceLROnPlateau学习率调度，factor={float(sched_cfg.get('factor', 0.5))}，"
                   f"patience={int(sched_cfg.get('patience', 10))}，"
                   f"最小学习率={float(sched_cfg.get('min_lr', 1e-6))}")

    elif sched_type == 'onecycle':
        # One Cycle Policy
        if steps_per_epoch is None:
            raise ValueError("OneCycleLR需要指定steps_per_epoch参数")

        scheduler = OneCycleLR(
            optimizer,
            max_lr=float(cfg['train']['init_lr']),
            total_steps=steps_per_epoch * total_epochs,
            pct_start=0.3,
            div_factor=25.0,
            final_div_factor=1000.0
        )
        logger.info(f"使用OneCycleLR学习率调度，最大学习率={float(cfg['train']['init_lr'])}，"
              f"总步数={steps_per_epoch * total_epochs}")

    elif sched_type == 'cosine_warmup':
        # 余弦退火+预热
        if steps_per_epoch is None:
            raise ValueError("cosine_warmup需要指定steps_per_epoch参数")

        warmup_epochs = int(sched_cfg.get('warmup_epochs', 10))
        cycles = int(sched_cfg.get('cycles', 3))
        min_lr = float(sched_cfg.get('min_lr', 1e-7))
        max_lr = float(sched_cfg.get('max_lr', 0.001))

        # 计算每个周期的长度
        cycle_length = total_epochs // cycles

        scheduler = CosineAnnealingWarmupRestarts(
            optimizer,
            first_cycle_steps=cycle_length * steps_per_epoch,
            warmup_steps=warmup_epochs * steps_per_epoch,
            max_lr=max_lr,
            min_lr=min_lr,
            cycle_mult=1.0,
            gamma=0.8  # 每个周期后学习率衰减因子
        )

        logger.info(f"使用余弦退火+预热学习率调度，"
                   f"预热轮数={warmup_epochs}，"
                   f"周期数={cycles}，"
                   f"周期长度={cycle_length}，"
                   f"最大学习率={max_lr}，"
                   f"最小学习率={min_lr}")

    elif sched_type == 'reduce_on_plateau':
        # 改进1(1).py中使用的ReduceLROnPlateau调度器
        factor = float(sched_cfg.get('factor', 0.5))
        patience = int(sched_cfg.get('patience', 3))
        threshold = float(sched_cfg.get('threshold', 0.01))
        cooldown = int(sched_cfg.get('cooldown', 0))
        min_lr = float(sched_cfg.get('min_lr', 1e-6))

        scheduler = ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=factor,
            patience=patience,
            threshold=threshold,
            cooldown=cooldown,
            min_lr=min_lr,
            verbose=True
        )

        logger.info(f"使用ReduceLROnPlateau学习率调度（改进版），factor={factor}，"
              f"patience={patience}，threshold={threshold}，"
              f"cooldown={cooldown}，最小学习率={min_lr}")

    else:
        raise ValueError(f"不支持的调度器类型: {sched_type}")

    # 应用预热
    if sched_type not in ['onecycle', 'cosine_warmup'] and sched_cfg.get('warmup_epochs', 0) > 0:
        warmup_epochs = int(sched_cfg.get('warmup_epochs', 0))
        logger.info(f"应用学习率预热，预热轮数={warmup_epochs}")
        scheduler = GradualWarmupScheduler(
            optimizer,
            multiplier=1.0,
            total_epoch=warmup_epochs,
            after_scheduler=scheduler
        )

    return scheduler

# 新增优化的调度器类
class CosineAnnealingWarmupLR(_LRScheduler):
    """
    余弦退火学习率调度器，支持warmup - 优化版本
    """

    def __init__(self, optimizer, T_max, eta_min=0, warmup_epochs=0, warmup_factor=0.1, last_epoch=-1):
        self.T_max = T_max
        self.eta_min = eta_min
        self.warmup_epochs = warmup_epochs
        self.warmup_factor = warmup_factor
        super(CosineAnnealingWarmupLR, self).__init__(optimizer, last_epoch)

    def get_lr(self):
        if self.last_epoch < self.warmup_epochs:
            # Warmup阶段
            alpha = self.last_epoch / self.warmup_epochs
            warmup_factor = self.warmup_factor * (1 - alpha) + alpha
            return [base_lr * warmup_factor for base_lr in self.base_lrs]
        else:
            # 余弦退火阶段
            epoch = self.last_epoch - self.warmup_epochs
            T_max = self.T_max - self.warmup_epochs
            return [self.eta_min + (base_lr - self.eta_min) *
                   (1 + math.cos(math.pi * epoch / T_max)) / 2
                   for base_lr in self.base_lrs]

class LayerWiseLR:
    """
    分层学习率管理器 - 为不同层设置不同的学习率
    """

    def __init__(self, model, base_lr=1e-3, layer_decay=0.8):
        self.model = model
        self.base_lr = base_lr
        self.layer_decay = layer_decay

    def get_parameter_groups(self):
        """获取分层参数组"""
        parameter_groups = []

        # 编码器参数
        encoder_params = []
        for name, param in self.model.named_parameters():
            if 'backbone' in name or 'encoder' in name:
                encoder_params.append(param)

        if encoder_params:
            parameter_groups.append({
                'params': encoder_params,
                'lr': self.base_lr * self.layer_decay ** 2,
                'name': 'encoder'
            })

        # 解码器参数
        decoder_params = []
        for name, param in self.model.named_parameters():
            if 'decoder' in name:
                decoder_params.append(param)

        if decoder_params:
            parameter_groups.append({
                'params': decoder_params,
                'lr': self.base_lr * self.layer_decay,
                'name': 'decoder'
            })

        # 分割头参数
        head_params = []
        for name, param in self.model.named_parameters():
            if 'head' in name or 'classifier' in name:
                head_params.append(param)

        if head_params:
            parameter_groups.append({
                'params': head_params,
                'lr': self.base_lr,
                'name': 'head'
            })

        return parameter_groups

def get_optimized_scheduler(optimizer, config):
    """
    根据配置创建优化的学习率调度器
    """
    scheduler_config = config.get('scheduler', {})
    scheduler_type = scheduler_config.get('type', 'cosine_annealing')

    if scheduler_type == 'cosine_annealing':
        return CosineAnnealingWarmupLR(
            optimizer,
            T_max=scheduler_config.get('T_max', 300),
            eta_min=scheduler_config.get('eta_min', 1e-6),
            warmup_epochs=scheduler_config.get('warmup_epochs', 10),
            warmup_factor=scheduler_config.get('warmup_factor', 0.1)
        )

    elif scheduler_type == 'reduce_on_plateau':
        from torch.optim.lr_scheduler import ReduceLROnPlateau
        return ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=scheduler_config.get('factor', 0.5),
            patience=scheduler_config.get('patience', 10),
            verbose=True,
            min_lr=scheduler_config.get('min_lr', 1e-7)
        )

    else:
        raise ValueError(f"不支持的调度器类型: {scheduler_type}")

def create_layerwise_optimizer(model, config):
    """
    创建分层学习率优化器
    """
    training_config = config.get('training', {})
    base_lr = training_config.get('init_lr', 1e-3)
    layer_decay = training_config.get('layer_decay', 0.8)

    # 创建分层学习率管理器
    layerwise_lr = LayerWiseLR(model, base_lr, layer_decay)
    parameter_groups = layerwise_lr.get_parameter_groups()

    # 创建优化器
    import torch.optim as optim
    optimizer = optim.AdamW(
        parameter_groups,
        weight_decay=training_config.get('weight_decay', 1e-4)
    )

    return optimizer
