# Configuration for Semantic Segmentation Training - Fully Optimized Version

data:
  dataset_path: "."  # 根目录，用于类别权重计算
  root_dir: "VOCdevkit"
  train_list: "VOC2025/ImageSets/Segmentation/train.txt"
  val_list:   "VOC2025/ImageSets/Segmentation/val.txt"
  train_mask_paths:
    - "VOC2025/SegmentationClass/*.png"
  input_size: [512, 512]
  num_classes: 29

  # 数据增强设置 - 强化版
  augmentation:
    # 基础几何变换
    random_resize_crop:
      enabled: true
      scale: [0.5, 1.0]      # 保持适度的缩放范围
      ratio: [0.8, 1.3]      # 保持适度的宽高比范围
      p: 0.8                 # 高概率应用
    horizontal_flip:
      enabled: true
      p: 0.5
    vertical_flip:           # 添加垂直翻转
      enabled: true
      p: 0.3                 # 增加垂直翻转概率
    rotate:                  # 添加旋转
      enabled: true
      limit: 15              # 增加最大旋转角度
      p: 0.5                 # 增加旋转概率

    # 特定领域增强 - 针对农村景观
    domain_specific:
      enabled: true
      p: 0.8                 # 增加概率

    # 随机裁剪 - 禁用，避免尺寸不一致
    random_crop:
      enabled: false
      height: 384
      width: 384
      p: 0.0

    # 颜色和光照变换 - 强化版
    color_jitter:
      enabled: true
      brightness: 0.3        # 保持适度的亮度变化
      contrast: 0.3          # 保持适度的对比度变化
      saturation: 0.3        # 保持适度的饱和度变化
      hue: 0.1               # 保持适度的色调变化
      p: 0.7                 # 增加概率

    # 添加高斯噪声和模糊
    gaussian_noise:
      enabled: true
      var_limit: [5, 30]     # 保持适度的噪声范围
      p: 0.3                 # 增加概率
    gaussian_blur:
      enabled: true
      blur_limit: 5          # 保持适度的模糊范围
      p: 0.3                 # 增加概率

    # 添加网格变形
    grid_distortion:
      enabled: true
      p: 0.2                 # 增加概率

    # 添加粗糙丢弃
    coarse_dropout:
      enabled: true
      max_holes: 8           # 增加最大孔洞数
      max_height: 32         # 增加最大高度
      max_width: 32          # 增加最大宽度
      p: 0.2                 # 增加概率

model:
  backbone: "resnet50"         # 使用ADE20K支持的骨干网络
  pretrained: true
  pretrained_weights: ""       # 预训练权重路径，留空则使用ImageNet权重
  use_attention: true          # 启用注意力机制
  attention_type: "cbam"       # 使用CBAM注意力机制
  dropout_rate: 0.2            # 适度的dropout率
  transfer_learning: true      # 启用迁移学习
  finetune_mode: "progressive" # 使用渐进式微调策略
  label_alignment: true        # 启用标签对齐，将我们的标签与ADE20K标签对齐
  decoder_channels: [256, 128, 64, 32, 16]  # 增加解码器通道数
  use_deep_supervision: true   # 启用深度监督，提供多尺度监督信号

pretrain:
  enabled: true                # 启用预训练
  dataset: "ADE20K"            # 使用ADE20K数据集预训练权重
  finetune_method: "gradual"   # 使用渐进式微调

loss:
  use_ce: true                 # 启用交叉熵损失
  use_dice: true               # 启用Dice损失
  use_focal: true              # 启用Focal Loss，更好地处理类别不平衡
  use_lovasz: true             # 启用Lovász Loss，直接优化IoU
  weight_ce: 0.4               # 降低交叉熵损失权重
  weight_dice: 1.2             # 增加Dice权重
  weight_focal: 0.8            # 增加Focal Loss权重
  weight_lovasz: 1.6           # 增加Lovász权重
  focal_gamma: 2.0             # 适度的gamma值
  focal_alpha: 0.6             # 增加alpha，更关注少数类
  use_class_weights: true      # 启用类别权重
  class_weight_method: "inverse_frequency"  # 使用逆频率方法计算权重
  label_smoothing: 0.1         # 适度的标签平滑
  ignore_index: 255            # 使用标准的忽略索引

  # 知识蒸馏设置
  use_distillation: false      # 暂不使用知识蒸馏
  distillation_alpha: 0.5      # 蒸馏损失权重
  distillation_temperature: 3.0  # 温度参数

optimizer:
  type: "adamw"                # 使用AdamW优化器
  beta1: 0.9                   # Adam/AdamW的beta1参数
  beta2: 0.999                 # Adam/AdamW的beta2参数
  momentum: 0.9                # SGD的动量参数

# 学习率策略优化
scheduler:
  # 使用余弦退火+预热调度器
  type: "cosine_warmup"        # 使用余弦退火+预热调度器
  max_lr: 0.003                # 增加最大学习率
  warmup_epochs: 5             # 适度的预热轮数
  cycles: 1                    # 使用单个周期
  pct_start: 0.05              # 减少预热时间比例，更快达到最大学习率
  div_factor: 10.0             # 适度的除数因子
  final_div_factor: 100.0      # 适度的最终除数因子
  min_lr: 1e-6                 # 适度的最小学习率

train:
  use_cuda: true
  total_epochs: 200            # 增加训练轮数
  # 分层学习率设置 - 更激进的学习率分配
  init_lr: 0.003               # 增加基础学习率
  encoder_lr: 1e-3             # 增加编码器学习率
  decoder_lr: 2e-3             # 增加解码器学习率
  head_lr: 3e-3                # 增加分割头学习率
  weight_decay: 2e-4           # 适度的权重衰减

  # 渐进式解冻设置 - 更平缓的策略
  progressive_unfreezing: true  # 启用渐进式解冻
  freeze_epochs: 0              # 传统冻结轮数，设为0表示使用渐进式解冻
  unfreeze_schedule:
    0: ["segmentation_head", "decoder"]  # 第0轮开始训练分割头和解码器
    5: ["encoder.layer4"]                # 第5轮开始解冻编码器最后一层
    10: ["encoder.layer3"]               # 第10轮开始解冻更多层
    15: ["encoder.layer2"]               # 第15轮开始解冻更多层
    20: ["encoder.layer1"]               # 第20轮开始解冻所有层

  # 批量大小设置 - 针对RTX 4070 Ti Super 16GB优化
  freeze_batch_size: 12        # 增加冻结阶段批量大小
  unfreeze_batch_size: 6       # 增加解冻阶段批量大小
  num_workers: 6               # 增加工作线程数

  # 其他训练设置
  save_dir: "logs"
  eval_period: 1
  early_stopping: 50           # 增加早停耐心值，给模型更多学习时间
  mixed_precision: true        # 使用混合精度训练
  gradient_accumulation: 4     # 增加梯度累积步数
  gradient_clip: 1.5           # 增加梯度裁剪阈值
  use_channels_last: true      # 使用channels_last内存格式，提高GPU利用率
  benchmark_cudnn: true        # 启用cudnn基准测试，优化卷积操作
  verbose: true
