data:
  augmentation:
    coarse_dropout:
      enabled: true
      max_height: 32
      max_holes: 8
      max_width: 32
      p: 0.2
    color_jitter:
      brightness: 0.4
      contrast: 0.4
      enabled: true
      hue: 0.2
      p: 0.8
      saturation: 0.4
    domain_specific:
      enabled: true
      p: 0.9
    gaussian_blur:
      blur_limit: 7
      enabled: true
      p: 0.3
    gaussian_noise:
      enabled: true
      p: 0.3
      var_limit:
      - 10
      - 50
    grid_distortion:
      enabled: true
      p: 0.2
    horizontal_flip:
      enabled: true
      p: 0.5
    random_crop:
      enabled: false
      height: 384
      p: 0.0
      width: 384
    random_resize_crop:
      enabled: true
      p: 0.9
      ratio:
      - 0.7
      - 1.4
      scale:
      - 0.4
      - 1.0
    rotate:
      enabled: true
      limit: 15
      p: 0.5
    vertical_flip:
      enabled: true
      p: 0.3
  dataset_path: .
  input_size:
  - 512
  - 512
  num_classes: 29
  root_dir: VOCdevkit
  train_list: VOC2025/ImageSets/Segmentation/train.txt
  train_mask_paths:
  - VOC2025/SegmentationClass/*.png
  val_list: VOC2025/ImageSets/Segmentation/val.txt
loss:
  class_weight_method: inverse_frequency
  distillation_alpha: 0.5
  distillation_temperature: 3.0
  focal_alpha: 0.6
  focal_gamma: 3.0
  ignore_index: 255
  label_smoothing: 0.1
  use_ce: true
  use_class_weights: true
  use_dice: true
  use_distillation: false
  use_focal: true
  use_lovasz: true
  weight_ce: 0.6
  weight_dice: 1.2
  weight_focal: 0.8
  weight_lovasz: 1.8
model:
  attention_type: cbam
  backbone: resnet50
  dropout_rate: 0.25
  finetune_mode: progressive
  label_alignment: true
  pretrained: true
  pretrained_weights: ''
  transfer_learning: true
  use_attention: true
optimizer:
  beta1: 0.9
  beta2: 0.999
  momentum: 0.9
  type: adamw
pretrain:
  dataset: ADE20K
  enabled: true
  finetune_method: gradual
  weights_path: ''
scheduler:
  cooldown: 0
  factor: 0.5
  max_lr: 0.002
  min_lr: 5.0e-06
  patience: 3
  threshold: 0.01
  type: cosine_warmup
  warmup_epochs: 10
train:
  benchmark_cudnn: true
  decoder_lr: 1e-3
  early_stopping: 50
  encoder_lr: 5e-4
  eval_period: 1
  freeze_batch_size: 12
  freeze_epochs: 0
  gradient_accumulation: 4
  gradient_clip: 1.5
  head_lr: 2e-3
  init_lr: 0.002
  mixed_precision: true
  num_workers: 8
  progressive_unfreezing: true
  save_dir: logs
  total_epochs: 300
  unfreeze_batch_size: 6
  unfreeze_schedule:
    0:
    - segmentation_head
    - decoder
    3:
    - encoder.layer4
    6:
    - encoder.layer3
    9:
    - encoder.layer2
    12:
    - encoder.layer1
  use_channels_last: true
  use_cuda: true
  verbose: true
  weight_decay: 5e-4
