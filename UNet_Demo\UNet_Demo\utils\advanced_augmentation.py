#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级数据增强模块
专门为语义分割任务设计的强化数据增强策略
目标：提升模型泛化能力，突破mIoU 0.4
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
import random
from PIL import Image, ImageEnhance, ImageFilter
import albumentations as A
from albumentations.pytorch import ToTensorV2

class AdvancedSegmentationAugmentation:
    """高级语义分割数据增强"""
    
    def __init__(self, img_size=(512, 512), num_classes=29):
        self.img_size = img_size
        self.num_classes = num_classes
        
        # 训练时增强
        self.train_transform = A.<PERSON>se([
            # 几何变换
            A.RandomResizedCrop(
                height=img_size[0], 
                width=img_size[1], 
                scale=(0.3, 1.2),
                ratio=(0.6, 1.6),
                p=0.95
            ),
            <PERSON><PERSON>(p=0.5),
            <PERSON><PERSON>(p=0.3),
            <PERSON><PERSON>(p=0.5),
            <PERSON><PERSON>(limit=30, p=0.7),
            <PERSON><PERSON>(
                shift_limit=0.1,
                scale_limit=0.2,
                rotate_limit=15,
                p=0.6
            ),
            
            # 颜色变换
            A.ColorJitter(
                brightness=0.4,
                contrast=0.4,
                saturation=0.4,
                hue=0.2,
                p=0.8
            ),
            A.RandomBrightnessContrast(
                brightness_limit=0.3,
                contrast_limit=0.3,
                p=0.7
            ),
            A.HueSaturationValue(
                hue_shift_limit=20,
                sat_shift_limit=30,
                val_shift_limit=20,
                p=0.6
            ),
            
            # 噪声和模糊
            A.GaussianBlur(blur_limit=(3, 7), p=0.3),
            A.MotionBlur(blur_limit=7, p=0.2),
            A.GaussNoise(var_limit=(10, 50), p=0.3),
            A.ISONoise(color_shift=(0.01, 0.05), intensity=(0.1, 0.5), p=0.2),
            
            # 天气效果
            A.RandomRain(p=0.1),
            A.RandomFog(p=0.1),
            A.RandomSunFlare(p=0.05),
            A.RandomShadow(p=0.1),
            
            # 像素级变换
            A.CLAHE(clip_limit=2.0, p=0.3),
            A.Sharpen(alpha=(0.2, 0.5), lightness=(0.5, 1.0), p=0.2),
            A.Emboss(alpha=(0.2, 0.5), strength=(0.2, 0.7), p=0.1),
            
            # 擦除和遮挡
            A.CoarseDropout(
                max_holes=8,
                max_height=32,
                max_width=32,
                min_holes=1,
                min_height=8,
                min_width=8,
                fill_value=0,
                p=0.3
            ),
            
            # 归一化
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])
        
        # 验证时增强（轻微）
        self.val_transform = A.Compose([
            A.Resize(height=img_size[0], width=img_size[1]),
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])
    
    def __call__(self, image, mask, is_train=True):
        """应用数据增强"""
        if is_train:
            transformed = self.train_transform(image=image, mask=mask)
        else:
            transformed = self.val_transform(image=image, mask=mask)
        
        return transformed['image'], transformed['mask']

class CutMixAugmentation:
    """CutMix数据增强"""
    
    def __init__(self, alpha=1.0, p=0.3):
        self.alpha = alpha
        self.p = p
    
    def __call__(self, images, targets):
        """应用CutMix增强"""
        if random.random() > self.p:
            return images, targets
        
        batch_size = images.size(0)
        indices = torch.randperm(batch_size)
        
        # 生成lambda
        lam = np.random.beta(self.alpha, self.alpha)
        
        # 生成边界框
        W, H = images.size(2), images.size(3)
        cut_rat = np.sqrt(1. - lam)
        cut_w = int(W * cut_rat)
        cut_h = int(H * cut_rat)
        
        cx = np.random.randint(W)
        cy = np.random.randint(H)
        
        bbx1 = np.clip(cx - cut_w // 2, 0, W)
        bby1 = np.clip(cy - cut_h // 2, 0, H)
        bbx2 = np.clip(cx + cut_w // 2, 0, W)
        bby2 = np.clip(cy + cut_h // 2, 0, H)
        
        # 应用CutMix
        images[:, :, bbx1:bbx2, bby1:bby2] = images[indices, :, bbx1:bbx2, bby1:bby2]
        targets[:, bbx1:bbx2, bby1:bby2] = targets[indices, bbx1:bbx2, bby1:bby2]
        
        return images, targets

class MixUpAugmentation:
    """MixUp数据增强"""
    
    def __init__(self, alpha=0.4, p=0.2):
        self.alpha = alpha
        self.p = p
    
    def __call__(self, images, targets):
        """应用MixUp增强"""
        if random.random() > self.p:
            return images, targets
        
        batch_size = images.size(0)
        indices = torch.randperm(batch_size)
        
        # 生成lambda
        lam = np.random.beta(self.alpha, self.alpha)
        
        # 应用MixUp
        mixed_images = lam * images + (1 - lam) * images[indices]
        mixed_targets = lam * targets.float() + (1 - lam) * targets[indices].float()
        
        return mixed_images, mixed_targets.long()

class TestTimeAugmentation:
    """测试时增强 (TTA)"""
    
    def __init__(self, scales=[0.75, 1.0, 1.25, 1.5], flip=True, rotate=[0, 90, 180, 270]):
        self.scales = scales
        self.flip = flip
        self.rotate = rotate
    
    def augment_image(self, image):
        """生成增强图像列表"""
        augmented_images = []
        
        for scale in self.scales:
            # 缩放
            h, w = image.shape[-2:]
            new_h, new_w = int(h * scale), int(w * scale)
            scaled_image = F.interpolate(
                image.unsqueeze(0), 
                size=(new_h, new_w), 
                mode='bilinear', 
                align_corners=False
            ).squeeze(0)
            
            # 裁剪或填充到原始尺寸
            if scale > 1.0:
                # 中心裁剪
                start_h = (new_h - h) // 2
                start_w = (new_w - w) // 2
                scaled_image = scaled_image[:, start_h:start_h+h, start_w:start_w+w]
            else:
                # 填充
                pad_h = (h - new_h) // 2
                pad_w = (w - new_w) // 2
                scaled_image = F.pad(scaled_image, (pad_w, pad_w, pad_h, pad_h))
            
            augmented_images.append(scaled_image)
            
            # 翻转
            if self.flip:
                augmented_images.append(torch.flip(scaled_image, dims=[2]))  # 水平翻转
                augmented_images.append(torch.flip(scaled_image, dims=[1]))  # 垂直翻转
            
            # 旋转
            for angle in self.rotate:
                if angle != 0:
                    rotated = self.rotate_image(scaled_image, angle)
                    augmented_images.append(rotated)
        
        return augmented_images
    
    def rotate_image(self, image, angle):
        """旋转图像"""
        if angle == 90:
            return torch.rot90(image, k=1, dims=[1, 2])
        elif angle == 180:
            return torch.rot90(image, k=2, dims=[1, 2])
        elif angle == 270:
            return torch.rot90(image, k=3, dims=[1, 2])
        else:
            return image
    
    def merge_predictions(self, predictions):
        """合并多个预测结果"""
        # 简单平均
        merged = torch.stack(predictions).mean(dim=0)
        return merged

class AdvancedDataLoader:
    """高级数据加载器，集成所有增强策略"""
    
    def __init__(self, dataset, batch_size=4, num_workers=4, use_cutmix=True, use_mixup=True):
        self.dataset = dataset
        self.batch_size = batch_size
        self.num_workers = num_workers
        
        # 增强策略
        self.cutmix = CutMixAugmentation() if use_cutmix else None
        self.mixup = MixUpAugmentation() if use_mixup else None
        
        # 基础数据加载器
        self.dataloader = torch.utils.data.DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True,
            drop_last=True
        )
    
    def __iter__(self):
        for images, targets in self.dataloader:
            # 应用批量级增强
            if self.cutmix and random.random() < 0.3:
                images, targets = self.cutmix(images, targets)
            elif self.mixup and random.random() < 0.2:
                images, targets = self.mixup(images, targets)
            
            yield images, targets
    
    def __len__(self):
        return len(self.dataloader)

def create_advanced_augmentation_pipeline(img_size=(512, 512), num_classes=29):
    """创建高级数据增强管道"""
    return AdvancedSegmentationAugmentation(img_size, num_classes)

def create_tta_pipeline(scales=[0.75, 1.0, 1.25, 1.5]):
    """创建测试时增强管道"""
    return TestTimeAugmentation(scales=scales)

if __name__ == "__main__":
    # 测试增强管道
    aug = create_advanced_augmentation_pipeline()
    print("✅ 高级数据增强管道创建成功")
    
    tta = create_tta_pipeline()
    print("✅ 测试时增强管道创建成功")
