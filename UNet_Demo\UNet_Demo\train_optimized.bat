@echo off
echo ===== 优化训练启动脚本 =====
echo 针对i7-14700KF + RTX 4070 Ti Super 16GB配置优化

REM 设置PyTorch环境变量以优化性能
echo 设置PyTorch环境变量...
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
set CUDA_LAUNCH_BLOCKING=0
set CUDA_DEVICE_ORDER=PCI_BUS_ID
set CUDA_VISIBLE_DEVICES=0

REM 设置NumPy环境变量
set OMP_NUM_THREADS=8
set MKL_NUM_THREADS=8

REM 清理CUDA缓存
echo 清理CUDA缓存...
python -c "import torch; torch.cuda.empty_cache()"

REM 启动GPU监控
echo 启动GPU内存监控...
start cmd /k "python gpu_memory_monitor.py --optimize --interval 2"

REM 启动TensorBoard
echo 启动TensorBoard...
start cmd /k "tensorboard --logdir=logs/current"

REM 启动训练
echo 启动优化训练（学习率策略优化）...
python train.py --cfg config_optimized.yaml

echo 训练完成！
pause
