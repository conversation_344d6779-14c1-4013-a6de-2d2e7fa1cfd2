#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Selenium环境
"""

import os
import sys

def test_selenium_import():
    """测试Selenium导入"""
    try:
        import selenium
        print(f"✅ Selenium版本: {selenium.__version__}")
        return True
    except ImportError as e:
        print(f"❌ Selenium导入失败: {e}")
        return False

def test_webdriver_manager():
    """测试webdriver-manager"""
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        print("✅ webdriver-manager导入成功")
        return True
    except ImportError as e:
        print(f"❌ webdriver-manager导入失败: {e}")
        return False

def test_chrome_driver():
    """测试ChromeDriver"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        print("正在测试ChromeDriver...")
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        # 方法1: 直接使用
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.get("https://www.baidu.com")
            title = driver.title
            driver.quit()
            print(f"✅ 方法1成功 - 页面标题: {title}")
            return True
        except Exception as e:
            print(f"❌ 方法1失败: {e}")
        
        # 方法2: 使用webdriver-manager
        try:
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service
            
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.get("https://www.baidu.com")
            title = driver.title
            driver.quit()
            print(f"✅ 方法2成功 - 页面标题: {title}")
            return True
        except Exception as e:
            print(f"❌ 方法2失败: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ ChromeDriver测试失败: {e}")
        return False

def check_chrome_installation():
    """检查Chrome安装"""
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME'))
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ 找到Chrome: {path}")
            return True
    
    print("❌ 未找到Chrome浏览器")
    return False

def main():
    """主函数"""
    print("🔍 Selenium环境测试")
    print("=" * 40)
    
    # 测试Python环境
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    
    # 测试各个组件
    tests = [
        ("Selenium导入", test_selenium_import),
        ("webdriver-manager", test_webdriver_manager),
        ("Chrome安装", check_chrome_installation),
        ("ChromeDriver", test_chrome_driver)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n📊 测试结果总结:")
    print("=" * 40)
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    if all_passed:
        print("\n🎉 所有测试通过！可以运行问卷星自动化脚本")
    else:
        print("\n⚠️ 部分测试失败，需要解决环境问题")
        print("\n💡 解决建议:")
        print("1. 确保已安装Chrome浏览器")
        print("2. 检查网络连接")
        print("3. 尝试手动下载ChromeDriver")

if __name__ == "__main__":
    main()
