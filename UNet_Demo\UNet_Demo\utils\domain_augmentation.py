"""
特定领域的数据增强 - 针对农村景观图像
增强版 - 支持更多数据增强方法
"""
import albumentations as A
from albumentations.pytorch import ToTensorV2
import logging
import numpy as np
import torch
from typing import Dict, List, Optional, Tuple, Union

logger = logging.getLogger(__name__)

def get_domain_specific_augmentation(cfg: Dict) -> <PERSON><PERSON>:
    """
    获取针对农村景观的特定领域数据增强 - 增强版

    Args:
        cfg: 配置字典

    Returns:
        Albumentations Compose对象
    """
    # 获取配置
    aug_cfg = cfg.get('data', {}).get('augmentation', {})
    input_size = cfg.get('data', {}).get('input_size', [512, 512])

    # 获取各种增强配置
    resize_crop_cfg = aug_cfg.get('random_resize_crop', {})
    h_flip_cfg = aug_cfg.get('horizontal_flip', {})
    v_flip_cfg = aug_cfg.get('vertical_flip', {})
    rotate_cfg = aug_cfg.get('rotate', {})
    domain_cfg = aug_cfg.get('domain_specific', {})
    color_cfg = aug_cfg.get('color_jitter', {})
    random_crop_cfg = aug_cfg.get('random_crop', {})
    gaussian_noise_cfg = aug_cfg.get('gaussian_noise', {})
    gaussian_blur_cfg = aug_cfg.get('gaussian_blur', {})

    # 创建变换列表
    transforms = []

    # 首先添加调整大小变换，确保所有图像都是相同的大小
    transforms.append(
        A.Resize(
            height=input_size[0],
            width=input_size[1],
            p=1.0
        )
    )
    logger.info(f"添加Resize: height={input_size[0]}, width={input_size[1]}, p=1.0")

    # 添加随机裁剪和缩放
    if resize_crop_cfg.get('enabled', True):
        scale = resize_crop_cfg.get('scale', [0.5, 1.0])
        ratio = resize_crop_cfg.get('ratio', [0.75, 1.33])
        p = resize_crop_cfg.get('p', 0.8)
        transforms.append(
            A.RandomResizedCrop(
                height=input_size[0],
                width=input_size[1],
                scale=scale,
                ratio=ratio,
                p=p
            )
        )
        logger.info(f"添加RandomResizedCrop: scale={scale}, ratio={ratio}, p={p}")

    # 添加随机裁剪
    if random_crop_cfg.get('enabled', False):
        height = random_crop_cfg.get('height', 384)
        width = random_crop_cfg.get('width', 384)
        p = random_crop_cfg.get('p', 0.5)
        transforms.append(
            A.RandomCrop(
                height=height,
                width=width,
                p=p
            )
        )
        logger.info(f"添加RandomCrop: height={height}, width={width}, p={p}")

    # 添加水平翻转
    if h_flip_cfg.get('enabled', True):
        p = h_flip_cfg.get('p', 0.5)
        transforms.append(A.HorizontalFlip(p=p))
        logger.info(f"添加HorizontalFlip: p={p}")

    # 添加垂直翻转
    if v_flip_cfg.get('enabled', False):
        p = v_flip_cfg.get('p', 0.3)
        transforms.append(A.VerticalFlip(p=p))
        logger.info(f"添加VerticalFlip: p={p}")

    # 添加旋转
    if rotate_cfg.get('enabled', False):
        limit = rotate_cfg.get('limit', 15)
        p = rotate_cfg.get('p', 0.5)
        transforms.append(A.Rotate(limit=limit, p=p))
        logger.info(f"添加Rotate: limit={limit}, p={p}")

    # 添加特定领域增强
    if domain_cfg.get('enabled', True):
        p = domain_cfg.get('p', 0.8)
        transforms.append(
            A.OneOf([
                # 模拟不同时间的光照条件
                A.RandomShadow(
                    shadow_roi=(0, 0, 1, 1),
                    num_shadows_lower=1,
                    num_shadows_upper=3,
                    p=1.0
                ),
                # 模拟阳光效果
                A.RandomSunFlare(
                    flare_roi=(0, 0, 1, 1),
                    angle_lower=0,
                    angle_upper=1,
                    p=1.0
                ),
                # 模拟雾效果 - 农村早晨常见
                A.RandomFog(
                    fog_coef_lower=0.1,
                    fog_coef_upper=0.3,
                    p=1.0
                ),
                # 模拟雨效果
                A.RandomRain(
                    slant_lower=-10,
                    slant_upper=10,
                    drop_length=20,
                    drop_width=1,
                    p=1.0
                ),
                # 模拟雪效果
                A.RandomSnow(
                    snow_point_lower=0.1,
                    snow_point_upper=0.3,
                    brightness_coeff=2.5,
                    p=1.0
                ),
                # 模拟季节变化 - 秋季
                A.RGBShift(
                    r_shift_limit=15,
                    g_shift_limit=15,
                    b_shift_limit=15,
                    p=1.0
                ),
            ], p=p)
        )
        logger.info(f"添加特定领域增强: p={p}")

    # 添加颜色抖动
    if color_cfg.get('enabled', True):
        brightness = color_cfg.get('brightness', 0.3)
        contrast = color_cfg.get('contrast', 0.3)
        saturation = color_cfg.get('saturation', 0.3)
        hue = color_cfg.get('hue', 0.1)
        p = color_cfg.get('p', 0.7)
        transforms.append(
            A.ColorJitter(
                brightness=brightness,
                contrast=contrast,
                saturation=saturation,
                hue=hue,
                p=p
            )
        )
        logger.info(f"添加ColorJitter: brightness={brightness}, contrast={contrast}, "
                   f"saturation={saturation}, hue={hue}, p={p}")

    # 添加高斯噪声
    if gaussian_noise_cfg.get('enabled', False):
        var_limit = gaussian_noise_cfg.get('var_limit', [10, 50])
        p = gaussian_noise_cfg.get('p', 0.3)
        transforms.append(
            A.GaussNoise(
                var_limit=var_limit,
                p=p
            )
        )
        logger.info(f"添加GaussNoise: var_limit={var_limit}, p={p}")

    # 添加高斯模糊
    if gaussian_blur_cfg.get('enabled', False):
        blur_limit = gaussian_blur_cfg.get('blur_limit', 7)
        p = gaussian_blur_cfg.get('p', 0.3)
        transforms.append(
            A.GaussianBlur(
                blur_limit=blur_limit,
                p=p
            )
        )
        logger.info(f"添加GaussianBlur: blur_limit={blur_limit}, p={p}")

    # 添加网格变形 - 从改进1(1).py借鉴
    grid_distortion_cfg = aug_cfg.get('grid_distortion', {})
    if grid_distortion_cfg.get('enabled', False):
        p = grid_distortion_cfg.get('p', 0.2)
        transforms.append(
            A.GridDistortion(p=p)
        )
        logger.info(f"添加GridDistortion: p={p}")

    # 添加粗糙丢弃 - 从改进1(1).py借鉴
    coarse_dropout_cfg = aug_cfg.get('coarse_dropout', {})
    if coarse_dropout_cfg.get('enabled', False):
        max_holes = coarse_dropout_cfg.get('max_holes', 8)
        max_height = coarse_dropout_cfg.get('max_height', 32)
        max_width = coarse_dropout_cfg.get('max_width', 32)
        p = coarse_dropout_cfg.get('p', 0.2)
        transforms.append(
            A.CoarseDropout(
                max_holes=max_holes,
                max_height=max_height,
                max_width=max_width,
                p=p
            )
        )
        logger.info(f"添加CoarseDropout: max_holes={max_holes}, max_height={max_height}, max_width={max_width}, p={p}")

    # 添加标准化和转换为张量
    transforms.extend([
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ])

    return A.Compose(transforms, additional_targets={"mask": "mask"})

def get_validation_augmentation(cfg: Dict) -> A.Compose:
    """
    获取验证集数据增强

    Args:
        cfg: 配置字典

    Returns:
        Albumentations Compose对象
    """
    input_size = cfg.get('data', {}).get('input_size', [512, 512])

    logger.info(f"验证集调整大小: height={input_size[0]}, width={input_size[1]}")

    return A.Compose([
        A.Resize(height=input_size[0], width=input_size[1], p=1.0),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ], additional_targets={"mask": "mask"})

def get_mixup_augmentation(x, y, alpha=0.2):
    """
    MixUp数据增强

    Args:
        x: 输入图像
        y: 标签
        alpha: Beta分布参数

    Returns:
        混合后的图像和标签
    """
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).to(x.device)

    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]
    return mixed_x, y_a, y_b, lam
