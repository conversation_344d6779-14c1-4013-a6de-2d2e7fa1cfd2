#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强训练脚本 V2.0
同时改进：1.模型架构 2.类别平衡 3.过拟合问题
目标：mIoU > 0.6
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import torch.nn.functional as F
import numpy as np
import os
import time
from datetime import datetime
import logging
import albumentations as A
from albumentations.pytorch import ToTensorV2
import segmentation_models_pytorch as smp
from sklearn.utils.class_weight import compute_class_weight
import cv2
from PIL import Image
import json

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🚀 使用设备: {device}")

class EnhancedConfig:
    """增强配置类"""

    # 1. 模型架构升级
    BACKBONE_OPTIONS = [
        'efficientnet-b7',      # 最强EfficientNet
        'timm-efficientnet-b8', # 更强版本
        'mit_b5',               # Mix Transformer
        'tu-convnext_large',    # ConvNeXt Large
    ]

    BACKBONE = 'efficientnet-b7'  # 主力模型
    ENCODER_WEIGHTS = 'imagenet'
    ARCHITECTURE = 'UnetPlusPlus'  # 升级到UNet++

    # 2. 训练参数优化
    EPOCHS = 300
    BATCH_SIZE = 16  # 根据显存调整
    LEARNING_RATE = 1e-4
    WEIGHT_DECAY = 1e-5

    # 3. 数据增强强化
    STRONG_AUGMENTATION = True
    MIXUP_ALPHA = 0.2
    CUTMIX_ALPHA = 1.0

    # 4. 类别平衡策略
    SMART_CLASS_WEIGHTS = True
    FOCAL_LOSS_GAMMA = 2.0
    DICE_SMOOTH = 1e-6

    # 5. 正则化策略
    DROPOUT_RATE = 0.3
    LABEL_SMOOTHING = 0.1
    GRADIENT_CLIPPING = 1.0

    # 6. 学习率调度
    SCHEDULER = 'cosine_warmup'
    WARMUP_EPOCHS = 20
    MIN_LR = 1e-7

    # 7. 早停和保存
    PATIENCE = 50
    SAVE_TOP_K = 5

class EnhancedDataAugmentation:
    """增强数据增强"""

    @staticmethod
    def get_training_transforms():
        """获取训练时的强化数据增强"""
        return A.Compose([
            # 几何变换
            A.RandomRotate90(p=0.5),
            A.Flip(p=0.5),
            A.Transpose(p=0.5),
            A.ShiftScaleRotate(
                shift_limit=0.1,
                scale_limit=0.2,
                rotate_limit=30,
                p=0.7
            ),

            # 弹性变形
            A.ElasticTransform(
                alpha=1,
                sigma=50,
                alpha_affine=50,
                p=0.3
            ),

            # 光学变换
            A.OpticalDistortion(p=0.3),
            A.GridDistortion(p=0.3),

            # 颜色增强
            A.RandomBrightnessContrast(
                brightness_limit=0.3,
                contrast_limit=0.3,
                p=0.8
            ),
            A.HueSaturationValue(
                hue_shift_limit=20,
                sat_shift_limit=30,
                val_shift_limit=20,
                p=0.8
            ),
            A.CLAHE(p=0.5),
            A.RandomGamma(p=0.5),

            # 噪声和模糊
            A.OneOf([
                A.GaussNoise(var_limit=(10, 50)),
                A.MultiplicativeNoise(),
                A.ISONoise(),
            ], p=0.5),

            A.OneOf([
                A.MotionBlur(blur_limit=5),
                A.MedianBlur(blur_limit=5),
                A.GaussianBlur(blur_limit=5),
            ], p=0.3),

            # 天气效果
            A.OneOf([
                A.RandomRain(p=0.3),
                A.RandomFog(p=0.3),
                A.RandomSunFlare(p=0.2),
            ], p=0.2),

            # 首先调整到固定尺寸
            A.Resize(height=256, width=256, p=1.0),

            # 标准化
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])

    @staticmethod
    def get_validation_transforms():
        """验证时的变换"""
        return A.Compose([
            A.Resize(height=256, width=256, p=1.0),
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])

class SmartClassWeighting:
    """智能类别权重计算"""

    def __init__(self, num_classes=29):
        self.num_classes = num_classes
        self.class_frequencies = None
        self.smart_weights = None

    def compute_frequencies(self, dataloader):
        """计算类别频率"""
        print("📊 计算类别频率...")
        class_counts = np.zeros(self.num_classes)
        total_pixels = 0

        for batch_idx, (_, masks) in enumerate(dataloader):
            if batch_idx % 50 == 0:
                print(f"  处理批次 {batch_idx}/{len(dataloader)}")

            for mask in masks:
                mask_np = mask.numpy()
                unique, counts = np.unique(mask_np, return_counts=True)
                for cls, count in zip(unique, counts):
                    if cls < self.num_classes:
                        class_counts[cls] += count
                        total_pixels += count

        self.class_frequencies = class_counts / total_pixels
        return self.class_frequencies

    def compute_smart_weights(self, strategy='balanced_focal'):
        """计算智能权重"""
        if self.class_frequencies is None:
            raise ValueError("请先计算类别频率")

        if strategy == 'balanced_focal':
            # 平衡 + Focal Loss 策略
            # 1. 基础平衡权重
            base_weights = 1.0 / (self.class_frequencies + 1e-8)

            # 2. 对极少类别额外加权
            rare_threshold = np.percentile(self.class_frequencies, 20)  # 最少20%的类别
            rare_mask = self.class_frequencies < rare_threshold
            base_weights[rare_mask] *= 3.0  # 稀有类别3倍权重

            # 3. 对优势类别降权
            dominant_threshold = np.percentile(self.class_frequencies, 80)  # 最多20%的类别
            dominant_mask = self.class_frequencies > dominant_threshold
            base_weights[dominant_mask] *= 0.3  # 优势类别降权

            # 4. 平滑处理
            weights = np.sqrt(base_weights)  # 平方根平滑
            weights = weights / np.mean(weights)  # 归一化

        elif strategy == 'effective_number':
            # Effective Number策略
            beta = 0.9999
            effective_num = 1.0 - np.power(beta, self.class_frequencies * 1000000)
            weights = (1.0 - beta) / effective_num
            weights = weights / np.mean(weights)

        else:
            # 简单逆频率
            weights = 1.0 / (self.class_frequencies + 1e-8)
            weights = weights / np.mean(weights)

        self.smart_weights = weights

        # 打印权重信息
        print(f"\n⚖️ 智能类别权重 ({strategy}):")
        for i in range(self.num_classes):
            freq = self.class_frequencies[i]
            weight = weights[i]
            print(f"  类别{i:2d}: 频率={freq:.6f}, 权重={weight:.2f}")

        return weights

class EnhancedLoss:
    """增强损失函数"""

    def __init__(self, class_weights, num_classes=29):
        self.class_weights = torch.FloatTensor(class_weights).to(device)
        self.num_classes = num_classes

        # 多种损失函数
        self.ce_loss = nn.CrossEntropyLoss(
            weight=self.class_weights,
            label_smoothing=EnhancedConfig.LABEL_SMOOTHING
        )
        self.focal_loss = self.focal_loss_fn
        self.dice_loss = self.dice_loss_fn
        self.lovasz_loss = self.lovasz_softmax

    def focal_loss_fn(self, inputs, targets, gamma=2.0):
        """Focal Loss"""
        ce_loss = F.cross_entropy(inputs, targets, weight=self.class_weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** gamma * ce_loss
        return focal_loss.mean()

    def dice_loss_fn(self, inputs, targets):
        """Dice Loss"""
        inputs = F.softmax(inputs, dim=1)
        targets_one_hot = F.one_hot(targets, self.num_classes).permute(0, 3, 1, 2).float()

        intersection = (inputs * targets_one_hot).sum(dim=(2, 3))
        union = inputs.sum(dim=(2, 3)) + targets_one_hot.sum(dim=(2, 3))

        dice = (2 * intersection + EnhancedConfig.DICE_SMOOTH) / (union + EnhancedConfig.DICE_SMOOTH)
        return 1 - dice.mean()

    def lovasz_softmax(self, inputs, targets):
        """Lovász-Softmax Loss (简化版)"""
        inputs = F.softmax(inputs, dim=1)
        targets_one_hot = F.one_hot(targets, self.num_classes).permute(0, 3, 1, 2).float()

        # 简化的Lovász损失
        intersection = (inputs * targets_one_hot).sum(dim=(2, 3))
        union = inputs.sum(dim=(2, 3)) + targets_one_hot.sum(dim=(2, 3)) - intersection

        iou = (intersection + 1e-8) / (union + 1e-8)
        return 1 - iou.mean()

    def combined_loss(self, inputs, targets):
        """组合损失"""
        ce = self.ce_loss(inputs, targets)
        focal = self.focal_loss(inputs, targets, EnhancedConfig.FOCAL_LOSS_GAMMA)
        dice = self.dice_loss(inputs, targets)
        lovasz = self.lovasz_loss(inputs, targets)

        # 动态权重
        total_loss = 0.3 * ce + 0.4 * focal + 0.2 * dice + 0.1 * lovasz

        return total_loss, {
            'ce': ce.item(),
            'focal': focal.item(),
            'dice': dice.item(),
            'lovasz': lovasz.item()
        }

class EnhancedModel(nn.Module):
    """增强模型架构"""

    def __init__(self, num_classes=29):
        super().__init__()

        # 使用segmentation_models_pytorch的UNet++
        self.model = smp.UnetPlusPlus(
            encoder_name=EnhancedConfig.BACKBONE,
            encoder_weights=EnhancedConfig.ENCODER_WEIGHTS,
            in_channels=3,
            classes=num_classes,
            activation=None,  # 我们手动添加
        )

        # 添加Dropout正则化
        self.dropout = nn.Dropout2d(p=EnhancedConfig.DROPOUT_RATE)

        # 注意力机制
        self.attention = self.create_attention_module(num_classes)

    def create_attention_module(self, num_classes):
        """创建注意力模块"""
        return nn.Sequential(
            nn.Conv2d(num_classes, num_classes // 4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(num_classes // 4, num_classes, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        # 主干网络
        features = self.model(x)

        # 应用Dropout
        features = self.dropout(features)

        # 注意力机制
        attention_weights = self.attention(features)
        features = features * attention_weights

        return features

class CosineWarmupScheduler:
    """余弦退火 + 预热学习率调度器"""

    def __init__(self, optimizer, warmup_epochs, total_epochs, min_lr=1e-7):
        self.optimizer = optimizer
        self.warmup_epochs = warmup_epochs
        self.total_epochs = total_epochs
        self.min_lr = min_lr
        self.base_lr = optimizer.param_groups[0]['lr']

    def step(self, epoch):
        if epoch < self.warmup_epochs:
            # 预热阶段
            lr = self.base_lr * (epoch + 1) / self.warmup_epochs
        else:
            # 余弦退火阶段
            progress = (epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)
            lr = self.min_lr + (self.base_lr - self.min_lr) * 0.5 * (1 + np.cos(np.pi * progress))

        for param_group in self.optimizer.param_groups:
            param_group['lr'] = lr

        return lr

def setup_logging():
    """设置日志"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"logs/enhanced_v2_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'{log_dir}/training.log'),
            logging.StreamHandler()
        ]
    )

    return log_dir, logging.getLogger(__name__)

def load_datasets():
    """加载数据集"""
    from enhanced_dataset import create_enhanced_datasets

    # 创建数据集
    train_dataset, val_dataset = create_enhanced_datasets()

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=EnhancedConfig.BATCH_SIZE,
        shuffle=True,
        num_workers=0,  # 使用单进程避免多进程问题
        pin_memory=True,
        drop_last=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=EnhancedConfig.BATCH_SIZE,
        shuffle=False,
        num_workers=0,  # 使用单进程避免多进程问题
        pin_memory=True
    )

    return train_loader, val_loader

def train_epoch(model, train_loader, criterion, optimizer, scheduler, epoch, logger):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    loss_components = {'ce': 0, 'focal': 0, 'dice': 0, 'lovasz': 0}

    for batch_idx, (images, masks) in enumerate(train_loader):
        images, masks = images.to(device), masks.to(device)

        # 前向传播
        optimizer.zero_grad()
        outputs = model(images)

        # 计算损失
        loss, components = criterion.combined_loss(outputs, masks)

        # 反向传播
        loss.backward()

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), EnhancedConfig.GRADIENT_CLIPPING)

        optimizer.step()

        # 统计
        total_loss += loss.item()
        for key, value in components.items():
            loss_components[key] += value

        # 打印进度
        if batch_idx % 50 == 0:
            current_lr = scheduler.optimizer.param_groups[0]['lr']
            logger.info(f"Epoch {epoch}, Batch {batch_idx}/{len(train_loader)}, "
                       f"Loss: {loss.item():.4f}, LR: {current_lr:.2e}")

    # 计算平均损失
    avg_loss = total_loss / len(train_loader)
    avg_components = {k: v / len(train_loader) for k, v in loss_components.items()}

    return avg_loss, avg_components

def validate_epoch(model, val_loader, criterion, epoch, logger):
    """验证一个epoch"""
    model.eval()
    total_loss = 0
    total_iou = 0
    class_ious = np.zeros(29)

    with torch.no_grad():
        for batch_idx, (images, masks) in enumerate(val_loader):
            images, masks = images.to(device), masks.to(device)

            outputs = model(images)
            loss, _ = criterion.combined_loss(outputs, masks)
            total_loss += loss.item()

            # 计算IoU
            predictions = torch.argmax(outputs, dim=1)

            for cls in range(29):
                pred_mask = (predictions == cls)
                true_mask = (masks == cls)

                intersection = (pred_mask & true_mask).sum().float()
                union = (pred_mask | true_mask).sum().float()

                if union > 0:
                    class_ious[cls] += (intersection / union).item()

    avg_loss = total_loss / len(val_loader)
    avg_class_ious = class_ious / len(val_loader)
    mean_iou = np.mean(avg_class_ious[avg_class_ious > 0])  # 只计算有效类别

    return avg_loss, mean_iou, avg_class_ious

def main():
    """主训练函数"""
    print("🚀 启动增强训练 V2.0")
    print("=" * 60)
    print(f"📋 配置信息:")
    print(f"  模型架构: {EnhancedConfig.ARCHITECTURE} + {EnhancedConfig.BACKBONE}")
    print(f"  训练轮数: {EnhancedConfig.EPOCHS}")
    print(f"  批次大小: {EnhancedConfig.BATCH_SIZE}")
    print(f"  学习率: {EnhancedConfig.LEARNING_RATE}")
    print(f"  强化增强: {EnhancedConfig.STRONG_AUGMENTATION}")
    print(f"  智能权重: {EnhancedConfig.SMART_CLASS_WEIGHTS}")

    # 设置日志
    log_dir, logger = setup_logging()

    # 保存配置
    config_dict = {k: v for k, v in EnhancedConfig.__dict__.items() if not k.startswith('_')}
    with open(f'{log_dir}/config.json', 'w') as f:
        json.dump(config_dict, f, indent=2)

    logger.info("🎯 目标: mIoU > 0.6")
    logger.info("🔧 同时改进: 模型架构 + 类别平衡 + 过拟合")

    # 加载数据
    print("\n📊 加载数据集...")
    train_loader, val_loader = load_datasets()
    logger.info(f"训练集: {len(train_loader)} 批次")
    logger.info(f"验证集: {len(val_loader)} 批次")

    # 计算智能类别权重
    if EnhancedConfig.SMART_CLASS_WEIGHTS:
        print("\n⚖️ 计算智能类别权重...")
        weight_calculator = SmartClassWeighting()
        weight_calculator.compute_frequencies(train_loader)
        class_weights = weight_calculator.compute_smart_weights('balanced_focal')
    else:
        class_weights = np.ones(29)

    # 创建模型
    print(f"\n🧠 创建模型: {EnhancedConfig.ARCHITECTURE} + {EnhancedConfig.BACKBONE}")
    model = EnhancedModel(num_classes=29).to(device)

    # 创建损失函数
    criterion = EnhancedLoss(class_weights)

    # 创建优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=EnhancedConfig.LEARNING_RATE,
        weight_decay=EnhancedConfig.WEIGHT_DECAY
    )

    # 创建学习率调度器
    scheduler = CosineWarmupScheduler(
        optimizer,
        warmup_epochs=EnhancedConfig.WARMUP_EPOCHS,
        total_epochs=EnhancedConfig.EPOCHS,
        min_lr=EnhancedConfig.MIN_LR
    )

    # 训练循环
    best_miou = 0
    patience_counter = 0

    print(f"\n🎯 开始训练 {EnhancedConfig.EPOCHS} 轮...")

    for epoch in range(EnhancedConfig.EPOCHS):
        start_time = time.time()

        # 更新学习率
        current_lr = scheduler.step(epoch)

        # 训练
        train_loss, train_components = train_epoch(
            model, train_loader, criterion, optimizer, scheduler, epoch, logger
        )

        # 验证
        val_loss, val_miou, class_ious = validate_epoch(
            model, val_loader, criterion, epoch, logger
        )

        epoch_time = time.time() - start_time

        # 记录结果
        logger.info(f"Epoch {epoch+1}/{EnhancedConfig.EPOCHS}")
        logger.info(f"  训练损失: {train_loss:.4f}")
        logger.info(f"  验证损失: {val_loss:.4f}")
        logger.info(f"  验证mIoU: {val_miou:.4f}")
        logger.info(f"  学习率: {current_lr:.2e}")
        logger.info(f"  耗时: {epoch_time:.1f}s")

        # 保存最佳模型
        if val_miou > best_miou:
            best_miou = val_miou
            patience_counter = 0

            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_miou': best_miou,
                'class_weights': class_weights,
            }, f'{log_dir}/best_model.pth')

            logger.info(f"🎉 新的最佳mIoU: {best_miou:.4f}")
        else:
            patience_counter += 1

        # 早停检查
        if patience_counter >= EnhancedConfig.PATIENCE:
            logger.info(f"⏹️ 早停触发，最佳mIoU: {best_miou:.4f}")
            break

        # 保存检查点
        if (epoch + 1) % 20 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'miou': val_miou,
            }, f'{log_dir}/checkpoint_epoch_{epoch+1}.pth')

    logger.info(f"🎊 训练完成！最佳mIoU: {best_miou:.4f}")
    print(f"\n🎊 训练完成！")
    print(f"📊 最佳mIoU: {best_miou:.4f}")
    print(f"📁 模型保存在: {log_dir}")

    if best_miou > 0.6:
        print("🎯 恭喜！达到目标 mIoU > 0.6！")
    elif best_miou > 0.5:
        print("👍 不错！mIoU > 0.5，继续优化可达到目标！")
    else:
        print("💪 需要进一步优化，但已有显著改进！")

if __name__ == "__main__":
    main()
