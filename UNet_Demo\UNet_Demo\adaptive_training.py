#!/usr/bin/env python3
"""
自适应训练脚本
根据训练过程中的表现动态调整训练策略
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import logging
from pathlib import Path
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.unet import Unet
from utils.dataloader import SegmentationDataset
from utils.class_weights import compute_class_weights
from utils.losses import CombinedLoss
from utils.callbacks import LossHistory, EvalCallback

class AdaptiveTrainer:
    """自适应训练器"""
    
    def __init__(self, config_path):
        self.config_path = config_path
        self.load_config()
        self.setup_logging()
        self.setup_device()
        
        # 自适应参数
        self.adaptation_history = []
        self.stagnation_counter = 0
        self.best_miou = 0.0
        self.adaptation_threshold = 5  # 连续多少轮无改善后触发自适应
        
    def load_config(self):
        """加载配置"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
    
    def setup_logging(self):
        """设置日志"""
        log_dir = self.config['logging']['log_dir']
        os.makedirs(log_dir, exist_ok=True)
        
        self.logger = logging.getLogger('adaptive_trainer')
        self.logger.setLevel(logging.INFO)
        
        # 清除已有handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # 文件handler
        log_file = os.path.join(log_dir, f'adaptive_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 控制台handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def setup_device(self):
        """设置设备"""
        self.device = torch.device('cuda' if torch.cuda.is_available() and self.config['hardware']['use_cuda'] else 'cpu')
        self.logger.info(f"使用设备: {self.device}")
    
    def create_model(self):
        """创建模型"""
        model = Unet(
            num_classes=self.config['data']['num_classes'],
            pretrained=self.config['model']['pretrained'],
            backbone=self.config['model']['backbone']
        )
        return model.to(self.device)
    
    def create_data_loaders(self):
        """创建数据加载器"""
        train_dataset = SegmentationDataset(
            annotation_path=self.config['data']['train_annotation_path'],
            input_shape=self.config['data']['input_shape'][:2],
            num_classes=self.config['data']['num_classes'],
            train=True
        )
        
        val_dataset = SegmentationDataset(
            annotation_path=self.config['data']['val_annotation_path'],
            input_shape=self.config['data']['input_shape'][:2],
            num_classes=self.config['data']['num_classes'],
            train=False
        )
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=True,
            num_workers=self.config['hardware']['num_workers'],
            pin_memory=self.config['hardware']['pin_memory']
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=False,
            num_workers=self.config['hardware']['num_workers'],
            pin_memory=self.config['hardware']['pin_memory']
        )
        
        return train_loader, val_loader
    
    def create_class_weights(self):
        """创建类别权重"""
        weights = torch.ones(self.config['data']['num_classes'])
        
        # 根据配置设置困难类别权重
        difficult_classes = self.config['class_optimization']['difficult_classes']
        class_specific_weights = self.config['class_optimization'].get('class_specific_weights', {})
        
        for cls in difficult_classes:
            weights[cls] = class_specific_weights.get(cls, 10.0)
        
        return weights.to(self.device)
    
    def adapt_learning_rate(self, optimizer, adaptation_type):
        """自适应调整学习率"""
        if adaptation_type == 'reduce':
            # 降低学习率
            for param_group in optimizer.param_groups:
                param_group['lr'] *= 0.5
            self.logger.info(f"降低学习率到: {optimizer.param_groups[0]['lr']:.2e}")
        
        elif adaptation_type == 'increase':
            # 增加学习率（谨慎使用）
            for param_group in optimizer.param_groups:
                param_group['lr'] *= 1.2
            self.logger.info(f"增加学习率到: {optimizer.param_groups[0]['lr']:.2e}")
        
        elif adaptation_type == 'reset':
            # 重置学习率
            init_lr = self.config['training']['learning_rate']['init_lr']
            for param_group in optimizer.param_groups:
                param_group['lr'] = init_lr
            self.logger.info(f"重置学习率到: {init_lr:.2e}")
    
    def adapt_loss_weights(self, criterion, adaptation_type):
        """自适应调整损失权重"""
        if adaptation_type == 'increase_focal':
            # 增加Focal Loss权重
            criterion.w_focal *= 1.5
            self.logger.info(f"增加Focal Loss权重到: {criterion.w_focal:.2f}")
        
        elif adaptation_type == 'increase_tversky':
            # 增加Tversky Loss权重
            if hasattr(criterion, 'w_tversky'):
                criterion.w_tversky *= 1.3
                self.logger.info(f"增加Tversky Loss权重到: {criterion.w_tversky:.2f}")
        
        elif adaptation_type == 'balance_weights':
            # 重新平衡损失权重
            criterion.w_ce = 0.2
            criterion.w_dice = 1.8
            criterion.w_focal = 2.5
            if hasattr(criterion, 'w_tversky'):
                criterion.w_tversky = 1.5
            self.logger.info("重新平衡损失权重")
    
    def adapt_class_weights(self, criterion, class_performance):
        """根据类别表现自适应调整类别权重"""
        if hasattr(criterion, 'ce') and hasattr(criterion.ce, 'weight'):
            current_weights = criterion.ce.weight.clone()
            
            # 对表现差的类别增加权重
            for cls_id, iou in enumerate(class_performance):
                if iou < 0.05:  # 极差表现
                    current_weights[cls_id] *= 2.0
                elif iou < 0.15:  # 差表现
                    current_weights[cls_id] *= 1.5
            
            # 限制权重范围
            current_weights = torch.clamp(current_weights, 0.1, 50.0)
            criterion.ce.weight = current_weights
            
            self.logger.info("根据类别表现调整了类别权重")
    
    def should_adapt(self, current_miou, epoch):
        """判断是否需要自适应调整"""
        if current_miou > self.best_miou:
            self.best_miou = current_miou
            self.stagnation_counter = 0
            return False, None
        else:
            self.stagnation_counter += 1
            
            if self.stagnation_counter >= self.adaptation_threshold:
                # 根据停滞时间决定适应策略
                if self.stagnation_counter < 10:
                    return True, 'mild'
                elif self.stagnation_counter < 20:
                    return True, 'moderate'
                else:
                    return True, 'aggressive'
            
            return False, None
    
    def apply_adaptation(self, strategy, optimizer, criterion, class_performance=None):
        """应用自适应策略"""
        self.logger.info(f"应用自适应策略: {strategy}")
        
        if strategy == 'mild':
            # 温和调整
            self.adapt_learning_rate(optimizer, 'reduce')
            self.adapt_loss_weights(criterion, 'increase_focal')
        
        elif strategy == 'moderate':
            # 中等调整
            self.adapt_learning_rate(optimizer, 'reduce')
            self.adapt_loss_weights(criterion, 'increase_tversky')
            if class_performance is not None:
                self.adapt_class_weights(criterion, class_performance)
        
        elif strategy == 'aggressive':
            # 激进调整
            self.adapt_learning_rate(optimizer, 'reset')
            self.adapt_loss_weights(criterion, 'balance_weights')
            if class_performance is not None:
                self.adapt_class_weights(criterion, class_performance)
        
        # 记录适应历史
        self.adaptation_history.append({
            'epoch': len(self.adaptation_history),
            'strategy': strategy,
            'best_miou': self.best_miou,
            'stagnation_counter': self.stagnation_counter
        })
        
        # 重置停滞计数器
        self.stagnation_counter = 0
    
    def train_epoch(self, model, criterion, optimizer, train_loader, epoch):
        """训练一个epoch"""
        model.train()
        total_loss = 0.0
        num_batches = len(train_loader)
        
        for batch_idx, (images, masks) in enumerate(train_loader):
            images = images.to(self.device)
            masks = masks.to(self.device)
            
            optimizer.zero_grad()
            
            # 混合精度训练
            if self.config['hardware']['mixed_precision']:
                with torch.cuda.amp.autocast():
                    outputs = model(images)
                    loss = criterion(outputs, masks)
                
                self.scaler.scale(loss).backward()
                self.scaler.step(optimizer)
                self.scaler.update()
            else:
                outputs = model(images)
                loss = criterion(outputs, masks)
                loss.backward()
                optimizer.step()
            
            total_loss += loss.item()
            
            if batch_idx % 20 == 0:
                self.logger.info(f'Epoch {epoch+1}, Batch {batch_idx}/{num_batches}, Loss: {loss.item():.4f}')
        
        return total_loss / num_batches
    
    def train(self):
        """主训练循环"""
        self.logger.info("开始自适应训练")
        
        # 创建模型和数据
        model = self.create_model()
        train_loader, val_loader = self.create_data_loaders()
        class_weights = self.create_class_weights()
        
        # 创建损失函数和优化器
        criterion = CombinedLoss(self.config['loss'], class_weights)
        optimizer = optim.AdamW(
            model.parameters(),
            lr=self.config['training']['learning_rate']['init_lr'],
            weight_decay=self.config['training']['optimizer']['weight_decay']
        )
        
        # 混合精度
        if self.config['hardware']['mixed_precision']:
            self.scaler = torch.cuda.amp.GradScaler()
        
        # 创建回调
        log_dir = self.config['logging']['log_dir']
        loss_history = LossHistory(log_dir)
        eval_callback = EvalCallback(
            model, val_loader, self.config['data']['num_classes'],
            self.device, log_dir, eval_period=1
        )
        
        # 训练循环
        total_epochs = self.config['training']['total_epochs']
        
        for epoch in range(total_epochs):
            self.logger.info(f"Epoch {epoch+1}/{total_epochs}")
            
            # 训练
            train_loss = self.train_epoch(model, criterion, optimizer, train_loader, epoch)
            
            # 验证
            val_loss, val_miou = eval_callback.on_epoch_end(epoch)
            
            # 记录历史
            loss_history.append_loss(epoch + 1, train_loss, val_loss)
            
            # 检查是否需要自适应调整
            should_adapt, strategy = self.should_adapt(val_miou, epoch)
            
            if should_adapt:
                # 获取类别表现（如果需要）
                class_performance = None
                if strategy in ['moderate', 'aggressive']:
                    class_performance = eval_callback.get_class_ious()
                
                # 应用自适应策略
                self.apply_adaptation(strategy, optimizer, criterion, class_performance)
            
            # 保存最佳模型
            if val_miou > self.best_miou:
                self.best_miou = val_miou
                torch.save(model.state_dict(), os.path.join(log_dir, 'best_adaptive_model.pth'))
                self.logger.info(f"保存最佳模型，mIoU: {self.best_miou:.4f}")
            
            # 保存适应历史
            history_file = os.path.join(log_dir, 'adaptation_history.json')
            with open(history_file, 'w') as f:
                json.dump(self.adaptation_history, f, indent=2)
        
        self.logger.info(f"训练完成，最佳mIoU: {self.best_miou:.4f}")
        self.logger.info(f"总共进行了 {len(self.adaptation_history)} 次自适应调整")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='自适应训练')
    parser.add_argument('--config', default='config_immediate_improvements.yaml', help='配置文件路径')
    
    args = parser.parse_args()
    
    # 创建自适应训练器
    trainer = AdaptiveTrainer(args.config)
    
    # 开始训练
    trainer.train()

if __name__ == "__main__":
    main()
