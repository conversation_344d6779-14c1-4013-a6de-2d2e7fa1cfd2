#!/usr/bin/env python3
"""
立即改进训练脚本 - 针对当前mIoU 0.36的快速优化
目标：快速提升到0.4+，解决类别不平衡问题
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.unet import Unet
from utils.dataloader import SegmentationDataset
from utils.utils_fit import fit_one_epoch
from utils.utils_metrics import f_score
from utils.callbacks import LossHistory, EvalCallback
from utils.class_weights import compute_class_weights
from utils.losses import CombinedLoss
from utils.scheduler import get_optimized_scheduler

def setup_logging(log_dir):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)

    # 创建logger
    logger = logging.getLogger('immediate_improvements')
    logger.setLevel(logging.INFO)

    # 清除已有的handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 文件handler
    log_file = os.path.join(log_dir, f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 格式化
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def create_extreme_class_weights(num_classes=29):
    """创建极端类别权重，特别关注困难类别"""
    # 基础权重
    weights = torch.ones(num_classes)

    # 根据历史表现设置权重
    # 完全未学习的类别
    zero_performance_classes = [8, 18, 28]
    for cls in zero_performance_classes:
        weights[cls] = 20.0  # 极高权重

    # 表现差的类别
    poor_classes = [5, 7, 9, 10, 11, 14, 15, 17, 19, 22, 23, 24, 25, 26, 27]
    for cls in poor_classes:
        weights[cls] = 8.0  # 高权重

    # 中等表现类别
    medium_classes = [1, 2, 3, 4, 6, 12, 13, 16, 20, 21]
    for cls in medium_classes:
        weights[cls] = 2.0  # 中等权重

    # 背景类和表现好的类别
    weights[0] = 0.1  # 背景类低权重

    return weights

def create_optimized_model(config):
    """创建优化的模型"""
    model_config = config['model']

    # 创建基础模型
    model = Unet(
        num_classes=config['data']['num_classes'],
        pretrained=model_config['pretrained'],
        backbone=model_config['backbone']
    )

    # 如果配置了注意力机制，这里可以添加
    # 注意：需要修改Unet类来支持注意力机制

    return model

def train_one_epoch(model, criterion, optimizer, train_loader, device, epoch, total_epochs, scaler=None, logger=None):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    num_batches = len(train_loader)

    for batch_idx, (images, masks) in enumerate(train_loader):
        images = images.to(device)
        masks = masks.to(device)

        optimizer.zero_grad()

        if scaler is not None:
            # 混合精度训练
            with torch.cuda.amp.autocast():
                outputs = model(images)
                loss = criterion(outputs, masks)

            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        else:
            # 标准训练
            outputs = model(images)
            loss = criterion(outputs, masks)
            loss.backward()
            optimizer.step()

        total_loss += loss.item()

        # 打印进度
        if batch_idx % 10 == 0 and logger:
            logger.info(f'Epoch {epoch+1}/{total_epochs}, Batch {batch_idx}/{num_batches}, Loss: {loss.item():.4f}')

    avg_loss = total_loss / num_batches
    return avg_loss


def create_optimized_optimizer(model, config):
    """创建优化的优化器，支持分层学习率"""
    training_config = config['training']
    lr_config = training_config['learning_rate']
    layerwise_config = training_config.get('layerwise_lr', {})

    if layerwise_config.get('enabled', False):
        # 分层学习率
        backbone_params = []
        decoder_params = []
        head_params = []

        # 分离参数
        for name, param in model.named_parameters():
            if 'backbone' in name or 'resnet' in name or 'vgg' in name:
                backbone_params.append(param)
            elif 'final' in name or 'classifier' in name:
                head_params.append(param)
            else:
                decoder_params.append(param)

        # 创建参数组
        param_groups = [
            {
                'params': backbone_params,
                'lr': lr_config['init_lr'] * layerwise_config.get('backbone_lr_scale', 0.1)
            },
            {
                'params': decoder_params,
                'lr': lr_config['init_lr'] * layerwise_config.get('decoder_lr_scale', 1.0)
            },
            {
                'params': head_params,
                'lr': lr_config['init_lr'] * layerwise_config.get('head_lr_scale', 2.0)
            }
        ]

        optimizer = optim.AdamW(
            param_groups,
            weight_decay=training_config['optimizer']['weight_decay'],
            betas=(training_config['optimizer']['beta1'], training_config['optimizer']['beta2'])
        )
    else:
        # 标准优化器
        optimizer = optim.AdamW(
            model.parameters(),
            lr=lr_config['init_lr'],
            weight_decay=training_config['optimizer']['weight_decay'],
            betas=(training_config['optimizer']['beta1'], training_config['optimizer']['beta2'])
        )

    return optimizer

def main():
    # 加载配置
    config_path = "config_immediate_improvements.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and config['hardware']['use_cuda'] else 'cpu')
    print(f"使用设备: {device}")

    # 设置日志
    log_dir = config['logging']['log_dir']
    logger = setup_logging(log_dir)
    logger.info("开始立即改进训练")
    logger.info(f"配置文件: {config_path}")

    # 创建模型
    model = create_optimized_model(config)
    model = model.to(device)

    # 设置混合精度
    if config['hardware']['mixed_precision']:
        scaler = torch.cuda.amp.GradScaler()
    else:
        scaler = None

    # 创建数据集
    train_dataset = SegmentationDataset(
        annotation_path=config['data']['train_annotation_path'],
        input_shape=config['data']['input_shape'][:2],
        num_classes=config['data']['num_classes'],
        train=True
    )

    val_dataset = SegmentationDataset(
        annotation_path=config['data']['val_annotation_path'],
        input_shape=config['data']['input_shape'][:2],
        num_classes=config['data']['num_classes'],
        train=False
    )

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=True,
        num_workers=config['hardware']['num_workers'],
        pin_memory=config['hardware']['pin_memory']
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=False,
        num_workers=config['hardware']['num_workers'],
        pin_memory=config['hardware']['pin_memory']
    )

    # 创建极端类别权重
    class_weights = create_extreme_class_weights(config['data']['num_classes'])
    class_weights = class_weights.to(device)
    logger.info(f"极端类别权重: {class_weights}")

    # 创建损失函数
    criterion = CombinedLoss(config['loss'], class_weights)

    # 创建优化器
    optimizer = create_optimized_optimizer(model, config)

    # 创建学习率调度器
    scheduler_config = config['training'].get('learning_rate', {})
    if scheduler_config.get('scheduler') == 'cosine_restart':
        # 创建余弦重启调度器
        from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
        scheduler = CosineAnnealingWarmRestarts(
            optimizer,
            T_0=50,  # 第一次重启周期
            T_mult=1,  # 周期倍数
            eta_min=scheduler_config.get('min_lr', 5e-7)
        )
    else:
        # 使用默认的余弦退火调度器
        from torch.optim.lr_scheduler import CosineAnnealingLR
        scheduler = CosineAnnealingLR(
            optimizer,
            T_max=config['training']['total_epochs'],
            eta_min=scheduler_config.get('min_lr', 5e-7)
        )

    # 创建回调函数
    loss_history = LossHistory(log_dir)
    eval_callback = EvalCallback(
        model, val_loader, config['data']['num_classes'],
        device, log_dir, eval_period=1
    )

    # 训练循环
    total_epochs = config['training']['total_epochs']
    best_miou = 0.0

    logger.info("开始训练...")

    for epoch in range(total_epochs):
        logger.info(f"Epoch {epoch+1}/{total_epochs}")

        # 训练一个epoch
        train_loss = train_one_epoch(
            model, criterion, optimizer, train_loader,
            device, epoch, total_epochs, scaler=scaler, logger=logger
        )

        # 验证
        val_loss, val_miou = eval_callback.on_epoch_end(epoch)

        # 更新学习率
        if scheduler:
            scheduler.step()

        # 记录历史
        loss_history.append_loss(epoch + 1, train_loss, val_loss)

        # 保存最佳模型
        if val_miou > best_miou:
            best_miou = val_miou
            torch.save(model.state_dict(), os.path.join(log_dir, 'best_model.pth'))
            logger.info(f"保存最佳模型，mIoU: {best_miou:.4f}")

        # 早停检查
        early_stopping_config = config['evaluation']['early_stopping']
        if early_stopping_config['enabled']:
            # 简单的早停实现
            if epoch > early_stopping_config['patience']:
                recent_mious = eval_callback.miou_history[-early_stopping_config['patience']:]
                if all(miou < best_miou - early_stopping_config['min_delta'] for miou in recent_mious):
                    logger.info(f"早停触发，最佳mIoU: {best_miou:.4f}")
                    break

    logger.info(f"训练完成，最佳mIoU: {best_miou:.4f}")

if __name__ == "__main__":
    main()
