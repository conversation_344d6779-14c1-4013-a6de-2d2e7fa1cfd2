import argparse
import os
import torch
import torch.nn as nn
import torch.backends.cudnn as cudnn
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import yaml
import logging
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches

from nets.deeplabv3plus import unet
from utils.dataloader import SegmentationDataset
from utils.metrics import SegmentationMetric
from utils.utils import show_config
from utils.label_alignment import build_aligned_model

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Semantic Segmentation Evaluation")
    parser.add_argument('--cfg', type=str, default='config.yaml', help='Path to config file')
    parser.add_argument('--model_path', type=str, required=True, help='Path to model checkpoint')
    parser.add_argument('--output_dir', type=str, default='evaluation_results', help='Directory to save evaluation results')
    parser.add_argument('--num_samples', type=int, default=10, help='Number of samples to visualize')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use (cuda or cpu)')
    return parser.parse_args()

def load_config(path):
    with open(path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def load_model(model_path, cfg, device):
    """加载模型"""
    # 获取模型参数
    dropout_rate = cfg['model'].get('dropout_rate', 0.2)
    use_attention = cfg['model'].get('use_attention', True)
    attention_type = cfg['model'].get('attention_type', 'cbam')
    use_label_alignment = cfg['model'].get('label_alignment', False)

    # 如果启用了标签对齐
    if use_label_alignment:
        logger.info("使用标签对齐模型...")
        # 构建标签对齐模型
        model = build_aligned_model(cfg, device)
    else:
        # 创建基础模型
        model = unet(num_classes=cfg['data']['num_classes'],
                    backbone=cfg['model']['backbone'],
                    pretrained=False,
                    dropout_rate=dropout_rate,
                    use_attention=use_attention,
                    attention_type=attention_type)

    # 加载权重
    logger.info(f"加载模型权重: {model_path}")
    checkpoint = torch.load(model_path, map_location=device)
    
    # 打印checkpoint的键，帮助调试
    if isinstance(checkpoint, dict):
        logger.info(f"Checkpoint keys: {checkpoint.keys()}")
    
    # 检查checkpoint的结构
    if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"从'model_state_dict'加载权重")
    elif isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
        model.load_state_dict(checkpoint['state_dict'])
        logger.info(f"从'state_dict'加载权重")
    else:
        model.load_state_dict(checkpoint)
        logger.info(f"直接加载权重")
    
    model = model.to(device)
    model.eval()
    return model

def evaluate(model, val_loader, num_classes, device, ignore_index=-1):
    """评估模型性能"""
    metric = SegmentationMetric(num_classes, ignore_index=ignore_index)
    
    with torch.no_grad():
        for images, masks in tqdm(val_loader, desc="Evaluating"):
            images = images.to(device)
            masks = masks.to(device)
            
            outputs = model(images)
            
            # 如果模型输出是字典，获取预测结果
            if isinstance(outputs, dict):
                outputs = outputs['out']
            
            # 计算指标
            metric.update(outputs, masks)
    
    # 获取指标结果
    pixAcc, mIoU, IoU = metric.get()
    
    # 打印每个类别的IoU
    for i, iou in enumerate(IoU):
        logger.info(f"Class {i} IoU: {iou:.4f}")
    
    return pixAcc, mIoU, IoU

def visualize_predictions(model, val_loader, num_classes, device, output_dir, num_samples=10):
    """可视化模型预测结果"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取类别名称
    class_names = val_loader.dataset.class_names if hasattr(val_loader.dataset, 'class_names') else [f"Class {i}" for i in range(num_classes)]
    
    # 创建颜色映射
    colors = plt.cm.get_cmap('tab20', num_classes)
    
    with torch.no_grad():
        for i, (images, masks) in enumerate(val_loader):
            if i >= num_samples:
                break
            
            images = images.to(device)
            masks = masks.to(device)
            
            outputs = model(images)
            
            # 如果模型输出是字典，获取预测结果
            if isinstance(outputs, dict):
                outputs = outputs['out']
            
            # 获取预测结果
            preds = torch.argmax(outputs, dim=1)
            
            # 可视化
            for j in range(images.size(0)):
                if i * images.size(0) + j >= num_samples:
                    break
                
                # 转换为numpy数组
                image = images[j].cpu().numpy().transpose(1, 2, 0)
                mask = masks[j].cpu().numpy()
                pred = preds[j].cpu().numpy()
                
                # 标准化图像
                image = (image - image.min()) / (image.max() - image.min())
                
                # 创建图像
                fig, axes = plt.subplots(1, 3, figsize=(15, 5))
                
                # 原始图像
                axes[0].imshow(image)
                axes[0].set_title("Original Image")
                axes[0].axis('off')
                
                # 真实标签
                mask_rgb = np.zeros((mask.shape[0], mask.shape[1], 3))
                for k in range(num_classes):
                    mask_rgb[mask == k] = colors(k)[:3]
                axes[1].imshow(mask_rgb)
                axes[1].set_title("Ground Truth")
                axes[1].axis('off')
                
                # 预测结果
                pred_rgb = np.zeros((pred.shape[0], pred.shape[1], 3))
                for k in range(num_classes):
                    pred_rgb[pred == k] = colors(k)[:3]
                axes[2].imshow(pred_rgb)
                axes[2].set_title("Prediction")
                axes[2].axis('off')
                
                # 添加图例
                patches = [mpatches.Patch(color=colors(k)[:3], label=class_names[k]) for k in range(num_classes)]
                fig.legend(handles=patches, loc='lower center', ncol=min(5, num_classes))
                
                # 保存图像
                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, f"sample_{i * images.size(0) + j}.png"))
                plt.close()

def main():
    args = parse_args()
    cfg = load_config(args.cfg)
    
    # 设备配置
    device = torch.device(args.device if torch.cuda.is_available() and args.device == 'cuda' else 'cpu')
    
    # 优化CUDA性能
    if device.type == 'cuda':
        cudnn.benchmark = True
        torch.cuda.empty_cache()
    
    logger.info(f"使用设备: {device}")
    
    # 加载模型
    model = load_model(args.model_path, cfg, device)
    
    # 数据加载器
    logger.info("准备数据集...")
    
    # 构建完整的文件路径
    val_list_path = os.path.join(cfg['data']['root_dir'], cfg['data']['val_list'])
    
    # 读取文件列表
    with open(val_list_path, 'r', encoding='utf-8') as f:
        val_files = [line.strip() for line in f if line.strip()]
    
    logger.info(f"从文件列表中读取: 验证集 {len(val_files)} 个文件")
    
    # 创建数据集
    val_ds = SegmentationDataset(val_files,
                               cfg['data']['root_dir'],
                               tuple(cfg['data']['input_size']),
                               cfg['data']['num_classes'],
                               train=False,
                               cfg=cfg)
    
    logger.info(f"验证集: {len(val_ds)} 样本")
    
    # 批量大小
    batch_size = cfg['train']['unfreeze_batch_size']
    
    # 创建数据加载器
    val_loader = DataLoader(
        val_ds,
        batch_size=batch_size,
        shuffle=False,
        num_workers=cfg['train']['num_workers'],
        pin_memory=True,
        drop_last=False
    )
    
    # 忽略索引
    ignore_index = cfg['loss'].get('ignore_index', -1)
    if ignore_index == -1 or ignore_index is None:
        ignore_index = cfg['data']['num_classes']
    
    # 确保ignore_index是整数
    ignore_index = int(ignore_index)
    
    # 评估模型
    logger.info("开始评估模型...")
    pixAcc, mIoU, IoU = evaluate(model, val_loader, cfg['data']['num_classes'], device, ignore_index)
    
    logger.info(f"像素精度: {pixAcc:.4f}")
    logger.info(f"平均IoU: {mIoU:.4f}")
    
    # 可视化预测结果
    logger.info("可视化预测结果...")
    visualize_predictions(model, val_loader, cfg['data']['num_classes'], device, args.output_dir, args.num_samples)
    
    logger.info(f"评估完成，结果保存在: {args.output_dir}")

if __name__ == '__main__':
    main()
