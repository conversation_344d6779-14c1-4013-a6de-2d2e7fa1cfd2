# 更强Backbone配置 - 目标mIoU > 0.4
# 基于当前成功的极端类别平衡策略，使用更强的模型

model:
  # 使用更强的backbone
  backbone: "efficientnet-b7"    # 从resnet50升级到efficientnet-b7
  # 备选方案: "resnext101_32x8d", "wide_resnet101_2", "regnet_y_32gf"
  
  pretrained: true
  num_classes: 29
  
  # 模型增强设置
  use_attention: true           # 启用注意力机制
  attention_type: "cbam"        # 使用CBAM注意力
  use_deep_supervision: true    # 启用深度监督
  use_auxiliary_loss: true      # 启用辅助损失
  auxiliary_weight: 0.4         # 辅助损失权重

  # 数据增强设置 - 更强版本
  augmentation:
    # 基础几何变换
    random_resize_crop:
      enabled: true
      scale: [0.3, 1.2]          # 更大的缩放范围
      ratio: [0.6, 1.6]          # 更大的宽高比范围
      p: 0.95                    # 更高概率
    horizontal_flip:
      enabled: true
      p: 0.5
    vertical_flip:
      enabled: true
      p: 0.3                     # 增加垂直翻转
    rotation:
      enabled: true
      degrees: [-30, 30]         # 增加旋转角度
      p: 0.7
    
    # 颜色变换 - 更强
    color_jitter:
      enabled: true
      brightness: 0.4            # 增加亮度变化
      contrast: 0.4              # 增加对比度变化
      saturation: 0.4            # 增加饱和度变化
      hue: 0.2                   # 增加色调变化
      p: 0.8
    
    # 高级增强
    gaussian_blur:
      enabled: true
      kernel_size: [3, 7]        # 高斯模糊
      sigma: [0.1, 2.0]
      p: 0.3
    
    random_erasing:
      enabled: true
      p: 0.3                     # 随机擦除
      scale: [0.02, 0.2]
      ratio: [0.3, 3.3]
    
    cutmix:
      enabled: true
      p: 0.3                     # CutMix增强
      alpha: 1.0
    
    mixup:
      enabled: true
      p: 0.2                     # MixUp增强
      alpha: 0.4

# 损失函数配置 - 保持成功的极端平衡策略
loss:
  use_ce: true
  use_dice: true
  use_focal: true
  use_lovasz: true
  weight_ce: 0.1               # 保持低CE权重
  weight_dice: 2.5             # 保持高Dice权重
  weight_focal: 3.0            # 保持高Focal权重
  weight_lovasz: 1.8           # 保持Lovász权重
  focal_gamma: 6.0             # 保持极高gamma值
  focal_alpha: 0.6
  use_class_weights: true
  class_weight_method: "manual"
  # 保持成功的极端类别权重
  manual_weights: [0.01, 1.0, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 100.0, 1.0, 90.0, 1.0, 1.0, 0.01, 1.0, 1.0, 1.0, 1.0, 80.0, 1.0, 1.0, 0.01, 1.0, 70.0, 1.0, 1.0, 60.0, 50.0, 40.0]
  label_smoothing: 0.15
  ignore_index: 255

optimizer:
  type: "adamw"
  beta1: 0.9
  beta2: 0.999
  momentum: 0.9

scheduler:
  type: "cosine_warmup"        # 使用余弦退火调度器
  max_lr: 0.001                # 适中的最大学习率
  warmup_epochs: 10            # 增加预热轮数
  cycles: 3                    # 多周期训练
  pct_start: 0.1
  div_factor: 10.0
  final_div_factor: 100.0

train:
  use_cuda: true
  total_epochs: 600            # 更长的训练时间
  
  # 分层学习率设置
  init_lr: 0.001               # 适中的基础学习率
  encoder_lr: 2e-4             # 编码器学习率
  decoder_lr: 5e-4             # 解码器学习率
  head_lr: 1e-3                # 分割头学习率
  weight_decay: 1e-4           # 权重衰减

  # 渐进式解冻设置 - 更保守的策略
  progressive_unfreezing: true
  freeze_epochs: 0
  unfreeze_schedule:
    0: ["segmentation_head", "decoder"]
    5: ["encoder.layer4"]
    10: ["encoder.layer3"]
    15: ["encoder.layer2"]
    20: ["encoder.layer1"]

  # 批量大小设置 - 考虑更大模型的内存需求
  freeze_batch_size: 8         # 适度减少批量大小
  unfreeze_batch_size: 4       # 适度减少批量大小
  num_workers: 4

  # 其他训练设置
  save_dir: "logs"
  eval_period: 1
  early_stopping: 100          # 更大的耐心值
  mixed_precision: true
  gradient_accumulation: 8     # 增加梯度累积以补偿小批量
  gradient_clip: 1.0           # 更严格的梯度裁剪
  use_channels_last: true
  benchmark_cudnn: true
  verbose: true

# 预训练设置
pretrain:
  enabled: true
  dataset: "ADE20K"            # 使用ADE20K预训练
  weights_path: ""
  finetune_method: "gradual"

# 测试时增强 (TTA)
test_time_augmentation:
  enabled: true
  scales: [0.75, 1.0, 1.25, 1.5]  # 多尺度测试
  flip: true                       # 翻转测试
  rotate: [0, 90, 180, 270]       # 旋转测试

# 模型集成设置
ensemble:
  enabled: false               # 暂时禁用，单独训练
  models: []
  weights: []
  voting_method: "soft"        # 软投票
