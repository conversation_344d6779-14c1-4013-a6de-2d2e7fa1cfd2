"""
分析分割错误案例
"""
import os
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from tqdm import tqdm
import argparse
import yaml
from sklearn.metrics import confusion_matrix
import seaborn as sns

from test_model import load_model, predict_image, get_color_map

def compute_confusion_matrix(preds, gts, num_classes):
    """
    计算混淆矩阵
    
    参数:
    - preds: 预测结果列表
    - gts: 真实标签列表
    - num_classes: 类别数
    
    返回:
    - cm: 混淆矩阵
    """
    # 将所有预测和真实标签展平
    pred_flat = np.concatenate([p.flatten() for p in preds])
    gt_flat = np.concatenate([g.flatten() for g in gts])
    
    # 计算混淆矩阵
    cm = confusion_matrix(gt_flat, pred_flat, labels=range(num_classes))
    
    return cm

def visualize_confusion_matrix(cm, class_names, save_path=None):
    """
    可视化混淆矩阵
    
    参数:
    - cm: 混淆矩阵
    - class_names: 类别名称
    - save_path: 保存路径
    """
    # 归一化混淆矩阵
    cm_norm = cm.astype('float') / (cm.sum(axis=1)[:, np.newaxis] + 1e-6)
    
    plt.figure(figsize=(12, 10))
    sns.heatmap(cm_norm, annot=False, fmt='.2f', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.title('Confusion Matrix')
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()

def find_error_regions(pred, gt, num_classes):
    """
    查找错误区域
    
    参数:
    - pred: 预测结果
    - gt: 真实标签
    - num_classes: 类别数
    
    返回:
    - error_mask: 错误掩码
    - error_classes: 每个类别的错误像素数
    """
    # 创建错误掩码
    error_mask = (pred != gt).astype(np.uint8) * 255
    
    # 统计每个类别的错误
    error_classes = np.zeros(num_classes)
    for c in range(num_classes):
        gt_mask = (gt == c)
        error_classes[c] = np.sum(error_mask[gt_mask] > 0) / (np.sum(gt_mask) + 1e-6)
    
    return error_mask, error_classes

def visualize_errors(img, pred, gt, error_mask, color_map, save_path=None):
    """
    可视化错误区域
    
    参数:
    - img: 原始图像
    - pred: 预测结果
    - gt: 真实标签
    - error_mask: 错误掩码
    - color_map: 颜色映射
    - save_path: 保存路径
    """
    # 创建彩色分割图
    pred_color = np.zeros((pred.shape[0], pred.shape[1], 3), dtype=np.uint8)
    gt_color = np.zeros((gt.shape[0], gt.shape[1], 3), dtype=np.uint8)
    
    for c in range(len(color_map)):
        pred_color[pred == c] = color_map[c]
        gt_color[gt == c] = color_map[c]
    
    # 创建错误掩码的彩色版本
    error_color = np.zeros_like(pred_color)
    error_color[error_mask > 0] = [255, 0, 0]  # 红色表示错误
    
    # 创建叠加图
    alpha = 0.5
    overlay = cv2.addWeighted(img, 1 - alpha, error_color, alpha, 0)
    
    # 显示结果
    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 2, 1)
    plt.imshow(img)
    plt.title('Original Image')
    plt.axis('off')
    
    plt.subplot(2, 2, 2)
    plt.imshow(gt_color)
    plt.title('Ground Truth')
    plt.axis('off')
    
    plt.subplot(2, 2, 3)
    plt.imshow(pred_color)
    plt.title('Prediction')
    plt.axis('off')
    
    plt.subplot(2, 2, 4)
    plt.imshow(overlay)
    plt.title('Error Regions')
    plt.axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()

def analyze_errors(model, test_dir, gt_dir, cfg, device, color_map, output_dir='error_analysis'):
    """
    分析错误案例
    
    参数:
    - model: 模型
    - test_dir: 测试图像目录
    - gt_dir: 真实标签目录
    - cfg: 配置
    - device: 设备
    - color_map: 颜色映射
    - output_dir: 输出目录
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取测试图像列表
    test_images = [f for f in os.listdir(test_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
    
    # 筛选有真实标签的图像
    valid_images = []
    for img_file in test_images:
        gt_path = os.path.join(gt_dir, os.path.splitext(img_file)[0] + '.png')
        if os.path.exists(gt_path):
            valid_images.append(img_file)
    
    if not valid_images:
        print("No ground truth labels found for analysis.")
        return
    
    # 预测所有图像
    preds = []
    gts = []
    error_classes_total = np.zeros(cfg['data']['num_classes'])
    
    for img_file in tqdm(valid_images, desc="Analyzing errors"):
        # 预测
        img_path = os.path.join(test_dir, img_file)
        pred, img = predict_image(model, img_path, cfg, device)
        
        # 读取真实标签
        gt_path = os.path.join(gt_dir, os.path.splitext(img_file)[0] + '.png')
        gt = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
        
        # 确保尺寸一致
        if gt.shape != pred.shape:
            gt = cv2.resize(gt, (pred.shape[1], pred.shape[0]), interpolation=cv2.INTER_NEAREST)
        
        preds.append(pred)
        gts.append(gt)
        
        # 查找错误区域
        error_mask, error_classes = find_error_regions(pred, gt, cfg['data']['num_classes'])
        error_classes_total += error_classes
        
        # 可视化错误
        save_path = os.path.join(output_dir, os.path.splitext(img_file)[0] + '_error.png')
        visualize_errors(img, pred, gt, error_mask, color_map, save_path)
    
    # 计算混淆矩阵
    cm = compute_confusion_matrix(preds, gts, cfg['data']['num_classes'])
    
    # 可视化混淆矩阵
    class_names = [f"Class {i}" for i in range(cfg['data']['num_classes'])]
    cm_path = os.path.join(output_dir, 'confusion_matrix.png')
    visualize_confusion_matrix(cm, class_names, cm_path)
    
    # 分析每个类别的错误率
    error_classes_avg = error_classes_total / len(valid_images)
    
    plt.figure(figsize=(12, 6))
    plt.bar(range(cfg['data']['num_classes']), error_classes_avg)
    plt.xlabel('Class')
    plt.ylabel('Error Rate')
    plt.title('Error Rate by Class')
    plt.savefig(os.path.join(output_dir, 'error_rate_by_class.png'))
    plt.close()
    
    # 找出错误率最高的类别
    top_error_classes = np.argsort(error_classes_avg)[-5:][::-1]
    print(f"Top 5 classes with highest error rates:")
    for c in top_error_classes:
        print(f"Class {c}: {error_classes_avg[c]:.4f}")

def main():
    parser = argparse.ArgumentParser(description="Analyze segmentation errors")
    parser.add_argument('--model', type=str, default='logs/best_model.pth', help='Path to model weights')
    parser.add_argument('--config', type=str, default='config.yaml', help='Path to config file')
    parser.add_argument('--test_dir', type=str, default='VOCdevkit/VOC2025/JPEGImages', help='Test images directory')
    parser.add_argument('--gt_dir', type=str, default='VOCdevkit/VOC2025/SegmentationClass', help='Ground truth directory')
    parser.add_argument('--output_dir', type=str, default='error_analysis', help='Output directory')
    args = parser.parse_args()
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载模型
    model, cfg = load_model(args.model, args.config, device)
    print(f"Model loaded from {args.model}")
    
    # 生成颜色映射
    color_map = get_color_map(cfg['data']['num_classes'])
    
    # 分析错误
    analyze_errors(model, args.test_dir, args.gt_dir, cfg, device, color_map, args.output_dir)

if __name__ == '__main__':
    main()
