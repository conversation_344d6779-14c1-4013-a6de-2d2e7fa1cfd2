import argparse
import os
import datetime
import torch
import torch.backends.cudnn as cudnn
from torch import nn, optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau, LambdaLR

from nets.deeplabv3plus import unet
from nets.unet_training import weights_init
from utils.callbacks import LossHistory, EvalCallback
from utils.dataloader import SegmentationDataset
from utils.losses import CombinedLoss, compute_class_weights
from utils.utils import show_config
from utils.utils_fit import fit_one_epoch


def parse_args():
    parser = argparse.ArgumentParser(description="Semantic Segmentation Training")
    parser.add_argument('--cfg', type=str, default='config.yaml', help='Path to config file')
    return parser.parse_args()


def load_config(path):
    import yaml
    # 以 utf-8 打开并读取为字符串，然后从字符串加载，避免 yaml 内部使用默认编码
    with open(path, encoding='utf-8') as f:
        content = f.read()
    return yaml.safe_load(content)

# ... rest of train.py unchanged follows ...
