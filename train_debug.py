#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版训练脚本
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import segmentation_models_pytorch as smp
import sys

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🚀 使用设备: {device}")
sys.stdout.flush()

def main():
    """主函数"""
    print("🚀 启动调试训练")
    sys.stdout.flush()
    
    try:
        print("📊 步骤1: 导入数据集模块...")
        sys.stdout.flush()
        from enhanced_dataset import create_enhanced_datasets
        
        print("📊 步骤2: 创建数据集...")
        sys.stdout.flush()
        train_dataset, val_dataset = create_enhanced_datasets()
        
        print(f"✅ 数据集创建完成: 训练{len(train_dataset)}, 验证{len(val_dataset)}")
        sys.stdout.flush()
        
        print("📊 步骤3: 创建数据加载器...")
        sys.stdout.flush()
        train_loader = DataLoader(
            train_dataset,
            batch_size=1,  # 最小批次
            shuffle=False,  # 不打乱，便于调试
            num_workers=0
        )
        
        print(f"✅ 数据加载器创建完成: {len(train_loader)} 批次")
        sys.stdout.flush()
        
        print("📊 步骤4: 测试数据加载...")
        sys.stdout.flush()
        
        # 测试加载第一个批次
        for batch_idx, (images, masks) in enumerate(train_loader):
            print(f"✅ 成功加载批次 {batch_idx}: 图像{images.shape}, 标签{masks.shape}")
            sys.stdout.flush()
            
            if batch_idx >= 2:  # 只测试前3个批次
                break
        
        print("📊 步骤5: 创建模型...")
        sys.stdout.flush()
        model = smp.Unet(
            encoder_name='resnet18',
            encoder_weights='imagenet',
            in_channels=3,
            classes=29,
            activation=None,
        ).to(device)
        
        print("✅ 模型创建完成")
        sys.stdout.flush()
        
        print("📊 步骤6: 创建损失函数和优化器...")
        sys.stdout.flush()
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=1e-4)
        
        print("✅ 损失函数和优化器创建完成")
        sys.stdout.flush()
        
        print("📊 步骤7: 开始训练循环...")
        sys.stdout.flush()
        
        model.train()
        
        for batch_idx, (images, masks) in enumerate(train_loader):
            print(f"🔄 处理批次 {batch_idx+1}/{len(train_loader)}")
            sys.stdout.flush()
            
            try:
                images, masks = images.to(device), masks.to(device)
                print(f"  ✅ 数据移动到设备完成")
                sys.stdout.flush()
                
                optimizer.zero_grad()
                print(f"  ✅ 梯度清零完成")
                sys.stdout.flush()
                
                outputs = model(images)
                print(f"  ✅ 前向传播完成: {outputs.shape}")
                sys.stdout.flush()
                
                loss = criterion(outputs, masks)
                print(f"  ✅ 损失计算完成: {loss.item():.4f}")
                sys.stdout.flush()
                
                loss.backward()
                print(f"  ✅ 反向传播完成")
                sys.stdout.flush()
                
                optimizer.step()
                print(f"  ✅ 参数更新完成")
                sys.stdout.flush()
                
                # 清理显存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                
                print(f"✅ 批次 {batch_idx+1} 完成，损失: {loss.item():.4f}")
                sys.stdout.flush()
                
                if batch_idx >= 4:  # 只训练5个批次
                    break
                    
            except Exception as e:
                print(f"❌ 批次 {batch_idx} 出错: {e}")
                sys.stdout.flush()
                import traceback
                traceback.print_exc()
                break
        
        print("🎉 调试训练完成!")
        sys.stdout.flush()
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        sys.stdout.flush()
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
