#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终增强训练脚本
目标：mIoU > 0.6
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import segmentation_models_pytorch as smp
import numpy as np
import time
from datetime import datetime
import os

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🚀 使用设备: {device}")

# 清理显存
if torch.cuda.is_available():
    torch.cuda.empty_cache()

class FinalConfig:
    """最终配置"""
    BACKBONE = 'efficientnet-b4'  # 平衡性能和速度
    EPOCHS = 200  # 充分训练
    BATCH_SIZE = 4  # 适合显存
    LEARNING_RATE = 1e-4
    NUM_CLASSES = 29
    SAVE_INTERVAL = 20  # 每20轮保存一次

def calculate_miou(predictions, targets, num_classes=29):
    """计算mIoU"""
    ious = []
    
    for cls in range(num_classes):
        pred_mask = (predictions == cls)
        true_mask = (targets == cls)
        
        intersection = (pred_mask & true_mask).sum().float()
        union = (pred_mask | true_mask).sum().float()
        
        if union > 0:
            iou = intersection / union
            ious.append(iou.item())
    
    return np.mean(ious) if ious else 0.0

def train_epoch(model, train_loader, criterion, optimizer, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    num_batches = 0
    
    print(f"\n🔄 Epoch {epoch+1}/{FinalConfig.EPOCHS}")
    
    for batch_idx, (images, masks) in enumerate(train_loader):
        try:
            images, masks = images.to(device), masks.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, masks)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 20 == 0:
                print(f"  批次 {batch_idx+1}/{len(train_loader)}, 损失: {loss.item():.4f}")
            
            # 定期清理显存
            if batch_idx % 10 == 0 and torch.cuda.is_available():
                torch.cuda.empty_cache()
                
        except Exception as e:
            print(f"❌ 训练批次 {batch_idx} 出错: {e}")
            continue
    
    avg_loss = total_loss / max(num_batches, 1)
    print(f"✅ 训练完成, 平均损失: {avg_loss:.4f}")
    
    return avg_loss

def validate_epoch(model, val_loader, criterion, epoch):
    """验证一个epoch"""
    model.eval()
    total_loss = 0
    total_miou = 0
    num_batches = 0
    
    with torch.no_grad():
        for batch_idx, (images, masks) in enumerate(val_loader):
            try:
                images, masks = images.to(device), masks.to(device)
                
                outputs = model(images)
                loss = criterion(outputs, masks)
                
                # 计算mIoU
                predictions = torch.argmax(outputs, dim=1)
                miou = calculate_miou(predictions, masks)
                
                total_loss += loss.item()
                total_miou += miou
                num_batches += 1
                
                # 清理显存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    
            except Exception as e:
                print(f"❌ 验证批次 {batch_idx} 出错: {e}")
                continue
    
    avg_loss = total_loss / max(num_batches, 1)
    avg_miou = total_miou / max(num_batches, 1)
    
    print(f"📊 验证结果 - 损失: {avg_loss:.4f}, mIoU: {avg_miou:.4f}")
    
    return avg_loss, avg_miou

def main():
    """主函数"""
    print("🚀 启动最终增强训练")
    print("=" * 50)
    print(f"📋 配置:")
    print(f"  模型: UNet + {FinalConfig.BACKBONE}")
    print(f"  轮数: {FinalConfig.EPOCHS}")
    print(f"  批次: {FinalConfig.BATCH_SIZE}")
    print(f"  学习率: {FinalConfig.LEARNING_RATE}")
    print(f"🎯 目标: mIoU > 0.6 (当前最佳: 0.35)")
    
    try:
        # 加载数据
        print("\n📊 加载数据...")
        from enhanced_dataset import create_enhanced_datasets
        
        train_dataset, val_dataset = create_enhanced_datasets()
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=FinalConfig.BATCH_SIZE,
            shuffle=True,
            num_workers=0
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=FinalConfig.BATCH_SIZE,
            shuffle=False,
            num_workers=0
        )
        
        print(f"✅ 训练集: {len(train_loader)} 批次")
        print(f"✅ 验证集: {len(val_loader)} 批次")
        
        # 创建模型
        print(f"\n🧠 创建模型: UNet + {FinalConfig.BACKBONE}")
        model = smp.UnetPlusPlus(  # 使用UNet++
            encoder_name=FinalConfig.BACKBONE,
            encoder_weights='imagenet',
            in_channels=3,
            classes=FinalConfig.NUM_CLASSES,
            activation=None,
        ).to(device)
        
        # 创建损失函数和优化器
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.AdamW(model.parameters(), lr=FinalConfig.LEARNING_RATE, weight_decay=1e-5)
        
        # 学习率调度器
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=FinalConfig.EPOCHS, eta_min=1e-7)
        
        # 训练循环
        best_miou = 0
        start_time = time.time()
        
        print(f"\n🎯 开始训练 {FinalConfig.EPOCHS} 轮...")
        
        for epoch in range(FinalConfig.EPOCHS):
            # 训练
            train_loss = train_epoch(model, train_loader, criterion, optimizer, epoch)
            
            # 验证
            val_loss, val_miou = validate_epoch(model, val_loader, criterion, epoch)
            
            # 更新学习率
            scheduler.step()
            current_lr = scheduler.get_last_lr()[0]
            
            # 保存最佳模型
            if val_miou > best_miou:
                best_miou = val_miou
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'best_miou': best_miou,
                    'train_loss': train_loss,
                    'val_loss': val_loss,
                }, f'best_enhanced_model_miou_{val_miou:.4f}.pth')
                
                print(f"🎉 新的最佳mIoU: {best_miou:.4f}")
                
                # 检查是否达到目标
                if best_miou > 0.6:
                    print("🎯🎯🎯 恭喜！达到目标 mIoU > 0.6！🎯🎯🎯")
                elif best_miou > 0.5:
                    print("🎯 很好！mIoU > 0.5，继续努力！")
                elif best_miou > 0.4:
                    print("👍 不错！mIoU > 0.4，有显著改进！")
            
            # 定期保存
            if (epoch + 1) % FinalConfig.SAVE_INTERVAL == 0:
                torch.save(model.state_dict(), f'enhanced_model_epoch_{epoch+1}.pth')
                elapsed = time.time() - start_time
                print(f"💾 检查点已保存, 已训练 {elapsed/60:.1f} 分钟")
            
            print(f"📈 Epoch {epoch+1} 总结:")
            print(f"  训练损失: {train_loss:.4f}")
            print(f"  验证损失: {val_loss:.4f}")
            print(f"  验证mIoU: {val_miou:.4f}")
            print(f"  最佳mIoU: {best_miou:.4f}")
            print(f"  学习率: {current_lr:.2e}")
        
        total_time = time.time() - start_time
        
        print(f"\n🎊 训练完成!")
        print(f"📊 最终结果:")
        print(f"  最佳mIoU: {best_miou:.4f}")
        print(f"  总耗时: {total_time/3600:.1f} 小时")
        print(f"  改进幅度: {(best_miou/0.35-1)*100:.1f}% (相比之前的0.35)")
        
        if best_miou > 0.6:
            print("🏆 任务完成！成功达到目标 mIoU > 0.6！")
        elif best_miou > 0.5:
            print("🥈 接近目标！mIoU > 0.5，再优化一下就能达到0.6！")
        elif best_miou > 0.4:
            print("🥉 有进步！mIoU > 0.4，比之前的0.35有显著提升！")
        else:
            print("💪 需要进一步优化，但已经验证了训练流程！")
            
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
