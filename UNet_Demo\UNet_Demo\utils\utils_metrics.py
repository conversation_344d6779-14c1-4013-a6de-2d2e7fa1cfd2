import torch
import torch.nn.functional as F

def f_score(inputs, targets, beta=1.0, ignore_index=None, eps=1e-6):
    """
    计算多类别 F-score, 支持 ignore_index.
    inputs: (B,C,H,W) logits or probs
    targets: (B,H,W) long
    """
    if inputs.dim() == 4:
        probs = F.softmax(inputs, dim=1)
    else:
        probs = inputs
    B, C, H, W = probs.shape

    # one-hot encode predictions & ground truth
    pred_labels    = torch.argmax(probs, dim=1)
    pred_one_hot   = F.one_hot(pred_labels, num_classes=C).permute(0,3,1,2).float()
    true_one_hot   = F.one_hot(targets,    num_classes=C).permute(0,3,1,2).float()

    # 忽略指定类别
    if ignore_index is not None and 0 <= ignore_index < C:
        valid_mask = (targets != ignore_index).unsqueeze(1).float()
        pred_one_hot = pred_one_hot * valid_mask
        true_one_hot = true_one_hot * valid_mask

    # 计算 TP, FP, FN
    tp = torch.sum(pred_one_hot * true_one_hot, dim=(0,2,3))
    fp = torch.sum(pred_one_hot * (1 - true_one_hot), dim=(0,2,3))
    fn = torch.sum((1 - pred_one_hot) * true_one_hot, dim=(0,2,3))

    precision = tp / (tp + fp + eps)
    recall    = tp / (tp + fn + eps)
    score     = (1 + beta**2) * precision * recall / (beta**2 * precision + recall + eps)

    # 平均多个类的分数
    return score.mean().item()

def compute_mIoU_tensor(preds, targets, num_classes, ignore_index=None, eps=1e-6, return_per_class=False):
    """
    基于 Tensor 的 mIoU 计算，不落盘。
    preds: (B,H,W) LongTensor, 预测标签
    targets: (B,H,W) LongTensor, 真实标签
    return_per_class: 是否返回每个类别的IoU
    """
    # 扁平化
    preds = preds.view(-1)
    targets = targets.view(-1)
    mask = torch.ones_like(targets, dtype=torch.bool)
    if ignore_index is not None:
        mask = mask & (targets != ignore_index)
    preds = preds[mask]
    targets = targets[mask]

    # 检查有效像素数量
    if len(targets) == 0:
        print("警告: 没有有效的像素用于计算mIoU")
        if return_per_class:
            return 0.0, [0.0] * num_classes
        return 0.0

    ious = []
    per_class_ious = [0.0] * num_classes  # 存储每个类别的IoU
    valid_classes = 0

    # 计算每个类别的IoU
    for cls in range(num_classes):
        # 排除 ignore_index
        if cls == ignore_index:
            continue

        # 检查该类别是否存在于真实标签中
        if not (targets == cls).any():
            # 如果类别不存在于真实标签中，跳过该类别
            per_class_ious[cls] = 0.0  # 该类别的IoU为0
            continue

        pred_i = preds == cls
        target_i = targets == cls
        inter = (pred_i & target_i).sum().float()
        union = (pred_i | target_i).sum().float()

        # 只有当union > 0时才计算IoU
        if union > eps:
            iou = inter / union
            ious.append(iou)
            per_class_ious[cls] = iou.item()  # 存储该类别的IoU
            valid_classes += 1
        else:
            per_class_ious[cls] = 0.0  # 该类别的IoU为0

    # 如果没有有效类别，返回0
    if valid_classes == 0:
        print("警告: 没有有效的类别用于计算mIoU")
        if return_per_class:
            return 0.0, per_class_ious
        return 0.0

    # 计算平均IoU
    mean_iou = torch.stack(ious).mean().item()

    if return_per_class:
        return mean_iou, per_class_ious
    return mean_iou