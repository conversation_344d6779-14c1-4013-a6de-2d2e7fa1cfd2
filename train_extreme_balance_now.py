#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from datetime import datetime
import logging

# 添加UNet_Demo路径
sys.path.append('UNet_Demo/UNet_Demo')

try:
    from train import unet
    from utils.dataloader import SegmentationDataset
    from utils.utils_metrics import f_score
    from nets.unet_training import CE_Loss, Dice_loss, Focal_Loss
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在正确的目录下运行此脚本")
    sys.exit(1)

def setup_logging():
    """设置日志"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f'logs/extreme_balance_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'{log_dir}/training.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    return log_dir, logging.getLogger(__name__)

class ExtremeBalanceLoss:
    """极端平衡损失函数"""

    def __init__(self):
        # 极端类别权重 - 基于您的成功经验
        class_weights = np.ones(29)

        # 完全未学习的类别 - 极高权重 (50-100倍)
        extreme_weights = {
            8: 100.0, 10: 90.0, 18: 80.0, 23: 70.0,
            26: 60.0, 27: 50.0, 28: 50.0
        }

        # 中等表现类别 - 中等权重 (5-20倍)
        medium_weights = {
            5: 15.0, 7: 12.0, 9: 10.0, 11: 8.0, 15: 6.0,
            17: 5.0, 19: 20.0, 22: 15.0, 25: 10.0
        }

        # 优秀表现类别 - 极低权重 (0.01-0.1倍)
        low_weights = {
            0: 0.01, 3: 0.01, 13: 0.01, 2: 0.05, 21: 0.05
        }

        # 其他类别 - 低权重 (0.3-0.8倍)
        other_weights = {
            1: 0.5, 4: 0.8, 6: 0.6, 12: 0.4, 14: 0.3,
            16: 0.7, 20: 0.6, 24: 0.5
        }

        # 应用权重
        for cls, weight in {**extreme_weights, **medium_weights, **low_weights, **other_weights}.items():
            class_weights[cls] = weight

        self.class_weights = torch.FloatTensor(class_weights)
        if torch.cuda.is_available():
            self.class_weights = self.class_weights.cuda()

        # 创建损失函数 - 这些是函数，不是类
        self.ce_loss = lambda outputs, targets: CE_Loss(outputs, targets, self.class_weights, 29)
        self.focal_loss = lambda outputs, targets: Focal_Loss(outputs, targets, self.class_weights, 29, alpha=0.25, gamma=2.0)
        self.dice_loss = lambda outputs, targets: Dice_loss(outputs, targets)

        print("📊 极端类别权重分布:")
        for i, weight in enumerate(class_weights):
            if weight != 1.0:
                print(f"  类别 {i}: {weight:.2f}x")

    def __call__(self, outputs, targets):
        # 组合损失: 40% Focal + 30% Dice + 30% CE
        focal = self.focal_loss(outputs, targets)
        dice = self.dice_loss(outputs, targets)
        ce = self.ce_loss(outputs, targets)

        total_loss = 0.4 * focal + 0.3 * dice + 0.3 * ce
        return total_loss

def create_model():
    """创建模型"""
    print("🧠 创建增强模型...")

    model = unet(
        num_classes=29,
        backbone='resnet50',
        pretrained=True,
        dropout_rate=0.3,
        use_attention=True,
        attention_type='cbam'
    )

    return model

def create_data_loaders():
    """创建数据加载器"""
    print("📊 创建数据加载器...")

    VOCdevkit_path = "UNet_Demo/UNet_Demo/VOCdevkit"

    # 读取数据集
    with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/train.txt"), "r") as f:
        train_lines = f.readlines()
    with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/val.txt"), "r") as f:
        val_lines = f.readlines()

    print(f"训练集样本数: {len(train_lines)}")
    print(f"验证集样本数: {len(val_lines)}")

    # 创建数据集
    train_dataset = SegmentationDataset(
        train_lines,
        root_dir=VOCdevkit_path,
        img_size=(512, 512),
        num_classes=29,
        train=True
    )

    val_dataset = SegmentationDataset(
        val_lines,
        root_dir=VOCdevkit_path,
        img_size=(512, 512),
        num_classes=29,
        train=False
    )

    # 创建数据加载器 - 减少worker数量避免多进程问题
    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=4,  # 减少batch size
        shuffle=True,
        num_workers=0,  # 使用单进程避免多进程问题
        pin_memory=True,
        drop_last=True
    )

    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=4,  # 减少batch size
        shuffle=False,
        num_workers=0,  # 使用单进程避免多进程问题
        pin_memory=True,
        drop_last=False
    )

    return train_loader, val_loader

def main():
    """主训练函数"""
    print("🚀 启动极端权重平衡训练")
    print("=" * 60)
    print("🎯 核心策略:")
    print("  ✅ 极端类别权重平衡 (50-100倍)")
    print("  ✅ Focal Loss + Dice Loss + CE Loss组合")
    print("  ✅ ResNet50 + 注意力机制")
    print("  ✅ 更高学习率 + 余弦退火")
    print("=" * 60)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 设置日志
    log_dir, logger = setup_logging()

    # 创建模型
    model = create_model()
    model = model.to(device)

    # 创建损失函数
    criterion = ExtremeBalanceLoss()

    # 创建优化器 - 更高的学习率
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=3e-4,  # 比之前更高的学习率
        weight_decay=1e-4
    )

    # 余弦退火学习率调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=200, eta_min=1e-6
    )

    # 创建数据加载器
    train_loader, val_loader = create_data_loaders()

    # 训练参数
    epochs = 200  # 足够的训练轮数
    best_miou = 0
    patience = 30
    patience_counter = 0

    print(f"\n🎯 开始训练 {epochs} 轮...")
    logger.info(f"开始极端权重平衡训练，目标: mIoU > 0.45")

    # 训练循环
    for epoch in range(epochs):
        print(f"\nEpoch {epoch+1}/{epochs}")
        print("-" * 40)

        # 训练阶段
        model.train()
        total_loss = 0

        for iteration, batch in enumerate(train_loader):
            if len(batch) == 2:
                images, targets = batch
            else:
                images, targets = batch[0], batch[1]

            images = images.to(device)
            targets = targets.to(device)

            optimizer.zero_grad()

            outputs = model(images)
            loss = criterion(outputs, targets)

            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            optimizer.step()

            total_loss += loss.item()

            if iteration % 30 == 0:
                print(f"  Batch {iteration}, Loss: {loss.item():.4f}")

        # 更新学习率
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']

        avg_train_loss = total_loss / len(train_loader)

        # 验证阶段
        model.eval()
        val_loss = 0
        val_miou = 0

        with torch.no_grad():
            for batch in val_loader:
                if len(batch) == 2:
                    images, targets = batch
                else:
                    images, targets = batch[0], batch[1]

                images = images.to(device)
                targets = targets.to(device)

                outputs = model(images)
                loss = criterion(outputs, targets)

                val_loss += loss.item()

                # 计算mIoU
                pred = torch.argmax(outputs, dim=1)
                miou = f_score(pred, targets, beta=1, num_classes=29, type='miou')
                val_miou += miou

        avg_val_loss = val_loss / len(val_loader)
        avg_val_miou = val_miou / len(val_loader)

        # 记录结果
        print(f"训练损失: {avg_train_loss:.4f}")
        print(f"验证损失: {avg_val_loss:.4f}")
        print(f"验证mIoU: {avg_val_miou:.4f}")
        print(f"学习率: {current_lr:.2e}")

        logger.info(f"Epoch {epoch+1}: train_loss={avg_train_loss:.4f}, val_loss={avg_val_loss:.4f}, val_miou={avg_val_miou:.4f}, lr={current_lr:.2e}")

        # 保存最佳模型
        if avg_val_miou > best_miou:
            best_miou = avg_val_miou
            patience_counter = 0

            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_miou': best_miou,
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss,
            }, f'best_extreme_balance_miou_{avg_val_miou:.4f}.pth')

            print(f"🎉 新的最佳mIoU: {best_miou:.4f}")
            logger.info(f"🎉 新的最佳mIoU: {best_miou:.4f}")

            # 检查是否达到目标
            if best_miou > 0.45:
                print("🎯🎯🎯 恭喜！达到目标 mIoU > 0.45！🎯🎯🎯")
                logger.info("🎯 达到目标 mIoU > 0.45")
            elif best_miou > 0.40:
                print("🎯 很好！mIoU > 0.40，继续努力！")
        else:
            patience_counter += 1

        # 早停检查
        if patience_counter >= patience:
            print(f"⏹️ 早停触发，最佳mIoU: {best_miou:.4f}")
            logger.info(f"⏹️ 早停触发，最佳mIoU: {best_miou:.4f}")
            break

        # 定期保存
        if (epoch + 1) % 20 == 0:
            torch.save(model.state_dict(), f'extreme_balance_epoch_{epoch+1}.pth')

    print(f"\n🎊 训练完成！")
    print(f"📊 最佳mIoU: {best_miou:.4f}")
    print(f"📁 日志保存在: {log_dir}")

    # 计算改善程度
    baseline_miou = 0.34  # 之前的最佳结果
    improvement = ((best_miou - baseline_miou) / baseline_miou) * 100

    print(f"📈 相比基线改善: {improvement:.1f}%")
    logger.info(f"📈 相比基线改善: {improvement:.1f}%")

    if best_miou > 0.45:
        print("🏆🏆🏆 恭喜！成功达到目标！🏆🏆🏆")
    elif best_miou > 0.40:
        print("🎯 接近目标，继续优化！")
    else:
        print("💪 有改善，需要进一步优化策略")

if __name__ == "__main__":
    main()
