#!/usr/bin/env python3
"""
立即改进训练监控脚本
实时监控训练进度，特别关注mIoU提升和类别平衡情况
"""

import os
import time
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import argparse

def read_miou_file(file_path):
    """读取mIoU文件"""
    if not os.path.exists(file_path):
        return []
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    mious = []
    for line in lines:
        line = line.strip()
        if line:
            try:
                miou = float(line)
                mious.append(miou)
            except ValueError:
                continue
    
    return mious

def read_loss_file(file_path):
    """读取损失文件"""
    if not os.path.exists(file_path):
        return [], []
    
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    train_losses = []
    val_losses = []
    
    for line in lines:
        line = line.strip()
        if line and ',' in line:
            try:
                parts = line.split(',')
                if len(parts) >= 2:
                    train_loss = float(parts[0])
                    val_loss = float(parts[1])
                    train_losses.append(train_loss)
                    val_losses.append(val_loss)
            except ValueError:
                continue
    
    return train_losses, val_losses

def plot_training_progress(log_dir):
    """绘制训练进度"""
    # 查找最新的日志目录
    if not os.path.exists(log_dir):
        print(f"日志目录不存在: {log_dir}")
        return
    
    # 读取mIoU数据
    miou_file = os.path.join(log_dir, "epoch_miou.txt")
    mious = read_miou_file(miou_file)
    
    # 读取损失数据
    loss_file = os.path.join(log_dir, "epoch_loss.txt")
    train_losses, val_losses = read_loss_file(loss_file)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('立即改进训练监控 - 实时进度', fontsize=16)
    
    # mIoU趋势
    if mious:
        epochs = list(range(1, len(mious) + 1))
        axes[0, 0].plot(epochs, mious, 'b-', linewidth=2, label='mIoU')
        axes[0, 0].axhline(y=0.4, color='r', linestyle='--', label='目标 (0.4)')
        axes[0, 0].axhline(y=0.36, color='orange', linestyle='--', label='当前基线 (0.36)')
        axes[0, 0].set_title(f'mIoU趋势 (当前: {mious[-1]:.4f})')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('mIoU')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 计算改进率
        if len(mious) > 1:
            improvement = mious[-1] - mious[0]
            improvement_rate = improvement / len(mious) * 100
            axes[0, 0].text(0.02, 0.98, f'改进率: {improvement_rate:.4f}%/epoch', 
                          transform=axes[0, 0].transAxes, verticalalignment='top',
                          bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
    else:
        axes[0, 0].text(0.5, 0.5, '等待mIoU数据...', ha='center', va='center', 
                       transform=axes[0, 0].transAxes)
        axes[0, 0].set_title('mIoU趋势')
    
    # 损失趋势
    if train_losses and val_losses:
        epochs = list(range(1, len(train_losses) + 1))
        axes[0, 1].plot(epochs, train_losses, 'g-', linewidth=2, label='训练损失')
        axes[0, 1].plot(epochs, val_losses, 'r-', linewidth=2, label='验证损失')
        axes[0, 1].set_title('损失趋势')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 显示当前损失值
        current_train_loss = train_losses[-1]
        current_val_loss = val_losses[-1]
        axes[0, 1].text(0.02, 0.98, f'当前训练损失: {current_train_loss:.4f}\n当前验证损失: {current_val_loss:.4f}', 
                       transform=axes[0, 1].transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.5))
    else:
        axes[0, 1].text(0.5, 0.5, '等待损失数据...', ha='center', va='center', 
                       transform=axes[0, 1].transAxes)
        axes[0, 1].set_title('损失趋势')
    
    # mIoU改进分析
    if len(mious) > 10:
        # 计算最近10个epoch的改进趋势
        recent_mious = mious[-10:]
        recent_epochs = list(range(len(mious)-9, len(mious)+1))
        
        # 线性拟合
        z = np.polyfit(range(len(recent_mious)), recent_mious, 1)
        p = np.poly1d(z)
        
        axes[1, 0].plot(recent_epochs, recent_mious, 'bo-', label='最近10个epoch')
        axes[1, 0].plot(recent_epochs, p(range(len(recent_mious))), 'r--', label=f'趋势线 (斜率: {z[0]:.6f})')
        axes[1, 0].set_title('最近改进趋势')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('mIoU')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 预测何时达到0.4
        if z[0] > 0:
            epochs_to_target = (0.4 - mious[-1]) / z[0]
            if epochs_to_target > 0:
                axes[1, 0].text(0.02, 0.02, f'预计{epochs_to_target:.1f}个epoch后达到0.4', 
                               transform=axes[1, 0].transAxes, verticalalignment='bottom',
                               bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.5))
    else:
        axes[1, 0].text(0.5, 0.5, '需要更多数据进行趋势分析...', ha='center', va='center', 
                       transform=axes[1, 0].transAxes)
        axes[1, 0].set_title('改进趋势分析')
    
    # 训练统计
    stats_text = "训练统计信息:\n"
    if mious:
        stats_text += f"• 当前mIoU: {mious[-1]:.4f}\n"
        stats_text += f"• 最佳mIoU: {max(mious):.4f}\n"
        stats_text += f"• 训练轮数: {len(mious)}\n"
        
        if len(mious) > 1:
            total_improvement = mious[-1] - mious[0]
            stats_text += f"• 总改进: {total_improvement:.4f}\n"
            
            # 距离目标的差距
            gap_to_target = 0.4 - mious[-1]
            stats_text += f"• 距离目标0.4: {gap_to_target:.4f}\n"
            
            # 改进百分比
            improvement_percent = (total_improvement / mious[0]) * 100 if mious[0] > 0 else 0
            stats_text += f"• 改进百分比: {improvement_percent:.2f}%\n"
    
    if train_losses:
        stats_text += f"• 当前训练损失: {train_losses[-1]:.4f}\n"
    if val_losses:
        stats_text += f"• 当前验证损失: {val_losses[-1]:.4f}\n"
    
    axes[1, 1].text(0.05, 0.95, stats_text, transform=axes[1, 1].transAxes, 
                   verticalalignment='top', fontsize=10,
                   bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    axes[1, 1].set_xlim(0, 1)
    axes[1, 1].set_ylim(0, 1)
    axes[1, 1].axis('off')
    axes[1, 1].set_title('训练统计')
    
    plt.tight_layout()
    return fig

def monitor_training(log_dir, refresh_interval=30):
    """监控训练进度"""
    print(f"开始监控训练进度...")
    print(f"日志目录: {log_dir}")
    print(f"刷新间隔: {refresh_interval}秒")
    print("按Ctrl+C停止监控")
    
    plt.ion()  # 开启交互模式
    
    try:
        while True:
            # 清除之前的图表
            plt.clf()
            
            # 绘制新的图表
            fig = plot_training_progress(log_dir)
            
            if fig:
                plt.draw()
                plt.pause(0.1)
            
            # 等待刷新间隔
            time.sleep(refresh_interval)
            
    except KeyboardInterrupt:
        print("\n监控已停止")
    finally:
        plt.ioff()  # 关闭交互模式

def main():
    parser = argparse.ArgumentParser(description='监控立即改进训练进度')
    parser.add_argument('--log_dir', default='logs/immediate_improvements', 
                       help='日志目录路径')
    parser.add_argument('--refresh', type=int, default=30, 
                       help='刷新间隔（秒）')
    parser.add_argument('--once', action='store_true', 
                       help='只生成一次图表，不持续监控')
    
    args = parser.parse_args()
    
    if args.once:
        # 只生成一次图表
        fig = plot_training_progress(args.log_dir)
        if fig:
            plt.show()
    else:
        # 持续监控
        monitor_training(args.log_dir, args.refresh)

if __name__ == "__main__":
    main()
