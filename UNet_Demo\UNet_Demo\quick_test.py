#!/usr/bin/env python3
"""
快速测试脚本
用于验证模型和环境配置是否正确
"""

import os
import sys
import torch
import numpy as np
from PIL import Image
import time
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_environment():
    """测试环境配置"""
    print("=" * 50)
    print("环境测试")
    print("=" * 50)

    # Python版本
    print(f"Python版本: {sys.version}")

    # PyTorch版本
    print(f"PyTorch版本: {torch.__version__}")

    # CUDA可用性
    cuda_available = torch.cuda.is_available()
    print(f"CUDA可用: {cuda_available}")

    if cuda_available:
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")

    # 内存信息
    if cuda_available:
        torch.cuda.empty_cache()
        allocated = torch.cuda.memory_allocated() / 1024**3
        cached = torch.cuda.memory_reserved() / 1024**3
        print(f"GPU内存 - 已分配: {allocated:.2f}GB, 已缓存: {cached:.2f}GB")

    return cuda_available

def test_model_loading():
    """测试模型加载"""
    print("\n" + "=" * 50)
    print("模型加载测试")
    print("=" * 50)

    try:
        from nets.deeplabv3plus import unet

        # 创建模型
        model = unet(
            num_classes=29,
            backbone='resnet50',
            pretrained=False,  # 快速测试不下载预训练权重
            dropout_rate=0.2,
            use_attention=True,
            attention_type='cbam'
        )

        print("✓ 模型创建成功")

        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

        print(f"总参数数: {total_params:,}")
        print(f"可训练参数数: {trainable_params:,}")

        return model

    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return None

def test_model_inference(model, device):
    """测试模型推理"""
    print("\n" + "=" * 50)
    print("模型推理测试")
    print("=" * 50)

    if model is None:
        print("✗ 模型未加载，跳过推理测试")
        return False

    try:
        model = model.to(device)
        model.eval()

        # 创建随机输入
        batch_size = 2
        input_tensor = torch.randn(batch_size, 3, 512, 512).to(device)

        print(f"输入张量形状: {input_tensor.shape}")

        # 推理
        start_time = time.time()
        with torch.no_grad():
            output = model(input_tensor)
        inference_time = time.time() - start_time

        print(f"输出张量形状: {output[0].shape}")
        print(f"推理时间: {inference_time:.3f}秒")
        print(f"每张图像推理时间: {inference_time/batch_size:.3f}秒")

        # 检查输出
        if isinstance(output, (list, tuple)):
            output_tensor = output[0]
        else:
            output_tensor = output

        if len(output_tensor.shape) == 3 and output_tensor.shape[0] == 29:
            print("✓ 模型推理成功")
            return True
        elif len(output_tensor.shape) == 4 and output_tensor.shape[1] == 29:
            print("✓ 模型推理成功")
            return True
        else:
            print(f"✗ 输出形状不正确: {output_tensor.shape}")
            return False

    except Exception as e:
        print(f"✗ 模型推理失败: {e}")
        return False

def test_data_loading():
    """测试数据加载"""
    print("\n" + "=" * 50)
    print("数据加载测试")
    print("=" * 50)

    # 检查数据目录
    data_dirs = [
        "VOCdevkit/VOC2025/JPEGImages",
        "VOCdevkit/VOC2025/SegmentationClass",
        "VOCdevkit/VOC2025/ImageSets/Segmentation"
    ]

    all_exist = True
    for data_dir in data_dirs:
        if os.path.exists(data_dir):
            file_count = len([f for f in os.listdir(data_dir) if not f.startswith('.')])
            print(f"✓ {data_dir}: {file_count} 个文件")
        else:
            print(f"✗ {data_dir}: 目录不存在")
            all_exist = False

    # 检查标注文件
    annotation_files = [
        "VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt",
        "VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt"
    ]

    for ann_file in annotation_files:
        if os.path.exists(ann_file):
            with open(ann_file, 'r') as f:
                lines = f.readlines()
            print(f"✓ {ann_file}: {len(lines)} 个样本")
        else:
            print(f"✗ {ann_file}: 文件不存在")
            all_exist = False

    if all_exist:
        print("✓ 数据结构检查通过")
    else:
        print("✗ 数据结构检查失败")

    return all_exist

def test_loss_functions():
    """测试损失函数"""
    print("\n" + "=" * 50)
    print("损失函数测试")
    print("=" * 50)

    try:
        from utils.losses import CombinedLoss

        # 创建损失函数
        loss_config = {
            'use_ce': True,
            'use_dice': True,
            'use_focal': True,
            'use_lovasz': True,
            'weight_ce': 0.6,
            'weight_dice': 1.2,
            'weight_focal': 0.8,
            'weight_lovasz': 1.8,
            'focal_alpha': 0.6,
            'focal_gamma': 3.0,
            'label_smoothing': 0.1
        }

        loss_fn = CombinedLoss(loss_config)
        print("✓ 损失函数创建成功")

        # 测试损失计算
        batch_size, num_classes, height, width = 2, 29, 512, 512

        # 模拟预测和标签
        predictions = torch.randn(batch_size, num_classes, height, width)
        targets = torch.randint(0, num_classes, (batch_size, height, width))

        loss = loss_fn(predictions, targets)
        print(f"✓ 损失计算成功: {loss.item():.4f}")

        return True

    except Exception as e:
        print(f"✗ 损失函数测试失败: {e}")
        return False

def test_memory_usage(device):
    """测试内存使用"""
    print("\n" + "=" * 50)
    print("内存使用测试")
    print("=" * 50)

    if device.type != 'cuda':
        print("CPU模式，跳过GPU内存测试")
        return True

    try:
        # 清空缓存
        torch.cuda.empty_cache()

        # 记录初始内存
        initial_memory = torch.cuda.memory_allocated() / 1024**3
        print(f"初始GPU内存: {initial_memory:.2f}GB")

        # 创建大张量测试内存
        test_tensors = []
        for i in range(5):
            tensor = torch.randn(4, 3, 512, 512).cuda()
            test_tensors.append(tensor)

            current_memory = torch.cuda.memory_allocated() / 1024**3
            print(f"创建张量 {i+1}: {current_memory:.2f}GB")

        # 清理
        del test_tensors
        torch.cuda.empty_cache()

        final_memory = torch.cuda.memory_allocated() / 1024**3
        print(f"清理后GPU内存: {final_memory:.2f}GB")

        print("✓ 内存测试完成")
        return True

    except Exception as e:
        print(f"✗ 内存测试失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='快速测试脚本')
    parser.add_argument('--skip_model', action='store_true', help='跳过模型测试')
    parser.add_argument('--skip_data', action='store_true', help='跳过数据测试')
    parser.add_argument('--device', type=str, default='auto', help='设备类型')
    args = parser.parse_args()

    print("U-net快速测试脚本")
    print("用于验证环境和配置是否正确")

    # 测试环境
    cuda_available = test_environment()

    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if cuda_available else 'cpu')
    else:
        device = torch.device(args.device)

    print(f"\n使用设备: {device}")

    # 测试结果
    test_results = {}

    # 模型测试
    if not args.skip_model:
        model = test_model_loading()
        test_results['model_loading'] = model is not None

        if model:
            test_results['model_inference'] = test_model_inference(model, device)
            test_results['memory_usage'] = test_memory_usage(device)
        else:
            test_results['model_inference'] = False
            test_results['memory_usage'] = False

    # 数据测试
    if not args.skip_data:
        test_results['data_loading'] = test_data_loading()

    # 损失函数测试
    test_results['loss_functions'] = test_loss_functions()

    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)

    passed_tests = sum(test_results.values())
    total_tests = len(test_results)

    for test_name, result in test_results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")

    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")

    if passed_tests == total_tests:
        print("🎉 所有测试通过！环境配置正确，可以开始训练。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置。")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
