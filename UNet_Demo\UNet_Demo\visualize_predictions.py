"""
可视化模型预测结果
从改进1(1).py借鉴的可视化功能
"""
import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import albumentations as A
from albumentations.pytorch import ToTensorV2
import argparse
import yaml
import cv2
from tqdm import tqdm
import random

from nets.deeplabv3plus import unet
from utils.utils_metrics import compute_mIoU_tensor

# 颜色映射，用于可视化
def get_color_map(num_classes=29):
    """
    生成颜色映射，用于可视化分割结果
    """
    color_map = np.zeros((num_classes, 3), dtype=np.uint8)
    for i in range(num_classes):
        r = np.random.randint(0, 256)
        g = np.random.randint(0, 256)
        b = np.random.randint(0, 256)
        color_map[i] = [r, g, b]
    return color_map

def load_model(model_path, config_path, device='cuda'):
    """
    加载模型

    参数:
    - model_path: 模型路径
    - config_path: 配置文件路径
    - device: 设备

    返回:
    - model: 加载的模型
    - cfg: 配置
    """
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)

    # 创建模型
    model = unet(
        num_classes=cfg['data']['num_classes'],
        backbone=cfg['model']['backbone'],
        pretrained=False
    ).to(device)

    # 加载权重
    try:
        print(f"尝试加载模型: {model_path}")
        checkpoint = torch.load(model_path, map_location=device)

        # 打印checkpoint的键，帮助调试
        if isinstance(checkpoint, dict):
            print(f"Checkpoint keys: {checkpoint.keys()}")

        # 检查checkpoint的结构
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            # 训练脚本保存的checkpoint
            state_dict = checkpoint['model_state_dict']
            print(f"从'model_state_dict'加载权重")

            # 检查state_dict的键，帮助调试
            print(f"State dict keys (前5个): {list(state_dict.keys())[:5]}")

            # 检查是否是UnetWrapper模型
            if any(k.startswith('model.') for k in state_dict.keys()):
                print("检测到UnetWrapper模型，移除'model.'前缀")
                # 移除'model.'前缀
                new_state_dict = {}
                for k, v in state_dict.items():
                    if k.startswith('model.'):
                        new_state_dict[k[6:]] = v
                    else:
                        new_state_dict[k] = v

                # 尝试加载权重
                try:
                    model.load_state_dict(new_state_dict)
                    print("成功加载权重（移除前缀后）")
                except Exception as e:
                    print(f"加载权重失败（移除前缀后）: {e}")
                    # 尝试直接加载原始state_dict
                    try:
                        model.load_state_dict(state_dict)
                        print("成功加载原始权重")
                    except Exception as e2:
                        print(f"加载原始权重失败: {e2}")
                        raise e2
            else:
                # 尝试直接加载state_dict
                try:
                    model.load_state_dict(state_dict)
                    print("成功加载权重")
                except Exception as e:
                    print(f"加载权重失败: {e}")
                    raise e
        elif isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
            # 另一种常见的checkpoint格式
            state_dict = checkpoint['state_dict']
            print(f"从'state_dict'加载权重")
            model.load_state_dict(state_dict)
        else:
            # 直接加载权重
            print("直接加载权重")
            model.load_state_dict(checkpoint)
    except Exception as e:
        print(f"加载模型时出错: {e}")
        raise e

    model.eval()
    return model, cfg

def preprocess_image(image_path, input_size=(512, 512)):
    """
    预处理图像

    参数:
    - image_path: 图像路径
    - input_size: 输入大小

    返回:
    - image: 原始图像
    - tensor: 预处理后的张量
    """
    # 读取图像
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # 预处理
    transform = A.Compose([
        A.Resize(input_size[0], input_size[1]),
        A.Normalize(mean=(0.485, 0.456, 0.406), std=(0.229, 0.224, 0.225)),
        ToTensorV2()
    ])

    transformed = transform(image=image)
    tensor = transformed['image'].unsqueeze(0)

    return image, tensor

def predict(model, image_tensor, device='cuda'):
    """
    预测

    参数:
    - model: 模型
    - image_tensor: 图像张量
    - device: 设备

    返回:
    - pred: 预测结果
    """
    with torch.no_grad():
        image_tensor = image_tensor.to(device)
        output = model(image_tensor)
        pred = torch.argmax(output, dim=1).squeeze().cpu().numpy()
    return pred

def visualize_prediction(image, pred, color_map, save_path=None, title=None):
    """
    可视化预测结果

    参数:
    - image: 原始图像
    - pred: 预测结果
    - color_map: 颜色映射
    - save_path: 保存路径
    - title: 标题
    """
    # 创建彩色分割图
    seg_color = np.zeros((pred.shape[0], pred.shape[1], 3), dtype=np.uint8)
    for c in range(len(color_map)):
        seg_color[pred == c] = color_map[c]

    # 创建叠加图
    alpha = 0.5
    overlay = cv2.addWeighted(image, 1 - alpha, seg_color, alpha, 0)

    # 显示结果
    plt.figure(figsize=(15, 5))

    plt.subplot(1, 3, 1)
    plt.imshow(image)
    plt.title('Original Image')
    plt.axis('off')

    plt.subplot(1, 3, 2)
    plt.imshow(seg_color)
    plt.title('Segmentation')
    plt.axis('off')

    plt.subplot(1, 3, 3)
    plt.imshow(overlay)
    plt.title('Overlay')
    plt.axis('off')

    if title:
        plt.suptitle(title)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()

def visualize_predictions(model, dataset_dir, num_samples=5, save_dir=None, device='cuda', input_size=(512, 512), config_path='config.yaml'):
    """
    可视化多个预测结果

    参数:
    - model: 模型
    - dataset_dir: 数据集目录
    - num_samples: 样本数量
    - save_dir: 保存目录
    - device: 设备
    - input_size: 输入大小
    - config_path: 配置文件路径
    """
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)

    # 获取颜色映射
    color_map = get_color_map(cfg['data']['num_classes'])

    # 获取图像列表
    image_files = []
    for root, _, files in os.walk(dataset_dir):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                image_files.append(os.path.join(root, file))

    # 随机选择样本
    if len(image_files) > num_samples:
        image_files = random.sample(image_files, num_samples)

    # 创建保存目录
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)

    # 预测并可视化
    for i, image_path in enumerate(tqdm(image_files, desc="Visualizing predictions")):
        # 预处理图像
        image, tensor = preprocess_image(image_path, input_size)

        # 预测
        pred = predict(model, tensor, device)

        # 可视化
        save_path = os.path.join(save_dir, f"pred_{i}.png") if save_dir else None
        visualize_prediction(image, pred, color_map, save_path, title=os.path.basename(image_path))

def parse_args():
    parser = argparse.ArgumentParser(description="可视化模型预测结果")
    parser.add_argument('--model', type=str, required=True, help='模型路径')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--image', type=str, help='单张图像路径')
    parser.add_argument('--dir', type=str, help='图像目录路径')
    parser.add_argument('--num_samples', type=int, default=5, help='可视化样本数量')
    parser.add_argument('--output_dir', type=str, default='visualization_results', help='输出目录')
    parser.add_argument('--device', type=str, default='cuda', help='设备')
    return parser.parse_args()

def main():
    args = parse_args()

    # 加载模型
    model, cfg = load_model(args.model, args.config, args.device)

    # 获取颜色映射
    color_map = get_color_map(cfg['data']['num_classes'])

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    if args.image:
        # 预测单张图像
        image, tensor = preprocess_image(args.image, cfg['data']['input_size'])
        pred = predict(model, tensor, args.device)
        save_path = os.path.join(args.output_dir, f"pred_{os.path.basename(args.image)}")
        visualize_prediction(image, pred, color_map, save_path, title=os.path.basename(args.image))
        print(f"预测结果已保存到: {save_path}")

    elif args.dir:
        # 预测目录中的图像
        visualize_predictions(
            model=model,
            dataset_dir=args.dir,
            num_samples=args.num_samples,
            save_dir=args.output_dir,
            device=args.device,
            input_size=cfg['data']['input_size'],
            config_path=args.config
        )
        print(f"预测结果已保存到: {args.output_dir}")

    else:
        print("请指定--image或--dir参数")

if __name__ == "__main__":
    main()
