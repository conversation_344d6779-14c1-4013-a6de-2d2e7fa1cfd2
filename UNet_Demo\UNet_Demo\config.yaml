# Configuration for Semantic Segmentation Training

data:
  dataset_path: "."  # 根目录，用于类别权重计算
  root_dir: "VOCdevkit"
  train_list: "VOC2025/ImageSets/Segmentation/train.txt"
  val_list:   "VOC2025/ImageSets/Segmentation/val.txt"
  train_mask_paths:
    - "VOC2025/SegmentationClass/*.png"
  input_size: [512, 512]
  num_classes: 29

  # 数据增强设置 - 增强版
  augmentation:
    # 基础几何变换
    random_resize_crop:
      enabled: true
      scale: [0.4, 1.0]      # 扩大缩放范围
      ratio: [0.7, 1.4]      # 扩大宽高比范围
      p: 0.9                 # 提高概率
    horizontal_flip:
      enabled: true
      p: 0.5
    vertical_flip:           # 添加垂直翻转
      enabled: true
      p: 0.3
    rotate:                  # 添加旋转
      enabled: true
      limit: 15              # 最大旋转角度
      p: 0.5

    # 特定领域增强 - 针对农村景观
    domain_specific:
      enabled: true
      p: 0.9                 # 提高概率

    # 随机裁剪 - 禁用，避免尺寸不一致
    random_crop:
      enabled: false
      height: 384
      width: 384
      p: 0.0

    # 颜色和光照变换 - 增强版
    color_jitter:
      enabled: true
      brightness: 0.4        # 增加亮度变化
      contrast: 0.4          # 增加对比度变化
      saturation: 0.4        # 增加饱和度变化
      hue: 0.2               # 增加色调变化
      p: 0.8                 # 提高概率

    # 添加高斯噪声和模糊
    gaussian_noise:
      enabled: true
      var_limit: [10, 50]
      p: 0.3
    gaussian_blur:
      enabled: true
      blur_limit: 7
      p: 0.3

    # 添加网格变形 - 从改进1(1).py借鉴
    grid_distortion:
      enabled: true
      p: 0.2

    # 添加粗糙丢弃 - 从改进1(1).py借鉴
    coarse_dropout:
      enabled: true
      max_holes: 8
      max_height: 32
      max_width: 32
      p: 0.2

model:
  backbone: "resnet50"         # 使用ADE20K支持的骨干网络
  pretrained: true
  pretrained_weights: ""       # 预训练权重路径，留空则使用ImageNet权重
  use_attention: true          # 保持注意力机制
  attention_type: "cbam"       # 使用CBAM注意力机制，比普通注意力更强
  dropout_rate: 0.3            # 增加dropout率防止过拟合
  transfer_learning: true      # 启用迁移学习
  finetune_mode: "progressive" # 使用渐进式微调策略
  label_alignment: true        # 启用标签对齐，将我们的标签与ADE20K标签对齐

pretrain:
  enabled: true                # 启用预训练
  dataset: "ADE20K"            # 使用ADE20K数据集预训练权重
  finetune_method: "gradual"   # 使用渐进式微调

loss:
  use_ce: true
  use_dice: true
  use_focal: true            # 启用Focal Loss，更好地处理类别不平衡
  use_lovasz: true           # 保留Lovász Loss，直接优化IoU
  weight_ce: 0.1             # 大幅降低交叉熵损失权重
  weight_dice: 2.5           # 增加Dice权重
  weight_focal: 3.0          # 大幅增加Focal Loss权重，更好处理类别不平衡
  weight_lovasz: 1.8         # 大幅增加Lovász权重，更强调IoU优化
  focal_gamma: 6.0           # 极高gamma值，专注困难样本
  focal_alpha: 0.6           # 增加alpha，更关注少数类
  use_class_weights: true    # 启用类别权重
  class_weight_method: "manual"  # 使用手动权重，专门解决类别不平衡
  manual_weights: [0.01, 1.0, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 100.0, 1.0, 90.0, 1.0, 1.0, 0.01, 1.0, 1.0, 1.0, 1.0, 80.0, 1.0, 1.0, 0.01, 1.0, 70.0, 1.0, 1.0, 60.0, 50.0, 40.0]  # 极端类别权重
  label_smoothing: 0.15      # 增加标签平滑，减轻过拟合
  ignore_index: 255          # 使用标准的忽略索引

  # 知识蒸馏设置
  use_distillation: false    # 是否使用知识蒸馏
  distillation_alpha: 0.5    # 蒸馏损失权重
  distillation_temperature: 3.0  # 温度参数

optimizer:
  type: "adamw"              # 使用AdamW优化器，从改进1(1).py借鉴
  beta1: 0.9                 # Adam/AdamW的beta1参数
  beta2: 0.999               # Adam/AdamW的beta2参数
  momentum: 0.9              # SGD的动量参数

scheduler:
  # 使用改进1(1).py中的ReduceLROnPlateau调度器
  type: "reduce_on_plateau"  # 根据验证损失自动调整学习率
  factor: 0.5                # 学习率衰减因子
  patience: 3                # 容忍多少个epoch验证损失没有改善
  threshold: 0.01            # 改善阈值
  cooldown: 0                # 冷却期
  min_lr: 5e-5               # 最小学习率

  # 以下是其他可选的调度器配置
  # type: "cosine_warmup"    # 使用余弦退火+预热调度器
  # max_lr: 0.02             # 大幅提高最大学习率
  # warmup_epochs: 5         # 增加预热轮数
  # cycles: 4                # 增加周期数
  # pct_start: 0.1           # 增加预热时间比例
  # div_factor: 4.0          # 调整除数因子
  # final_div_factor: 40.0   # 调整最终除数因子

train:
  use_cuda: true
  total_epochs: 500          # 大幅增加训练轮数
  # 分层学习率设置 - 大幅提高学习率
  init_lr: 0.002             # 大幅提高基础学习率
  encoder_lr: 5e-4           # 大幅提高编码器学习率
  decoder_lr: 1e-3           # 大幅提高解码器学习率
  head_lr: 2e-3              # 大幅提高分割头学习率
  weight_decay: 5e-4         # 增加权重衰减以减少过拟合

  # 渐进式解冻设置 - 更加激进的策略
  progressive_unfreezing: true  # 启用渐进式解冻
  freeze_epochs: 0           # 传统冻结轮数，设为0表示使用渐进式解冻
  unfreeze_schedule:
    0: ["segmentation_head", "decoder"]  # 第0轮开始训练分割头和解码器
    3: ["encoder.layer4"]                # 第3轮开始解冻编码器最后一层
    6: ["encoder.layer3"]                # 第6轮开始解冻更多层
    9: ["encoder.layer2"]                # 更快解冻
    12: ["encoder.layer1"]               # 更快解冻

  # 批量大小设置 - 针对RTX 4070 Ti Super 16GB优化，但考虑内存限制
  freeze_batch_size: 12      # 适度增加冻结阶段批量大小
  unfreeze_batch_size: 6     # 适度增加解冻阶段批量大小
  num_workers: 4             # 适度增加工作线程数，避免内存问题

  # 其他训练设置
  save_dir: "logs"
  eval_period: 1
  early_stopping: 80         # 增加早停耐心值，允许更长时间寻找最优解
  mixed_precision: true      # 使用混合精度训练
  gradient_accumulation: 4   # 减少梯度累积步数，因为已增加批次大小
  gradient_clip: 1.5         # 保持梯度裁剪
  use_channels_last: true    # 使用channels_last内存格式，提高GPU利用率
  benchmark_cudnn: true      # 启用cudnn基准测试，优化卷积操作
  verbose: true

# 预训练和微调设置
pretrain:
  enabled: true              # 启用预训练
  dataset: "ADE20K"          # 预训练数据集: ADE20K, Cityscapes, COCO-Stuff
  weights_path: ""           # 预训练权重路径，留空则自动下载
  finetune_method: "gradual" # 微调方法: all, gradual, freeze_encoder
