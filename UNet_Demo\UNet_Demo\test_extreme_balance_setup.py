#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极端类别平衡设置测试脚本
验证所有组件是否正常工作
"""

import os
import sys
import yaml
import torch
import numpy as np
import logging
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_config_loading():
    """测试配置文件加载"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试配置文件加载 ===")

    try:
        with open('config_extreme_balance.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        logger.info("✓ 配置文件加载成功")

        # 检查关键配置
        assert config['data']['num_classes'] == 29, "类别数量配置错误"
        assert len(config['class_balancing']['class_weights']) > 0, "类别权重配置为空"

        # 显示困难类别权重
        difficult_classes = [8, 10, 18, 23, 26, 27, 28]
        logger.info("困难类别权重配置:")
        for class_id in difficult_classes:
            weight = config['class_balancing']['class_weights'].get(class_id, 1.0)
            logger.info(f"  类别 {class_id}: {weight}")

        logger.info("✓ 配置文件验证通过")
        return True

    except Exception as e:
        logger.error(f"❌ 配置文件测试失败: {e}")
        return False

def test_loss_functions():
    """测试损失函数"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试损失函数 ===")

    try:
        from utils.extreme_balance_losses import ExtremeBalanceLoss, FocalLoss, DifficultClassFocalLoss

        # 创建测试数据
        batch_size, num_classes, height, width = 2, 29, 64, 64
        predictions = torch.randn(batch_size, num_classes, height, width)
        targets = torch.randint(0, num_classes, (batch_size, height, width))

        # 测试极端平衡损失
        extreme_loss = ExtremeBalanceLoss(num_classes=num_classes)
        total_loss, loss_dict = extreme_loss(predictions, targets)

        logger.info(f"✓ 极端平衡损失计算成功: {total_loss.item():.4f}")
        logger.info("损失组件:")
        for name, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                logger.info(f"  {name}: {value.item():.4f}")
            else:
                logger.info(f"  {name}: {value:.4f}")

        # 测试困难类别专用损失
        difficult_focal = DifficultClassFocalLoss(
            difficult_classes=[8, 10, 18, 23, 26, 27, 28],
            num_classes=num_classes
        )
        difficult_loss = difficult_focal(predictions, targets)
        logger.info(f"✓ 困难类别Focal损失: {difficult_loss.item():.4f}")

        logger.info("✓ 损失函数测试通过")
        return True

    except Exception as e:
        logger.error(f"❌ 损失函数测试失败: {e}")
        return False

def test_difficult_class_handler():
    """测试困难类别处理器"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试困难类别处理器 ===")

    try:
        from utils.difficult_class_handler import DifficultClassHandler

        handler = DifficultClassHandler()

        # 测试权重创建
        weights = handler.create_extreme_weights()
        logger.info(f"✓ 极端权重创建成功，形状: {weights.shape}")

        # 显示困难类别权重
        for class_id in handler.difficult_classes:
            logger.info(f"  类别 {class_id} 权重: {weights[class_id].item():.1f}")

        # 测试学习进度监控
        mock_class_ious = [0.0] * 29
        mock_class_ious[8] = 0.001  # 模拟类别8开始学习
        mock_class_ious[10] = 0.0   # 模拟类别10仍未学习

        handler.monitor_learning_progress(epoch=50, class_ious=mock_class_ious)

        # 测试动态调整建议
        suggestions = handler.suggest_dynamic_adjustments(epoch=50, class_ious=mock_class_ious)
        logger.info(f"✓ 动态调整建议: {len(suggestions)} 条")

        logger.info("✓ 困难类别处理器测试通过")
        return True

    except Exception as e:
        logger.error(f"❌ 困难类别处理器测试失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试模型创建 ===")

    try:
        from nets.unet import Unet

        # 创建模型
        model = Unet(
            num_classes=29,
            backbone='resnet50',
            pretrained=False  # 测试时不下载预训练权重
        )

        logger.info(f"✓ 模型创建成功")

        # 测试前向传播
        batch_size = 2
        test_input = torch.randn(batch_size, 3, 512, 512)

        model.eval()
        with torch.no_grad():
            output = model(test_input)

        expected_shape = (batch_size, 29, 512, 512)
        assert output.shape == expected_shape, f"输出形状错误: {output.shape} != {expected_shape}"

        logger.info(f"✓ 前向传播测试通过，输出形状: {output.shape}")

        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

        logger.info(f"✓ 模型参数: 总计 {total_params:,}, 可训练 {trainable_params:,}")

        logger.info("✓ 模型创建测试通过")
        return True

    except Exception as e:
        logger.error(f"❌ 模型创建测试失败: {e}")
        return False

def test_data_loading():
    """测试数据加载"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试数据加载 ===")

    try:
        # 检查数据目录
        data_dirs = [
            'VOCdevkit/VOC2025/JPEGImages',
            'VOCdevkit/VOC2025/SegmentationClass',
            'VOCdevkit/VOC2025/ImageSets/Segmentation'
        ]

        for data_dir in data_dirs:
            if os.path.exists(data_dir):
                file_count = len(os.listdir(data_dir))
                logger.info(f"✓ {data_dir}: {file_count} 个文件")
            else:
                logger.warning(f"⚠️ 数据目录不存在: {data_dir}")

        # 检查标注文件
        annotation_files = [
            'VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt',
            'VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt'
        ]

        for ann_file in annotation_files:
            if os.path.exists(ann_file):
                with open(ann_file, 'r') as f:
                    lines = f.readlines()
                logger.info(f"✓ {ann_file}: {len(lines)} 个样本")
            else:
                logger.warning(f"⚠️ 标注文件不存在: {ann_file}")

        # 如果数据存在，测试数据加载器
        if os.path.exists('VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt'):
            from utils.dataloader import SegmentationDataset

            with open('VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt', 'r') as f:
                lines = f.readlines()

            if lines:
                # 只测试前几个样本
                test_lines = [line.strip() for line in lines[:min(5, len(lines))]]

                dataset = SegmentationDataset(
                    file_list=test_lines,
                    root_dir='VOCdevkit',
                    img_size=(512, 512),
                    num_classes=29,
                    train=True
                )

                logger.info(f"✓ 数据集创建成功，样本数: {len(dataset)}")

                # 测试加载一个样本
                if len(dataset) > 0:
                    sample = dataset[0]
                    logger.info(f"✓ 样本加载成功，图像形状: {sample[0].shape}, 标签形状: {sample[1].shape}")

        logger.info("✓ 数据加载测试通过")
        return True

    except Exception as e:
        logger.error(f"❌ 数据加载测试失败: {e}")
        return False

def test_gpu_availability():
    """测试GPU可用性"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试GPU可用性 ===")

    try:
        logger.info(f"PyTorch版本: {torch.__version__}")
        logger.info(f"CUDA可用: {torch.cuda.is_available()}")

        if torch.cuda.is_available():
            logger.info(f"CUDA版本: {torch.version.cuda}")
            logger.info(f"GPU数量: {torch.cuda.device_count()}")

            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                logger.info(f"GPU {i}: {gpu_name}, 内存: {memory_total:.1f}GB")

            # 测试GPU计算
            device = torch.device('cuda:0')
            test_tensor = torch.randn(1000, 1000).to(device)
            result = torch.mm(test_tensor, test_tensor)
            logger.info(f"✓ GPU计算测试通过")

        else:
            logger.warning("⚠️ CUDA不可用，将使用CPU训练")

        logger.info("✓ GPU可用性测试完成")
        return True

    except Exception as e:
        logger.error(f"❌ GPU测试失败: {e}")
        return False

def test_training_components():
    """测试训练组件"""
    logger = logging.getLogger(__name__)
    logger.info("=== 测试训练组件 ===")

    try:
        # 测试优化器创建
        from nets.unet import Unet

        model = Unet(num_classes=29, backbone='resnet50', pretrained=False)

        # 分层学习率参数组
        backbone_params = []
        decoder_params = []
        classifier_params = []

        for name, param in model.named_parameters():
            if 'backbone' in name:
                backbone_params.append(param)
            elif 'classifier' in name or 'head' in name:
                classifier_params.append(param)
            else:
                decoder_params.append(param)

        param_groups = [
            {'params': backbone_params, 'lr': 5e-5},
            {'params': decoder_params, 'lr': 2e-4},
            {'params': classifier_params, 'lr': 5e-3}
        ]

        optimizer = torch.optim.AdamW(param_groups, weight_decay=0.02)
        logger.info(f"✓ 优化器创建成功，参数组数: {len(optimizer.param_groups)}")

        # 测试学习率调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer, T_0=80, T_mult=2, eta_min=1e-7
        )
        logger.info(f"✓ 学习率调度器创建成功")

        # 测试一步训练
        batch_size = 2
        test_input = torch.randn(batch_size, 3, 512, 512)
        test_target = torch.randint(0, 29, (batch_size, 512, 512))

        from utils.extreme_balance_losses import ExtremeBalanceLoss
        loss_fn = ExtremeBalanceLoss(num_classes=29)

        model.train()
        optimizer.zero_grad()

        output = model(test_input)

        # 检查输出和目标的形状
        logger.info(f"模型输出形状: {output.shape}")
        logger.info(f"目标标签形状: {test_target.shape}")

        total_loss, loss_dict = loss_fn(output, test_target)

        total_loss.backward()
        optimizer.step()
        scheduler.step()

        logger.info(f"✓ 训练步骤测试通过，损失: {total_loss.item():.4f}")
        logger.info("损失组件:")
        for name, value in loss_dict.items():
            if isinstance(value, torch.Tensor):
                logger.info(f"  {name}: {value.item():.4f}")
            else:
                logger.info(f"  {name}: {value:.4f}")

        logger.info("✓ 训练组件测试通过")
        return True

    except Exception as e:
        logger.error(f"❌ 训练组件测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger = setup_logging()

    print("🧪 极端类别平衡设置测试")
    print("=" * 50)

    tests = [
        ("配置文件加载", test_config_loading),
        ("损失函数", test_loss_functions),
        ("困难类别处理器", test_difficult_class_handler),
        ("模型创建", test_model_creation),
        ("数据加载", test_data_loading),
        ("GPU可用性", test_gpu_availability),
        ("训练组件", test_training_components)
    ]

    results = []

    for test_name, test_func in tests:
        logger.info(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))

    # 总结
    print("\n" + "=" * 50)
    print("🏁 测试结果总结")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总计: {passed}/{total} 个测试通过")

    if passed == total:
        print("🎉 所有测试通过！可以开始极端类别平衡训练")
        return True
    else:
        print("⚠️ 部分测试失败，请修复后再开始训练")
        return False

def main():
    """主函数"""
    success = run_all_tests()

    if success:
        print("\n" + "🚀" * 20)
        print("准备就绪！可以运行以下命令开始训练:")
        print("python start_extreme_balance_training.py")
        print("或者双击: start_extreme_balance.bat")
        print("🚀" * 20)
    else:
        print("\n" + "🔧" * 20)
        print("请修复测试失败的问题后重新运行测试")
        print("🔧" * 20)

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
