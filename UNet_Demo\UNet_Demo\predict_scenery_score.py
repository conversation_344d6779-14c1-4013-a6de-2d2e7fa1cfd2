"""
预测美景度得分
"""
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from torch.utils.data import Dataset, DataLoader
import numpy as np
import cv2
import matplotlib.pyplot as plt
from tqdm import tqdm
import argparse
import yaml
import pandas as pd
import albumentations as A
from albumentations.pytorch import ToTensorV2

from test_model import load_model as load_segmentation_model, predict_image

class SceneryScoreModel(nn.Module):
    """
    美景度评分模型
    """
    def __init__(self, base_model='resnet50', num_classes=29):
        super(SceneryScoreModel, self).__init__()
        # 图像特征提取器
        if base_model == 'resnet50':
            self.feature_extractor = models.resnet50(pretrained=True)
            feature_dim = 2048
        elif base_model == 'resnet101':
            self.feature_extractor = models.resnet101(pretrained=True)
            feature_dim = 2048
        elif base_model == 'efficientnet-b4':
            try:
                from efficientnet_pytorch import EfficientNet
                self.feature_extractor = EfficientNet.from_pretrained('efficientnet-b4')
                feature_dim = 1792
            except ImportError:
                print("EfficientNet not installed, falling back to ResNet50")
                self.feature_extractor = models.resnet50(pretrained=True)
                feature_dim = 2048
        else:
            raise ValueError(f"Unsupported base model: {base_model}")

        self.feature_extractor = nn.Sequential(*list(self.feature_extractor.children())[:-1])

        # 分割特征处理
        self.seg_encoder = nn.Sequential(
            nn.Conv2d(num_classes, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((1, 1))
        )

        # 回归头
        self.regressor = nn.Sequential(
            nn.Linear(feature_dim + 128, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 1)
        )

    def forward(self, img, seg_mask):
        # 提取图像特征
        img_features = self.feature_extractor(img)
        img_features = img_features.view(img_features.size(0), -1)

        # 提取分割特征
        seg_features = self.seg_encoder(seg_mask)
        seg_features = seg_features.view(seg_features.size(0), -1)

        # 特征融合
        combined = torch.cat([img_features, seg_features], dim=1)

        # 预测分数
        score = self.regressor(combined)
        return score

class SceneryScoreDataset(Dataset):
    """
    美景度评分数据集
    """
    def __init__(self, image_paths, seg_paths, scores=None, img_size=(512, 512), transform=None):
        self.image_paths = image_paths
        self.seg_paths = seg_paths
        self.scores = scores  # 可以为None，用于预测
        self.img_size = img_size
        self.transform = transform

        # 默认变换
        if self.transform is None:
            self.transform = A.Compose([
                A.Resize(img_size[0], img_size[1]),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        seg_path = self.seg_paths[idx]

        # 读取图像和分割掩码
        img = cv2.imread(img_path)
        if img is None:
            raise ValueError(f"无法读取图像: {img_path}")
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        seg = cv2.imread(seg_path, cv2.IMREAD_GRAYSCALE)
        if seg is None:
            raise ValueError(f"无法读取分割掩码: {seg_path}")

        # 应用变换
        augmented = self.transform(image=img)
        img_tensor = augmented['image']

        # 处理分割掩码
        seg = cv2.resize(seg, (self.img_size[1], self.img_size[0]), interpolation=cv2.INTER_NEAREST)
        seg_tensor = torch.from_numpy(seg).long()
        seg_onehot = F.one_hot(seg_tensor, num_classes=29).permute(2, 0, 1).float()

        if self.scores is not None:
            score = self.scores[idx]
            return img_tensor, seg_onehot, torch.tensor(score, dtype=torch.float32)
        else:
            return img_tensor, seg_onehot, img_path

def train_score_model(train_loader, val_loader, device, num_classes=29, base_model='resnet50', epochs=50, lr=0.0001):
    """
    训练美景度评分模型
    """
    model = SceneryScoreModel(base_model=base_model, num_classes=num_classes).to(device)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5)

    best_val_loss = float('inf')
    best_model_state = None

    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        for imgs, segs, scores in tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs} [Train]", leave=True, bar_format='{l_bar}{bar}{r_bar}'):
            imgs = imgs.to(device)
            segs = segs.to(device)
            scores = scores.to(device)

            optimizer.zero_grad()
            outputs = model(imgs, segs)
            loss = criterion(outputs.squeeze(), scores)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()

        train_loss /= len(train_loader)

        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for imgs, segs, scores in tqdm(val_loader, desc=f"Epoch {epoch+1}/{epochs} [Val]", leave=True, bar_format='{l_bar}{bar}{r_bar}'):
                imgs = imgs.to(device)
                segs = segs.to(device)
                scores = scores.to(device)

                outputs = model(imgs, segs)
                loss = criterion(outputs.squeeze(), scores)
                val_loss += loss.item()

        val_loss /= len(val_loader)
        scheduler.step(val_loss)

        print(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")

        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict().copy()

    # 加载最佳模型
    model.load_state_dict(best_model_state)
    return model

def predict_scores(model, seg_model, cfg, test_dir, device, output_path='predicted_scores.csv'):
    """
    预测美景度得分
    """
    # 获取测试图像列表
    test_images = [os.path.join(test_dir, f) for f in os.listdir(test_dir)
                  if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]

    # 创建临时目录保存分割结果
    temp_seg_dir = 'temp_segmentations'
    os.makedirs(temp_seg_dir, exist_ok=True)

    # 分割所有图像
    seg_paths = []
    for img_path in tqdm(test_images, desc="Segmenting images"):
        pred, _ = predict_image(seg_model, img_path, cfg, device)
        seg_path = os.path.join(temp_seg_dir, os.path.basename(img_path).replace('.jpg', '.png'))
        cv2.imwrite(seg_path, pred)
        seg_paths.append(seg_path)

    # 创建数据集和数据加载器
    dataset = SceneryScoreDataset(test_images, seg_paths, scores=None,
                                 img_size=tuple(cfg['data']['input_size']))
    dataloader = DataLoader(dataset, batch_size=8, shuffle=False, num_workers=4)

    # 预测分数
    model.eval()
    predictions = []
    image_paths = []

    with torch.no_grad():
        for imgs, segs, paths in tqdm(dataloader, desc="Predicting scores", leave=True, bar_format='{l_bar}{bar}{r_bar}'):
            imgs = imgs.to(device)
            segs = segs.to(device)

            outputs = model(imgs, segs)
            scores = outputs.squeeze().cpu().numpy()

            predictions.extend(scores.tolist() if isinstance(scores, np.ndarray) else [scores])
            image_paths.extend(paths)

    # 保存预测结果
    results = pd.DataFrame({
        'image_path': image_paths,
        'predicted_score': predictions
    })
    results.to_csv(output_path, index=False)
    print(f"Predictions saved to {output_path}")

    return results

def main():
    parser = argparse.ArgumentParser(description="Train and predict scenery scores")
    parser.add_argument('--mode', type=str, choices=['train', 'predict'], required=True,
                        help='Mode: train or predict')
    parser.add_argument('--seg_model', type=str, default='logs/best_model.pth',
                        help='Path to segmentation model weights')
    parser.add_argument('--config', type=str, default='config.yaml',
                        help='Path to config file')
    parser.add_argument('--score_model', type=str, default='scenery_score_model.pth',
                        help='Path to score model (for predict mode) or save path (for train mode)')
    parser.add_argument('--train_csv', type=str, default='train_scores.csv',
                        help='CSV file with image paths and scores for training')
    parser.add_argument('--test_dir', type=str, default='test_images',
                        help='Directory with test images for prediction')
    parser.add_argument('--output', type=str, default='predicted_scores.csv',
                        help='Output CSV file for predictions')
    args = parser.parse_args()

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 加载分割模型
    seg_model, cfg = load_segmentation_model(args.seg_model, args.config, device)
    print(f"Segmentation model loaded from {args.seg_model}")

    if args.mode == 'train':
        # 加载训练数据
        train_data = pd.read_csv(args.train_csv)

        # 分割训练图像
        temp_seg_dir = 'temp_train_segmentations'
        os.makedirs(temp_seg_dir, exist_ok=True)

        seg_paths = []
        for img_path in tqdm(train_data['image_path'], desc="Segmenting training images"):
            pred, _ = predict_image(seg_model, img_path, cfg, device)
            seg_path = os.path.join(temp_seg_dir, os.path.basename(img_path).replace('.jpg', '.png'))
            cv2.imwrite(seg_path, pred)
            seg_paths.append(seg_path)

        # 划分训练集和验证集
        from sklearn.model_selection import train_test_split
        train_imgs, val_imgs, train_segs, val_segs, train_scores, val_scores = train_test_split(
            train_data['image_path'].tolist(), seg_paths, train_data['score'].tolist(),
            test_size=0.2, random_state=42)

        # 创建数据集和数据加载器
        train_dataset = SceneryScoreDataset(train_imgs, train_segs, train_scores,
                                           img_size=tuple(cfg['data']['input_size']))
        val_dataset = SceneryScoreDataset(val_imgs, val_segs, val_scores,
                                         img_size=tuple(cfg['data']['input_size']))

        train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=4)
        val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, num_workers=4)

        # 训练模型
        model = train_score_model(train_loader, val_loader, device,
                                 num_classes=cfg['data']['num_classes'],
                                 base_model=cfg['model']['backbone'])

        # 保存模型
        torch.save(model.state_dict(), args.score_model)
        print(f"Score model saved to {args.score_model}")

    elif args.mode == 'predict':
        # 加载评分模型
        model = SceneryScoreModel(base_model=cfg['model']['backbone'],
                                 num_classes=cfg['data']['num_classes']).to(device)
        model.load_state_dict(torch.load(args.score_model, map_location=device))
        print(f"Score model loaded from {args.score_model}")

        # 预测分数
        predict_scores(model, seg_model, cfg, args.test_dir, device, args.output)

if __name__ == '__main__':
    main()
