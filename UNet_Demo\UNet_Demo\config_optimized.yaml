# Configuration for Semantic Segmentation Training - Optimized Learning Rate Strategy

data:
  dataset_path: "."  # 根目录，用于类别权重计算
  root_dir: "VOCdevkit"
  train_list: "VOC2025/ImageSets/Segmentation/train.txt"
  val_list:   "VOC2025/ImageSets/Segmentation/val.txt"
  train_mask_paths:
    - "VOC2025/SegmentationClass/*.png"
  input_size: [512, 512]
  num_classes: 29

  # 数据增强设置 - 更平衡的增强策略
  augmentation:
    # 基础几何变换
    random_resize_crop:
      enabled: true
      scale: [0.5, 1.0]      # 缩小缩放范围，避免过度裁剪
      ratio: [0.8, 1.3]      # 缩小宽高比范围，保持更多原始形状
      p: 0.8                 # 降低概率，减少变形
    horizontal_flip:
      enabled: true
      p: 0.5
    vertical_flip:           # 添加垂直翻转
      enabled: true
      p: 0.2                 # 降低垂直翻转概率
    rotate:                  # 添加旋转
      enabled: true
      limit: 10              # 减小最大旋转角度
      p: 0.4                 # 降低旋转概率

    # 特定领域增强 - 针对农村景观
    domain_specific:
      enabled: true
      p: 0.7                 # 降低概率，减少过度增强

    # 随机裁剪 - 禁用，避免尺寸不一致
    random_crop:
      enabled: false
      height: 384
      width: 384
      p: 0.0

    # 颜色和光照变换 - 更平衡的版本
    color_jitter:
      enabled: true
      brightness: 0.3        # 减小亮度变化
      contrast: 0.3          # 减小对比度变化
      saturation: 0.3        # 减小饱和度变化
      hue: 0.1               # 减小色调变化
      p: 0.6                 # 降低概率

    # 添加高斯噪声和模糊
    gaussian_noise:
      enabled: true
      var_limit: [5, 30]     # 减小噪声范围
      p: 0.2                 # 降低概率
    gaussian_blur:
      enabled: true
      blur_limit: 5          # 减小模糊范围
      p: 0.2                 # 降低概率

    # 添加网格变形
    grid_distortion:
      enabled: true
      p: 0.1                 # 降低概率

    # 添加粗糙丢弃
    coarse_dropout:
      enabled: true
      max_holes: 6           # 减少最大孔洞数
      max_height: 24         # 减小最大高度
      max_width: 24          # 减小最大宽度
      p: 0.15                # 降低概率

    # 暂时禁用弹性变换，可能导致错误
    elastic_transform:
      enabled: false
      alpha: 1.0
      sigma: 50.0
      p: 0.0

model:
  backbone: "resnet50"         # 使用ADE20K支持的骨干网络
  pretrained: true
  pretrained_weights: ""       # 预训练权重路径，留空则使用ImageNet权重
  use_attention: true          # 保持注意力机制
  attention_type: "cbam"       # 使用CBAM注意力机制，比普通注意力更强
  dropout_rate: 0.25           # 降低dropout率，避免过度正则化
  transfer_learning: true      # 启用迁移学习
  finetune_mode: "progressive" # 使用渐进式微调策略
  label_alignment: true        # 启用标签对齐，将我们的标签与ADE20K标签对齐

pretrain:
  enabled: true                # 启用预训练
  dataset: "ADE20K"            # 使用ADE20K数据集预训练权重
  finetune_method: "gradual"   # 使用渐进式微调

loss:
  use_ce: true
  use_dice: true
  use_focal: false           # 禁用Focal Loss，简化损失函数
  use_lovasz: false          # 禁用Lovász Loss，简化损失函数
  weight_ce: 0.5             # 调整交叉熵损失权重
  weight_dice: 1.0           # 调整Dice权重
  weight_focal: 0.0          # 禁用Focal Loss权重
  weight_lovasz: 0.0         # 禁用Lovász权重
  focal_gamma: 2.0           # 保留默认gamma
  focal_alpha: 0.5           # 保留默认alpha
  use_class_weights: true    # 启用类别权重
  class_weight_method: "inverse_frequency"  # 使用逆频率方法计算权重
  label_smoothing: 0.1       # 减小标签平滑
  ignore_index: 255          # 使用标准的忽略索引

  # 知识蒸馏设置
  use_distillation: false    # 是否使用知识蒸馏
  distillation_alpha: 0.5    # 蒸馏损失权重
  distillation_temperature: 3.0  # 温度参数

optimizer:
  type: "adamw"              # 使用AdamW优化器
  beta1: 0.9                 # Adam/AdamW的beta1参数
  beta2: 0.999               # Adam/AdamW的beta2参数
  momentum: 0.9              # SGD的动量参数

# 学习率策略优化
scheduler:
  # 使用余弦退火+预热调度器
  type: "cosine_warmup"      # 使用余弦退火+预热调度器
  max_lr: 0.001              # 降低最大学习率
  warmup_epochs: 5           # 增加预热轮数
  cycles: 1                  # 使用单个周期
  pct_start: 0.1             # 增加预热时间比例
  div_factor: 10.0           # 增加除数因子
  final_div_factor: 100.0    # 调整最终除数因子
  min_lr: 1e-6               # 降低最小学习率

  # 以下是其他可选的调度器配置
  # type: "reduce_on_plateau"  # 根据验证损失自动调整学习率
  # factor: 0.5                # 学习率衰减因子
  # patience: 2                # 容忍多少个epoch验证损失没有改善
  # threshold: 0.005           # 改善阈值
  # cooldown: 0                # 冷却期
  # min_lr: 5e-5               # 最小学习率

train:
  use_cuda: true
  total_epochs: 100          # 减少训练轮数
  # 分层学习率设置 - 更保守的学习率分配
  init_lr: 0.001             # 降低基础学习率
  encoder_lr: 5e-4           # 降低编码器学习率
  decoder_lr: 1e-3           # 降低解码器学习率
  head_lr: 2e-3              # 降低分割头学习率
  weight_decay: 1e-4         # 减少权重衰减

  # 渐进式解冻设置 - 简化策略
  progressive_unfreezing: true  # 启用渐进式解冻
  freeze_epochs: 0           # 传统冻结轮数，设为0表示使用渐进式解冻
  unfreeze_schedule:
    0: ["segmentation_head", "decoder"]  # 第0轮开始训练分割头和解码器
    3: ["encoder.layer4"]                # 第3轮开始解冻编码器最后一层
    6: ["encoder.layer3", "encoder.layer2", "encoder.layer1"]  # 第6轮开始解冻所有剩余层

  # 批量大小设置 - 针对RTX 4070 Ti Super 16GB优化
  freeze_batch_size: 8       # 减小冻结阶段批量大小
  unfreeze_batch_size: 4     # 减小解冻阶段批量大小
  num_workers: 4             # 减少工作线程数

  # 其他训练设置
  save_dir: "logs"
  eval_period: 1
  early_stopping: 20         # 减少早停耐心值
  mixed_precision: true      # 使用混合精度训练
  gradient_accumulation: 2   # 减少梯度累积步数
  gradient_clip: 1.0         # 减小梯度裁剪阈值
  use_channels_last: true    # 使用channels_last内存格式，提高GPU利用率
  benchmark_cudnn: true      # 启用cudnn基准测试，优化卷积操作
  verbose: true
