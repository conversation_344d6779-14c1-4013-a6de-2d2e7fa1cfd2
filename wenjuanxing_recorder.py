#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问卷星操作录制器 - 简化版本
录制您的手动操作，生成完善的自动化脚本
"""

import time
import json
import os
from datetime import datetime
import keyboard
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.options import Options
from selenium.webdriver.edge.service import Service
import logging

class WenjuanxingRecorder:
    """问卷星操作录制器"""
    
    def __init__(self):
        self.recording = False
        self.operations = []
        self.driver = None
        self.step_count = 0
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        log_filename = f"wenjuanxing_recorder_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_browser(self):
        """设置浏览器"""
        try:
            edge_options = Options()
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-dev-shm-usage')
            
            # 使用已下载的EdgeDriver
            if os.path.exists("msedgedriver.exe"):
                service = Service("msedgedriver.exe")
                self.driver = webdriver.Edge(service=service, options=edge_options)
            else:
                self.driver = webdriver.Edge(options=edge_options)
            
            self.driver.maximize_window()
            self.logger.info("浏览器设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器设置失败: {e}")
            return False
    
    def start_recording(self, url):
        """开始录制"""
        print("🎬 问卷星操作录制器")
        print("=" * 50)
        print("📋 录制说明：")
        print("  1. 浏览器将自动打开问卷编辑页面")
        print("  2. 请手动完成登录（如需要）")
        print("  3. 按空格键记录当前步骤")
        print("  4. 按回车键停止录制")
        print("  5. 脚本将自动生成完善的自动化代码")
        
        # 设置浏览器
        if not self.setup_browser():
            return False
        
        # 打开页面
        self.driver.get(url)
        time.sleep(3)
        
        # 初始截图
        self.take_screenshot("initial_page")
        
        print("\n🔴 录制已开始！")
        print("请在浏览器中手动创建一个完整的循环评价题目")
        print("每完成一个关键步骤，按空格键记录")
        print("完成后按回车键停止录制")
        
        # 开始交互式录制
        self.interactive_recording()
        
        return True
    
    def interactive_recording(self):
        """交互式录制"""
        self.recording = True
        
        while self.recording:
            try:
                print(f"\n📍 步骤 {self.step_count + 1}")
                print("请在浏览器中执行操作，完成后按空格键记录...")
                
                # 等待用户按键
                key = keyboard.read_event()
                if key.event_type == keyboard.KEY_DOWN:
                    if key.name == 'space':
                        self.record_current_step()
                    elif key.name == 'enter':
                        self.stop_recording()
                        break
                    elif key.name == 'esc':
                        self.emergency_exit()
                        break
                
            except Exception as e:
                self.logger.error(f"录制过程出错: {e}")
                break
    
    def record_current_step(self):
        """记录当前步骤"""
        try:
            self.step_count += 1
            
            # 获取当前页面信息
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            # 截图
            screenshot_file = self.take_screenshot(f"step_{self.step_count}")
            
            # 获取页面上的关键元素
            elements_info = self.analyze_page_elements()
            
            # 询问用户这一步做了什么
            print(f"\n📝 记录步骤 {self.step_count}")
            action_description = input("请描述您刚才执行的操作: ")
            
            # 记录操作
            operation = {
                'step': self.step_count,
                'description': action_description,
                'url': current_url,
                'title': page_title,
                'screenshot': screenshot_file,
                'elements': elements_info,
                'timestamp': datetime.now().isoformat()
            }
            
            self.operations.append(operation)
            
            self.logger.info(f"步骤 {self.step_count} 已记录: {action_description}")
            print(f"✅ 步骤 {self.step_count} 已记录")
            
        except Exception as e:
            self.logger.error(f"记录步骤失败: {e}")
    
    def analyze_page_elements(self):
        """分析页面元素"""
        try:
            elements_info = {}
            
            # 查找常见的问卷星元素
            selectors = {
                'buttons': "//button | //a[contains(@class, 'btn')] | //span[contains(@class, 'btn')]",
                'inputs': "//input | //textarea",
                'clickable_spans': "//span[contains(text(), '添加')] | //span[contains(text(), '保存')] | //span[contains(text(), '设置')]",
                'question_elements': "//*[contains(@class, 'question')] | //*[contains(@id, 'question')]",
                'upload_elements': "//*[contains(text(), '上传')] | //input[@type='file']"
            }
            
            for element_type, xpath in selectors.items():
                try:
                    elements = self.driver.find_elements(By.XPATH, xpath)
                    elements_info[element_type] = []
                    
                    for elem in elements[:5]:  # 只取前5个
                        try:
                            elem_info = {
                                'tag': elem.tag_name,
                                'text': elem.text[:50] if elem.text else '',
                                'id': elem.get_attribute('id') or '',
                                'class': elem.get_attribute('class') or '',
                                'xpath': self.get_element_xpath(elem)
                            }
                            elements_info[element_type].append(elem_info)
                        except:
                            continue
                            
                except Exception as e:
                    elements_info[element_type] = []
            
            return elements_info
            
        except Exception as e:
            self.logger.error(f"分析页面元素失败: {e}")
            return {}
    
    def get_element_xpath(self, element):
        """获取元素的XPath"""
        try:
            script = """
            function getXPath(element) {
                if (element.id !== '') {
                    return "//*[@id='" + element.id + "']";
                }
                if (element === document.body) {
                    return '/html/body';
                }
                var ix = 0;
                var siblings = element.parentNode.childNodes;
                for (var i = 0; i < siblings.length; i++) {
                    var sibling = siblings[i];
                    if (sibling === element) {
                        return getXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
                    }
                    if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                        ix++;
                    }
                }
            }
            return getXPath(arguments[0]);
            """
            return self.driver.execute_script(script, element)
        except:
            return ""
    
    def take_screenshot(self, name):
        """截图"""
        try:
            filename = f"recording_{name}_{datetime.now().strftime('%H%M%S')}.png"
            self.driver.save_screenshot(filename)
            self.logger.info(f"截图保存: {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"截图失败: {e}")
            return None
    
    def stop_recording(self):
        """停止录制"""
        self.recording = False
        print("\n⏹️ 录制已停止")
        
        # 保存录制数据
        self.save_recording()
        
        # 生成自动化脚本
        self.generate_automation_script()
        
        # 关闭浏览器
        if self.driver:
            self.driver.quit()
    
    def emergency_exit(self):
        """紧急退出"""
        self.recording = False
        print("\n🚨 紧急退出")
        if self.driver:
            self.driver.quit()
    
    def save_recording(self):
        """保存录制数据"""
        try:
            filename = f"wenjuanxing_recording_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            recording_data = {
                'metadata': {
                    'total_steps': self.step_count,
                    'recording_time': datetime.now().isoformat(),
                    'purpose': '问卷星循环评价题目创建'
                },
                'operations': self.operations
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(recording_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"录制数据保存: {filename}")
            print(f"📁 录制数据已保存: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存录制数据失败: {e}")
    
    def generate_automation_script(self):
        """生成自动化脚本"""
        try:
            script_content = self.create_enhanced_script()
            
            filename = f"wenjuanxing_enhanced_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            self.logger.info(f"增强脚本生成: {filename}")
            print(f"🤖 增强的自动化脚本已生成: {filename}")
            print(f"📋 脚本包含 {self.step_count} 个步骤的完整操作流程")
            
        except Exception as e:
            self.logger.error(f"生成脚本失败: {e}")
    
    def create_enhanced_script(self):
        """创建增强的自动化脚本"""
        # 这里会根据录制的操作生成完整的自动化脚本
        script_template = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问卷星自动化脚本 - 基于录制操作生成
录制时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总步骤数: {self.step_count}
"""

import time
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.options import Options
from selenium.webdriver.edge.service import Service
from selenium.common.exceptions import TimeoutException, ElementClickInterceptedException

class WenjuanxingEnhancedAutomation:
    """增强的问卷星自动化工具"""
    
    def __init__(self, image_folder, questionnaire_url):
        self.image_folder = image_folder
        self.questionnaire_url = questionnaire_url
        self.driver = None
        self.wait = None
        self.images = self.load_images()
    
    def load_images(self):
        """加载图片"""
        import glob
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif']
        images = []
        
        for ext in image_extensions:
            pattern = os.path.join(self.image_folder, ext)
            images.extend(glob.glob(pattern))
            pattern_upper = os.path.join(self.image_folder, ext.upper())
            images.extend(glob.glob(pattern_upper))
        
        images.sort()
        return images
    
    def setup_browser(self):
        """设置浏览器"""
        edge_options = Options()
        edge_options.add_argument('--no-sandbox')
        edge_options.add_argument('--disable-dev-shm-usage')
        
        if os.path.exists("msedgedriver.exe"):
            service = Service("msedgedriver.exe")
            self.driver = webdriver.Edge(service=service, options=edge_options)
        else:
            self.driver = webdriver.Edge(options=edge_options)
        
        self.driver.maximize_window()
        self.wait = WebDriverWait(self.driver, 20)
    
    def safe_click(self, selectors, timeout=10, description=""):
        """安全点击 - 尝试多个选择器"""
        if isinstance(selectors, str):
            selectors = [selectors]
        
        for selector in selectors:
            try:
                # 等待元素可点击
                element = WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                
                # 尝试点击
                try:
                    element.click()
                    print(f"✅ 成功点击: {{description}}")
                    return True
                except ElementClickInterceptedException:
                    # 如果被遮挡，尝试JavaScript点击
                    self.driver.execute_script("arguments[0].click();", element)
                    print(f"✅ JavaScript点击成功: {{description}}")
                    return True
                    
            except TimeoutException:
                continue
        
        print(f"❌ 点击失败: {{description}}")
        return False
    
    def safe_input(self, selectors, text, timeout=10, description=""):
        """安全输入"""
        if isinstance(selectors, str):
            selectors = [selectors]
        
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                element.clear()
                element.send_keys(text)
                print(f"✅ 成功输入: {{description}}")
                return True
            except TimeoutException:
                continue
        
        print(f"❌ 输入失败: {{description}}")
        return False
    
    def create_question(self, question_number, image_paths):
        """创建题目 - 基于录制的步骤"""
        print(f"\\n🔄 开始创建第 {{question_number}} 题...")
        
        try:
{self.generate_steps_code()}
            
            print(f"✅ 第 {{question_number}} 题创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 第 {{question_number}} 题创建失败: {{e}}")
            return False
    
    def run(self, questions_count=30):
        """运行自动化"""
        try:
            self.setup_browser()
            self.driver.get(self.questionnaire_url)
            
            # 处理登录
            if "login" in self.driver.current_url.lower():
                print("请手动完成登录...")
                input("登录完成后按回车继续...")
                self.driver.get(self.questionnaire_url)
            
            # 创建题目
            images_per_question = 10
            for i in range(1, questions_count + 1):
                start_idx = (i - 1) * images_per_question
                end_idx = start_idx + images_per_question
                question_images = self.images[start_idx:end_idx]
                
                success = self.create_question(i, question_images)
                if not success:
                    user_choice = input(f"第 {{i}} 题失败，继续？(y/n): ")
                    if user_choice.lower() != 'y':
                        break
                
                time.sleep(2)  # 题目间隔
            
            print("🎉 自动化执行完成！")
            
        except Exception as e:
            print(f"❌ 执行失败: {{e}}")
        finally:
            if self.driver:
                input("按回车关闭浏览器...")
                self.driver.quit()

def main():
    image_folder = r"D:\\1a_taohuacun"
    questionnaire_url = "https://www.wjx.cn/wjxdesignnew/designnew.aspx?version=7&openType=redesign&curid=316618829&nqt=&sguid=8d03ce0d-b368-47b9-8646-e129a13932f2"
    
    automation = WenjuanxingEnhancedAutomation(image_folder, questionnaire_url)
    automation.run(30)

if __name__ == "__main__":
    main()
'''
        
        return script_template
    
    def generate_steps_code(self):
        """生成步骤代码"""
        steps_code = ""
        
        for i, op in enumerate(self.operations):
            description = op.get('description', f'步骤{i+1}')
            
            # 根据描述生成对应的代码
            if '添加题目' in description or '添加' in description:
                steps_code += f'''
            # {description}
            add_selectors = [
                "//span[contains(text(), '添加题目')]",
                "//button[contains(text(), '添加题目')]",
                "//div[contains(text(), '添加题目')]"
            ]
            self.safe_click(add_selectors, description="{description}")
            time.sleep(3)
'''
            elif '循环评价' in description:
                steps_code += f'''
            # {description}
            loop_selectors = [
                "//div[contains(text(), '循环评价')]",
                "//span[contains(text(), '循环评价')]",
                "//li[contains(text(), '循环评价')]"
            ]
            self.safe_click(loop_selectors, description="{description}")
            time.sleep(3)
'''
            elif '标题' in description or '题目' in description:
                steps_code += f'''
            # {description}
            title_selectors = [
                "//input[@placeholder='请输入题目']",
                "//input[contains(@placeholder, '题目')]",
                "//textarea[contains(@placeholder, '题目')]"
            ]
            self.safe_input(title_selectors, f"图片评价题目 {{question_number}}", description="{description}")
            time.sleep(2)
'''
            elif '保存' in description:
                steps_code += f'''
            # {description}
            save_selectors = [
                "//span[contains(text(), '保存')]",
                "//button[contains(text(), '保存')]",
                "//div[contains(text(), '保存')]"
            ]
            self.safe_click(save_selectors, description="{description}")
            time.sleep(3)
'''
            else:
                steps_code += f'''
            # {description}
            print("执行: {description}")
            time.sleep(2)
'''
        
        return steps_code

def main():
    """主函数"""
    url = "https://www.wjx.cn/wjxdesignnew/designnew.aspx?version=7&openType=redesign&curid=316618829&nqt=&sguid=8d03ce0d-b368-47b9-8646-e129a13932f2"
    
    recorder = WenjuanxingRecorder()
    recorder.start_recording(url)

if __name__ == "__main__":
    main()
