#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强数据集类
适配现有的VOC数据结构
"""

import os
import cv2
import numpy as np
from PIL import Image
import torch
from torch.utils.data import Dataset
import albumentations as A

class EnhancedSegmentationDataset(Dataset):
    """增强的语义分割数据集"""

    def __init__(self, annotation_lines, input_shape=(256, 256), num_classes=29, transform=None):
        """
        Args:
            annotation_lines: 标注文件行列表
            input_shape: 输入图像尺寸
            num_classes: 类别数量
            transform: 数据增强变换
        """
        self.annotation_lines = annotation_lines
        self.input_shape = input_shape
        self.num_classes = num_classes
        self.transform = transform

    def __len__(self):
        return len(self.annotation_lines)

    def __getitem__(self, index):
        annotation_line = self.annotation_lines[index]
        name = annotation_line.split()[0]

        # 读取图像和标签
        jpg_path = os.path.join("UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/JPEGImages", name + ".jpg")
        png_path = os.path.join("UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/SegmentationClass", name + ".png")

        # 读取图像
        image = cv2.imread(jpg_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 读取标签
        label = Image.open(png_path)
        label = np.array(label, dtype=np.int64)  # 确保是int64类型

        # 应用数据增强
        if self.transform:
            augmented = self.transform(image=image, mask=label)
            image = augmented['image']
            label = augmented['mask']
        else:
            # 基础预处理
            image = cv2.resize(image, self.input_shape)
            label = cv2.resize(label, self.input_shape, interpolation=cv2.INTER_NEAREST)

            # 转换为tensor
            image = torch.from_numpy(image.transpose(2, 0, 1)).float() / 255.0
            label = torch.from_numpy(label).long()

        # 确保标签是正确的数据类型
        if isinstance(label, torch.Tensor):
            label = label.long()  # 强制转换为long类型
            label = label.contiguous()
        else:
            # 如果不是tensor，转换为long tensor
            label = torch.from_numpy(np.array(label, dtype=np.int64)).long()

        # 确保图像tensor是连续的
        if isinstance(image, torch.Tensor):
            image = image.contiguous()

        return image, label

def load_annotation_lines():
    """加载标注文件"""
    train_path = "UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt"
    val_path = "UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt"

    # 读取训练集
    with open(train_path, 'r') as f:
        train_lines = f.readlines()
    train_lines = [line.strip() for line in train_lines]

    # 读取验证集
    with open(val_path, 'r') as f:
        val_lines = f.readlines()
    val_lines = [line.strip() for line in val_lines]

    return train_lines, val_lines

def create_enhanced_datasets():
    """创建增强数据集"""
    from train_enhanced_v2 import EnhancedDataAugmentation

    # 加载标注
    train_lines, val_lines = load_annotation_lines()

    print(f"📊 数据集信息:")
    print(f"  训练集: {len(train_lines)} 张图像")
    print(f"  验证集: {len(val_lines)} 张图像")

    # 创建数据增强
    train_transforms = EnhancedDataAugmentation.get_training_transforms()
    val_transforms = EnhancedDataAugmentation.get_validation_transforms()

    # 创建数据集
    train_dataset = EnhancedSegmentationDataset(
        annotation_lines=train_lines,
        transform=train_transforms
    )

    val_dataset = EnhancedSegmentationDataset(
        annotation_lines=val_lines,
        transform=val_transforms
    )

    return train_dataset, val_dataset

if __name__ == "__main__":
    # 测试数据集
    train_dataset, val_dataset = create_enhanced_datasets()

    print(f"✅ 数据集创建成功")
    print(f"📊 训练集大小: {len(train_dataset)}")
    print(f"📊 验证集大小: {len(val_dataset)}")

    # 测试一个样本
    image, label = train_dataset[0]
    print(f"📸 图像形状: {image.shape}")
    print(f"🏷️ 标签形状: {label.shape}")
    print(f"🎯 标签类别范围: {label.min().item()} - {label.max().item()}")
