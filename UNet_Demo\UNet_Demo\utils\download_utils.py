"""
下载工具函数，用于下载预训练权重
"""
import os
import time
import requests
import logging
import urllib.request
import urllib.error
import ssl
import certifi
from tqdm import tqdm

logger = logging.getLogger(__name__)

def download_file_with_progress(url, destination, max_retries=5, timeout=30, chunk_size=8192):
    """
    下载文件并显示进度条，支持重试

    Args:
        url: 下载URL
        destination: 目标文件路径
        max_retries: 最大重试次数
        timeout: 超时时间（秒）
        chunk_size: 每次读取的块大小

    Returns:
        bool: 下载是否成功
    """
    # 创建目标目录
    os.makedirs(os.path.dirname(destination), exist_ok=True)

    # 如果文件已存在，直接返回成功
    if os.path.exists(destination):
        logger.info(f"文件已存在: {destination}")
        return True

    # 临时文件路径
    temp_file = f"{destination}.tmp"

    for retry in range(max_retries):
        try:
            logger.info(f"下载文件 (尝试 {retry+1}/{max_retries}): {url} -> {destination}")

            # 使用requests库下载
            response = requests.get(url, stream=True, timeout=timeout, verify=True)
            response.raise_for_status()  # 检查HTTP错误

            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))

            # 创建进度条
            progress_bar = tqdm(total=total_size, unit='B', unit_scale=True, desc=f"下载中", leave=True, bar_format='{l_bar}{bar}{r_bar}')

            # 下载文件
            with open(temp_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:  # 过滤掉保持连接的空块
                        f.write(chunk)
                        progress_bar.update(len(chunk))

            progress_bar.close()

            # 重命名临时文件
            os.rename(temp_file, destination)
            logger.info(f"下载成功: {destination}")
            return True

        except (requests.exceptions.RequestException, IOError) as e:
            logger.warning(f"下载失败 (尝试 {retry+1}/{max_retries}): {e}")

            # 删除临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)

            # 如果不是最后一次重试，等待一段时间后重试
            if retry < max_retries - 1:
                wait_time = 2 ** retry  # 指数退避
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)

    # 所有重试都失败
    logger.error(f"下载失败，已达到最大重试次数: {max_retries}")
    return False

def download_file_with_urllib(url, destination, max_retries=5, timeout=30, chunk_size=8192):
    """
    使用urllib下载文件并显示进度条，支持重试

    Args:
        url: 下载URL
        destination: 目标文件路径
        max_retries: 最大重试次数
        timeout: 超时时间（秒）
        chunk_size: 每次读取的块大小

    Returns:
        bool: 下载是否成功
    """
    # 创建目标目录
    os.makedirs(os.path.dirname(destination), exist_ok=True)

    # 如果文件已存在，直接返回成功
    if os.path.exists(destination):
        logger.info(f"文件已存在: {destination}")
        return True

    # 临时文件路径
    temp_file = f"{destination}.tmp"

    for retry in range(max_retries):
        try:
            logger.info(f"下载文件 (尝试 {retry+1}/{max_retries}): {url} -> {destination}")

            # 创建SSL上下文
            ssl_context = ssl.create_default_context(cafile=certifi.where())

            # 打开URL
            with urllib.request.urlopen(url, timeout=timeout, context=ssl_context) as response:
                # 获取文件大小
                total_size = int(response.info().get('Content-Length', 0))

                # 创建进度条
                progress_bar = tqdm(total=total_size, unit='B', unit_scale=True, desc=f"下载中", leave=True, bar_format='{l_bar}{bar}{r_bar}')

                # 下载文件
                with open(temp_file, 'wb') as f:
                    while True:
                        chunk = response.read(chunk_size)
                        if not chunk:
                            break
                        f.write(chunk)
                        progress_bar.update(len(chunk))

                progress_bar.close()

            # 重命名临时文件
            os.rename(temp_file, destination)
            logger.info(f"下载成功: {destination}")
            return True

        except (urllib.error.URLError, IOError, ssl.SSLError) as e:
            logger.warning(f"下载失败 (尝试 {retry+1}/{max_retries}): {e}")

            # 删除临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)

            # 如果不是最后一次重试，等待一段时间后重试
            if retry < max_retries - 1:
                wait_time = 2 ** retry  # 指数退避
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)

    # 所有重试都失败
    logger.error(f"下载失败，已达到最大重试次数: {max_retries}")
    return False

def download_file(url, destination, max_retries=5, timeout=30):
    """
    尝试使用多种方法下载文件

    Args:
        url: 下载URL
        destination: 目标文件路径
        max_retries: 最大重试次数
        timeout: 超时时间（秒）

    Returns:
        bool: 下载是否成功
    """
    # 首先尝试使用requests
    success = download_file_with_progress(url, destination, max_retries, timeout)
    if success:
        return True

    # 如果requests失败，尝试使用urllib
    logger.info("使用requests下载失败，尝试使用urllib...")
    return download_file_with_urllib(url, destination, max_retries, timeout)
