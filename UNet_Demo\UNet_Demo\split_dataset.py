"""
按照8:2的比例重新划分数据集
"""
import os
import glob
import random
import argparse
from pathlib import Path

def get_all_annotated_files(segmentation_dir):
    """
    获取所有有标注的文件
    
    参数:
    - segmentation_dir: 分割标注目录
    
    返回:
    - 有标注的文件名列表（不含扩展名）
    """
    # 获取所有分割标注文件
    seg_files = glob.glob(os.path.join(segmentation_dir, "*.png"))
    
    # 提取文件名（不含扩展名）
    file_names = [os.path.splitext(os.path.basename(f))[0] for f in seg_files]
    
    return file_names

def split_dataset(file_names, train_ratio=0.8, seed=42):
    """
    划分数据集
    
    参数:
    - file_names: 文件名列表
    - train_ratio: 训练集比例
    - seed: 随机种子
    
    返回:
    - train_files: 训练集文件名列表
    - val_files: 验证集文件名列表
    """
    # 设置随机种子
    random.seed(seed)
    
    # 随机打乱文件列表
    random.shuffle(file_names)
    
    # 计算训练集大小
    train_size = int(len(file_names) * train_ratio)
    
    # 划分训练集和验证集
    train_files = file_names[:train_size]
    val_files = file_names[train_size:]
    
    return train_files, val_files

def save_file_list(file_list, output_path):
    """
    保存文件列表
    
    参数:
    - file_list: 文件名列表
    - output_path: 输出文件路径
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        for file_name in file_list:
            f.write(f"{file_name}\n")

def main():
    parser = argparse.ArgumentParser(description="按照8:2的比例重新划分数据集")
    parser.add_argument('--root_dir', type=str, default='VOCdevkit', help='数据集根目录')
    parser.add_argument('--seg_dir', type=str, default='VOC2025/SegmentationClass', help='分割标注目录')
    parser.add_argument('--output_dir', type=str, default='VOC2025/ImageSets/Segmentation', help='输出目录')
    parser.add_argument('--train_ratio', type=float, default=0.8, help='训练集比例')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    args = parser.parse_args()
    
    # 构建完整路径
    seg_dir = os.path.join(args.root_dir, args.seg_dir)
    output_dir = os.path.join(args.root_dir, args.output_dir)
    
    # 获取所有有标注的文件
    print(f"从 {seg_dir} 获取标注文件...")
    file_names = get_all_annotated_files(seg_dir)
    print(f"找到 {len(file_names)} 个标注文件")
    
    # 划分数据集
    train_files, val_files = split_dataset(file_names, args.train_ratio, args.seed)
    print(f"划分为 {len(train_files)} 个训练样本和 {len(val_files)} 个验证样本")
    
    # 保存文件列表
    train_path = os.path.join(output_dir, 'train.txt')
    val_path = os.path.join(output_dir, 'val.txt')
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    save_file_list(train_files, train_path)
    save_file_list(val_files, val_path)
    
    print(f"训练集文件列表已保存到: {train_path}")
    print(f"验证集文件列表已保存到: {val_path}")

if __name__ == '__main__':
    main()
