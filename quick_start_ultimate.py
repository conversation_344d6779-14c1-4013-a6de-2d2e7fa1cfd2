#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import time
from datetime import datetime

def check_environment():
    """检查环境和依赖"""
    print("🔍 检查环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or python_version.minor < 7:
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}")
    
    # 检查关键依赖
    required_packages = ['torch', 'numpy', 'PIL', 'yaml']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: 未安装")
    
    if missing_packages:
        print(f"请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA: 可用 (设备数: {torch.cuda.device_count()})")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
        else:
            print("⚠️  CUDA: 不可用，将使用CPU训练")
    except:
        print("❌ PyTorch未正确安装")
        return False
    
    return True

def check_data():
    """检查数据集"""
    print("\n📊 检查数据集...")
    
    voc_path = "UNet_Demo/UNet_Demo/VOCdevkit"
    
    if not os.path.exists(voc_path):
        print(f"❌ 数据集路径不存在: {voc_path}")
        return False
    
    # 检查关键文件
    required_files = [
        "VOC2025/ImageSets/Segmentation/train.txt",
        "VOC2025/ImageSets/Segmentation/val.txt",
        "VOC2025/JPEGImages",
        "VOC2025/SegmentationClass"
    ]
    
    for file_path in required_files:
        full_path = os.path.join(voc_path, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path}: 存在")
        else:
            print(f"❌ {file_path}: 不存在")
            return False
    
    # 统计数据量
    try:
        train_file = os.path.join(voc_path, "VOC2025/ImageSets/Segmentation/train.txt")
        val_file = os.path.join(voc_path, "VOC2025/ImageSets/Segmentation/val.txt")
        
        with open(train_file, 'r') as f:
            train_count = len(f.readlines())
        with open(val_file, 'r') as f:
            val_count = len(f.readlines())
            
        print(f"📈 训练集样本: {train_count}")
        print(f"📈 验证集样本: {val_count}")
        
        if train_count < 10 or val_count < 5:
            print("⚠️  数据集样本数量较少，可能影响训练效果")
            
    except Exception as e:
        print(f"❌ 读取数据集信息失败: {e}")
        return False
    
    return True

def show_strategy_summary():
    """显示策略总结"""
    print("\n🎯 终极优化策略总结")
    print("=" * 60)
    
    strategies = [
        ("🔄 多数据集权重迁移", "基于ADE20K等4个数据集的权重混合，标签匹配率82%"),
        ("⚖️ 极端类别权重平衡", "困难类别权重50-100倍，优势类别权重0.01倍"),
        ("🎯 Focal Loss组合", "Focal + Dice + CE + Lovász多重损失优化"),
        ("🧠 强化模型架构", "ResNet50 + 注意力机制 + 多尺度特征融合"),
        ("📊 数据质量优化", "分层数据集划分 + 类别感知采样"),
        ("🔧 训练策略优化", "余弦退火学习率 + 梯度裁剪 + 300轮训练"),
        ("💪 强化数据增强", "针对稀有类别的专门增强策略")
    ]
    
    for i, (title, desc) in enumerate(strategies, 1):
        print(f"{i}. {title}")
        print(f"   {desc}")
    
    print("=" * 60)
    print("🎯 预期效果: mIoU从0.34提升到0.45-0.50 (30-40%改善)")

def create_backup():
    """创建配置备份"""
    print("\n💾 创建配置备份...")
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_dir = f"backups/ultimate_optimization_{timestamp}"
    
    os.makedirs(backup_dir, exist_ok=True)
    
    # 备份关键文件
    files_to_backup = [
        "config_ultimate_optimization.yaml",
        "start_ultimate_training.py",
        "train_ultimate_optimization_v2.py"
    ]
    
    for file_name in files_to_backup:
        if os.path.exists(file_name):
            import shutil
            shutil.copy2(file_name, backup_dir)
            print(f"✅ 备份: {file_name}")
    
    print(f"📁 备份保存在: {backup_dir}")
    return backup_dir

def start_training():
    """启动训练"""
    print("\n🚀 启动终极优化训练...")
    
    try:
        # 运行训练脚本
        result = subprocess.run([
            sys.executable, "start_ultimate_training.py"
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            print("✅ 训练启动成功")
        else:
            print(f"❌ 训练启动失败，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"❌ 启动训练时出错: {e}")
        print("\n🔧 手动启动命令:")
        print("python start_ultimate_training.py")

def main():
    """主函数"""
    print("🎯 终极优化训练快速启动器")
    print("=" * 50)
    print("整合多数据集权重迁移 + 极端权重平衡 + 强化架构")
    print("=" * 50)
    
    # 1. 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return
    
    # 2. 检查数据
    if not check_data():
        print("\n❌ 数据集检查失败，请确保数据集正确配置")
        return
    
    # 3. 显示策略总结
    show_strategy_summary()
    
    # 4. 创建备份
    backup_dir = create_backup()
    
    # 5. 确认启动
    print("\n" + "=" * 50)
    print("🎯 准备就绪！")
    print("=" * 50)
    
    response = input("是否立即开始训练？(y/n): ").lower().strip()
    
    if response in ['y', 'yes', '是', '1']:
        start_training()
    else:
        print("\n📋 手动启动步骤:")
        print("1. python start_ultimate_training.py")
        print("2. 监控训练日志")
        print("3. 等待最佳模型保存")
        
        print(f"\n📁 配置文件位置:")
        print(f"  - 主配置: config_ultimate_optimization.yaml")
        print(f"  - 训练脚本: start_ultimate_training.py")
        print(f"  - 备份目录: {backup_dir}")
        
        print(f"\n🎯 预期结果:")
        print(f"  - 短期目标: mIoU > 0.45 (1-2周)")
        print(f"  - 中期目标: mIoU > 0.55 (2-4周)")
        print(f"  - 长期目标: mIoU > 0.70 (1-2月)")

if __name__ == "__main__":
    main()
