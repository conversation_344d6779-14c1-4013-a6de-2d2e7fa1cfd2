"""
类别平衡采样器
"""
import os
import numpy as np
import cv2
import torch
from torch.utils.data.sampler import Sampler
from collections import defaultdict

class ClassBalancedSampler(Sampler):
    """
    类别平衡采样器

    对于每个批次，尝试包含所有类别的样本，以平衡不同类别的训练
    """
    def __init__(self, dataset, root_dir, num_classes, batch_size=8, oversample_rare=True):
        """
        初始化类别平衡采样器

        参数:
        - dataset: 数据集实例
        - root_dir: 数据集根目录
        - num_classes: 类别数量
        - batch_size: 批量大小
        - oversample_rare: 是否过采样稀有类别
        """
        self.dataset = dataset
        self.root_dir = root_dir
        self.num_classes = num_classes
        self.batch_size = batch_size
        self.oversample_rare = oversample_rare

        # 分析每个样本包含的类别
        self.sample_class_map = self._analyze_samples()

        # 按类别组织样本索引
        self.class_to_samples = defaultdict(list)
        for idx, classes in enumerate(self.sample_class_map):
            for c in classes:
                self.class_to_samples[c].append(idx)

        # 计算每个类别的样本数量
        self.class_counts = {c: len(samples) for c, samples in self.class_to_samples.items()}

        # 计算采样权重
        if self.oversample_rare:
            # 过采样稀有类别
            max_count = max(self.class_counts.values())
            self.class_weights = {c: max_count / count for c, count in self.class_counts.items()}
        else:
            # 均匀采样
            self.class_weights = {c: 1.0 for c in self.class_counts}

        # 计算总批次数
        self.num_batches = len(dataset) // batch_size
        if len(dataset) % batch_size > 0:
            self.num_batches += 1

    def _analyze_samples(self):
        """
        分析每个样本包含的类别

        返回:
        - sample_class_map: 每个样本包含的类别列表
        """
        sample_class_map = []

        for idx in range(len(self.dataset)):
            # 获取样本的掩码路径
            try:
                # 尝试从数据集获取文件名
                if hasattr(self.dataset, 'file_list'):
                    name = self.dataset.file_list[idx].strip()
                    # 处理可能的文件扩展名
                    name = os.path.splitext(name)[0]
                    mask_path = os.path.join(self.root_dir, 'VOC2025', 'SegmentationClass', name + '.png')
                else:
                    # 如果数据集没有file_list属性，尝试其他方法获取掩码路径
                    img_path = self.dataset.imgs[idx] if hasattr(self.dataset, 'imgs') else None
                    if img_path:
                        # 从图像路径推断掩码路径
                        img_name = os.path.basename(img_path)
                        img_name = os.path.splitext(img_name)[0]
                        mask_path = os.path.join(self.root_dir, 'VOC2025', 'SegmentationClass', img_name + '.png')
                    else:
                        # 无法获取掩码路径，使用默认值
                        mask_path = None
            except (AttributeError, IndexError) as e:
                print(f"Warning: Error getting mask path for index {idx}: {e}")
                mask_path = None

            # 读取掩码
            if mask_path is not None and os.path.exists(mask_path):
                mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
                if mask is not None:
                    # 获取样本中存在的类别
                    unique_classes = np.unique(mask)
                    # 过滤掉忽略索引
                    unique_classes = [c for c in unique_classes if c < self.num_classes]
                    sample_class_map.append(unique_classes)
                else:
                    # 如果掩码读取失败，假设包含所有类别
                    sample_class_map.append(list(range(self.num_classes)))
            else:
                # 如果掩码不存在，假设包含所有类别
                sample_class_map.append(list(range(self.num_classes)))

        return sample_class_map

    def __iter__(self):
        """
        生成采样序列
        """
        # 为每个批次选择样本
        all_indices = []

        for _ in range(self.num_batches):
            batch_indices = []

            # 确保每个批次包含尽可能多的类别
            remaining_classes = list(range(self.num_classes))
            np.random.shuffle(remaining_classes)

            # 首先为每个类别选择一个样本
            for c in remaining_classes[:min(self.batch_size, len(remaining_classes))]:
                if c in self.class_to_samples and self.class_to_samples[c]:
                    # 从该类别中随机选择一个样本
                    sample_idx = np.random.choice(self.class_to_samples[c])
                    batch_indices.append(sample_idx)

            # 如果批次还未满，随机选择样本填充
            while len(batch_indices) < self.batch_size:
                # 按类别权重随机选择一个类别
                class_probs = np.array([self.class_weights.get(c, 0) for c in range(self.num_classes)])
                class_probs = class_probs / class_probs.sum()
                c = np.random.choice(self.num_classes, p=class_probs)

                if c in self.class_to_samples and self.class_to_samples[c]:
                    # 从该类别中随机选择一个样本
                    sample_idx = np.random.choice(self.class_to_samples[c])
                    batch_indices.append(sample_idx)
                else:
                    # 如果该类别没有样本，随机选择一个样本
                    sample_idx = np.random.randint(len(self.dataset))
                    batch_indices.append(sample_idx)

            all_indices.extend(batch_indices)

        return iter(all_indices)

    def __len__(self):
        """
        返回采样器的长度
        """
        return self.num_batches * self.batch_size
