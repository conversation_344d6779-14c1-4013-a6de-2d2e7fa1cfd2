# 终极优化配置文件
# 整合所有成功策略：多数据集权重迁移 + 极端权重平衡 + 强化架构

# 基础配置
experiment_name: "ultimate_optimization_v1"
description: "整合多数据集权重迁移、极端类别权重、强化架构的终极优化方案"

# 数据配置
data:
  dataset_path: "UNet_Demo/UNet_Demo/VOCdevkit"
  num_classes: 29
  input_shape: [512, 512]
  
  # 数据集重划分策略
  stratified_split:
    enabled: true
    validation_split: 0.2
    random_state: 42
    ensure_class_balance: true
  
  # 类别名称（用于标签匹配）
  class_names:
    - "background"
    - "aeroplane"
    - "bicycle" 
    - "bird"
    - "boat"
    - "bottle"
    - "bus"
    - "car"
    - "cat"
    - "chair"
    - "cow"
    - "diningtable"
    - "dog"
    - "horse"
    - "motorbike"
    - "person"
    - "pottedplant"
    - "sheep"
    - "sofa"
    - "train"
    - "tvmonitor"
    - "wall"
    - "building"
    - "sky"
    - "floor"
    - "tree"
    - "ceiling"
    - "road"
    - "bed"

# 多预训练数据集权重迁移配置
multi_pretrain:
  enabled: true
  
  # 源数据集配置
  source_datasets:
    - name: "ade20k"
      weight: 0.7  # 最高相似度权重
      similarity_score: 0.82
    - name: "cityscapes" 
      weight: 0.1
      similarity_score: 0.45
    - name: "coco_stuff"
      weight: 0.1
      similarity_score: 0.38
    - name: "pascal_voc"
      weight: 0.1
      similarity_score: 0.65
  
  # 权重混合策略
  mixing_strategy: "similarity_weighted"
  
  # 标签匹配配置
  label_matching:
    strategy: "semantic_similarity"
    match_threshold: 0.6
    use_word_embeddings: true
    
  # 权重保存配置
  save_mixed_weights: true
  mixed_weights_path: "pretrained_weights/ultimate_mixed_weights.pth"

# 模型架构配置
model:
  # 基础架构
  architecture: "UNet"  # 先用UNet稳定训练，后续可升级到UnetPlusPlus
  backbone: "resnet50"  # 先用resnet50，稳定后升级到efficientnet-b7
  
  # 预训练权重
  pretrained: true
  encoder_weights: "imagenet"
  
  # 模型增强
  dropout_rate: 0.3
  use_attention: true
  attention_type: "cbam"
  
  # 多尺度特征融合
  multi_scale_fusion:
    enabled: true
    scales: [0.5, 1.0, 1.5]
    fusion_method: "attention_weighted"

# 极端类别权重平衡策略
class_balancing:
  # 极端类别权重配置
  extreme_weights:
    # 完全未学习的类别 - 极高权重 (50-100倍)
    8: 100.0   # cat
    10: 90.0   # cow  
    18: 80.0   # sofa
    23: 70.0   # sky
    26: 60.0   # ceiling
    27: 50.0   # road
    28: 50.0   # bed
    
    # 中等表现类别 - 中等权重 (5-20倍)
    5: 15.0    # bottle
    7: 12.0    # car
    9: 10.0    # chair
    11: 8.0    # diningtable
    15: 6.0    # person
    17: 5.0    # sheep
    19: 20.0   # train
    22: 15.0   # building
    25: 10.0   # tree
    
    # 优秀表现类别 - 极低权重 (0.01-0.1倍)
    0: 0.01    # background
    3: 0.01    # bird
    13: 0.01   # horse
    2: 0.05    # bicycle
    21: 0.05   # wall
    
    # 其他类别 - 低权重 (0.3-0.8倍)
    1: 0.5     # aeroplane
    4: 0.8     # boat
    6: 0.6     # bus
    12: 0.4    # dog
    14: 0.3    # motorbike
    16: 0.7    # pottedplant
    20: 0.6    # tvmonitor
    24: 0.5    # floor
  
  # 困难类别专门处理
  difficult_class_focus:
    enabled: true
    target_classes: [8, 10, 18, 23, 26, 27, 28]
    dedicated_loss_weight: 5.0
    focal_gamma_boost: 1.0
    
  # 类别感知采样
  class_aware_sampling:
    enabled: true
    oversample_ratio: 15.0
    min_samples_per_class: 1

# 损失函数配置
loss:
  # 组合损失权重
  combination_weights:
    focal_loss: 0.4
    dice_loss: 0.3
    ce_loss: 0.2
    lovasz_loss: 0.1
  
  # Focal Loss参数
  focal_loss:
    alpha: 0.25
    gamma: 2.0
    difficult_class_gamma_boost: 1.0
  
  # Dice Loss参数
  dice_loss:
    smooth: 1e-8
    
  # 标签平滑
  label_smoothing: 0.1

# 训练配置
training:
  # 基础参数
  epochs: 300
  batch_size: 8
  learning_rate: 2e-4
  weight_decay: 1e-4
  
  # 学习率调度
  scheduler:
    type: "cosine_warmup"
    warmup_epochs: 10
    min_lr: 1e-6
    
  # 优化器
  optimizer:
    type: "AdamW"
    betas: [0.9, 0.999]
    eps: 1e-8
    
  # 正则化
  gradient_clipping:
    enabled: true
    max_norm: 1.0
    
  # 早停
  early_stopping:
    patience: 50
    monitor: "val_miou"
    mode: "max"

# 数据增强配置
augmentation:
  # 基础增强
  basic:
    horizontal_flip: 0.5
    vertical_flip: 0.3
    random_rotate90: 0.5
    rotation_limit: 30
    rotation_prob: 0.7
    
  # 颜色增强
  color:
    brightness_contrast:
      brightness_limit: 0.3
      contrast_limit: 0.3
      prob: 0.8
    hue_saturation:
      hue_shift_limit: 20
      sat_shift_limit: 30
      val_shift_limit: 20
      prob: 0.7
      
  # 噪声和模糊
  noise:
    gaussian_blur:
      blur_limit: [3, 7]
      prob: 0.3
    gaussian_noise:
      var_limit: [10, 50]
      prob: 0.3
      
  # 强化增强（针对稀有类别）
  strong:
    enabled: true
    elastic_transform:
      alpha: 1
      sigma: 50
      alpha_affine: 50
      prob: 0.5
    grid_distortion:
      num_steps: 5
      distort_limit: 0.3
      prob: 0.5
    coarse_dropout:
      max_holes: 8
      max_height: 32
      max_width: 32
      prob: 0.3

# 多尺度训练配置
multi_scale_training:
  enabled: true
  input_sizes: [384, 512, 640]
  scale_probability: 0.3

# 日志和保存配置
logging:
  save_frequency: 20
  log_level: "INFO"
  tensorboard: true
  
  # 模型保存
  save_best_only: true
  save_weights_only: false
  
  # 监控指标
  monitor_metrics:
    - "train_loss"
    - "val_loss" 
    - "val_miou"
    - "val_fscore"
    - "learning_rate"

# 评估配置
evaluation:
  metrics:
    - "miou"
    - "fscore"
    - "pixel_accuracy"
    - "class_accuracy"
  
  # 类别特定评估
  per_class_evaluation: true
  difficult_class_focus: [8, 10, 18, 23, 26, 27, 28]

# 预期目标
targets:
  short_term:
    miou: 0.45
    timeline: "1-2 weeks"
    strategies: ["extreme_weights", "focal_loss"]
    
  medium_term:
    miou: 0.55
    timeline: "2-4 weeks"
    strategies: ["model_ensemble", "data_optimization"]
    
  long_term:
    miou: 0.70
    timeline: "1-2 months"
    strategies: ["architecture_innovation", "knowledge_distillation"]
