import torch
import torch.nn.functional as F
import numpy as np
import cv2
from scipy import ndimage
import logging

# 可选依赖
try:
    from skimage import morphology, measure
    from skimage.segmentation import watershed
    SKIMAGE_AVAILABLE = True
except ImportError:
    SKIMAGE_AVAILABLE = False

try:
    from skimage.feature import peak_local_maxima
    PEAK_DETECTION_AVAILABLE = True
except ImportError:
    PEAK_DETECTION_AVAILABLE = False

logger = logging.getLogger(__name__)

class AdvancedPostProcessor:
    """高级后处理器 - 通过多种后处理技术提升mIoU"""

    def __init__(self, num_classes=29, device='cuda'):
        self.num_classes = num_classes
        self.device = device

        # 后处理配置
        self.config = {
            'use_tta': True,           # 测试时增强
            'use_crf': False,          # 条件随机场 (需要额外依赖)
            'use_morphology': True,    # 形态学操作
            'use_watershed': True,     # 分水岭算法
            'use_connected_components': True,  # 连通组件分析
            'use_confidence_filtering': True,  # 置信度过滤
            'use_boundary_refinement': True,   # 边界细化
            'use_multi_scale_fusion': True,    # 多尺度融合
        }

        # 形态学操作参数
        self.morph_params = {
            'opening_kernel': 3,
            'closing_kernel': 5,
            'erosion_kernel': 2,
            'dilation_kernel': 3,
        }

        # 连通组件参数
        self.cc_params = {
            'min_size': 50,      # 最小连通组件大小
            'max_size': 50000,   # 最大连通组件大小
        }

        # 置信度过滤参数
        self.conf_params = {
            'min_confidence': 0.3,  # 最小置信度阈值
            'adaptive_threshold': True,  # 自适应阈值
        }

    def process_prediction(self, logits, original_size=None, apply_all=True):
        """
        对模型预测结果进行后处理

        Args:
            logits: 模型输出 [B, C, H, W] 或 [C, H, W]
            original_size: 原始图像尺寸 (H, W)
            apply_all: 是否应用所有后处理技术

        Returns:
            processed_pred: 后处理后的预测结果
            confidence_map: 置信度图
        """
        if len(logits.shape) == 4:
            logits = logits[0]  # 移除batch维度

        # 1. 基础softmax处理
        probs = F.softmax(logits, dim=0)  # [C, H, W]

        # 2. 置信度过滤
        if self.config['use_confidence_filtering']:
            probs, confidence_map = self._confidence_filtering(probs)
        else:
            confidence_map = torch.max(probs, dim=0)[0]

        # 3. 多尺度融合
        if self.config['use_multi_scale_fusion']:
            probs = self._multi_scale_fusion(probs)

        # 4. 获取初始预测
        pred = torch.argmax(probs, dim=0).cpu().numpy()

        # 5. 形态学操作
        if self.config['use_morphology']:
            pred = self._morphological_operations(pred)

        # 6. 连通组件分析
        if self.config['use_connected_components']:
            pred = self._connected_components_analysis(pred)

        # 7. 分水岭算法
        if self.config['use_watershed']:
            pred = self._watershed_segmentation(pred, probs.cpu().numpy())

        # 8. 边界细化
        if self.config['use_boundary_refinement']:
            pred = self._boundary_refinement(pred, probs.cpu().numpy())

        # 9. 调整到原始尺寸
        if original_size is not None:
            pred = cv2.resize(pred.astype(np.uint8),
                            (original_size[1], original_size[0]),
                            interpolation=cv2.INTER_NEAREST)

        return pred, confidence_map.cpu().numpy()

    def _confidence_filtering(self, probs):
        """置信度过滤"""
        confidence_map = torch.max(probs, dim=0)[0]

        if self.conf_params['adaptive_threshold']:
            # 自适应阈值
            threshold = torch.quantile(confidence_map, 0.3)
            threshold = max(threshold.item(), self.conf_params['min_confidence'])
        else:
            threshold = self.conf_params['min_confidence']

        # 低置信度区域设为背景类
        low_conf_mask = confidence_map < threshold
        probs_filtered = probs.clone()
        probs_filtered[:, low_conf_mask] = 0
        probs_filtered[0, low_conf_mask] = 1  # 设为背景类

        logger.info(f"置信度过滤: 阈值={threshold:.3f}, 过滤像素={low_conf_mask.sum().item()}")

        return probs_filtered, confidence_map

    def _multi_scale_fusion(self, probs):
        """多尺度融合"""
        scales = [0.75, 1.0, 1.25]
        fused_probs = torch.zeros_like(probs)

        for scale in scales:
            if scale != 1.0:
                h, w = probs.shape[1], probs.shape[2]
                new_h, new_w = int(h * scale), int(w * scale)

                # 缩放
                scaled_probs = F.interpolate(
                    probs.unsqueeze(0),
                    size=(new_h, new_w),
                    mode='bilinear',
                    align_corners=False
                )[0]

                # 恢复原尺寸
                scaled_probs = F.interpolate(
                    scaled_probs.unsqueeze(0),
                    size=(h, w),
                    mode='bilinear',
                    align_corners=False
                )[0]
            else:
                scaled_probs = probs

            fused_probs += scaled_probs / len(scales)

        return fused_probs

    def _morphological_operations(self, pred):
        """形态学操作"""
        processed_pred = pred.copy()

        # 对每个类别分别处理
        for class_id in range(1, self.num_classes):  # 跳过背景类
            class_mask = (pred == class_id).astype(np.uint8)

            if class_mask.sum() == 0:
                continue

            # 开运算 - 去除小噪点
            kernel_open = cv2.getStructuringElement(
                cv2.MORPH_ELLIPSE,
                (self.morph_params['opening_kernel'], self.morph_params['opening_kernel'])
            )
            class_mask = cv2.morphologyEx(class_mask, cv2.MORPH_OPEN, kernel_open)

            # 闭运算 - 填充小洞
            kernel_close = cv2.getStructuringElement(
                cv2.MORPH_ELLIPSE,
                (self.morph_params['closing_kernel'], self.morph_params['closing_kernel'])
            )
            class_mask = cv2.morphologyEx(class_mask, cv2.MORPH_CLOSE, kernel_close)

            # 更新预测结果
            processed_pred[class_mask == 1] = class_id
            processed_pred[(pred == class_id) & (class_mask == 0)] = 0  # 被移除的区域设为背景

        return processed_pred

    def _connected_components_analysis(self, pred):
        """连通组件分析"""
        processed_pred = pred.copy()

        for class_id in range(1, self.num_classes):
            class_mask = (pred == class_id).astype(np.uint8)

            if class_mask.sum() == 0:
                continue

            # 连通组件标记
            num_labels, labels = cv2.connectedComponents(class_mask)

            for label_id in range(1, num_labels):
                component_mask = (labels == label_id)
                component_size = component_mask.sum()

                # 移除过小或过大的连通组件
                if (component_size < self.cc_params['min_size'] or
                    component_size > self.cc_params['max_size']):
                    processed_pred[component_mask] = 0  # 设为背景

        return processed_pred

    def _watershed_segmentation(self, pred, probs):
        """分水岭算法分割"""
        if not SKIMAGE_AVAILABLE or not PEAK_DETECTION_AVAILABLE:
            logger.warning("Skimage不可用，跳过分水岭算法")
            return pred

        processed_pred = pred.copy()

        # 对每个类别应用分水岭
        for class_id in range(1, self.num_classes):
            class_mask = (pred == class_id).astype(np.uint8)

            if class_mask.sum() < 100:  # 跳过太小的区域
                continue

            try:
                # 使用概率图作为距离变换
                class_prob = probs[class_id]

                # 距离变换
                distance = ndimage.distance_transform_edt(class_mask)

                # 寻找局部最大值作为种子点
                local_maxima = peak_local_maxima(distance, min_distance=10, threshold_abs=0.3)

                if len(local_maxima[0]) > 1:  # 只有多个种子点时才应用分水岭
                    markers = np.zeros_like(distance, dtype=np.int32)
                    for i, (y, x) in enumerate(zip(local_maxima[0], local_maxima[1])):
                        markers[y, x] = i + 1

                    # 应用分水岭
                    labels = watershed(-distance, markers, mask=class_mask)

                    # 更新预测结果 (保持原类别标签)
                    processed_pred[class_mask == 1] = class_id
            except Exception as e:
                logger.warning(f"分水岭算法处理类别{class_id}失败: {e}")
                continue

        return processed_pred

    def _boundary_refinement(self, pred, probs):
        """边界细化"""
        processed_pred = pred.copy()

        # 检测边界像素
        kernel = np.ones((3, 3), np.uint8)

        for class_id in range(1, self.num_classes):
            class_mask = (pred == class_id).astype(np.uint8)

            if class_mask.sum() == 0:
                continue

            # 边界检测
            eroded = cv2.erode(class_mask, kernel, iterations=1)
            boundary = class_mask - eroded

            if boundary.sum() == 0:
                continue

            # 在边界区域重新分类
            boundary_coords = np.where(boundary == 1)

            for y, x in zip(boundary_coords[0], boundary_coords[1]):
                # 获取邻域内的概率分布
                y_start, y_end = max(0, y-1), min(probs.shape[1], y+2)
                x_start, x_end = max(0, x-1), min(probs.shape[2], x+2)

                neighborhood_probs = probs[:, y_start:y_end, x_start:x_end]
                avg_probs = neighborhood_probs.mean(dim=(1, 2))

                # 重新分类
                new_class = torch.argmax(avg_probs).item()
                processed_pred[y, x] = new_class

        return processed_pred

    def test_time_augmentation(self, model, image_tensor, input_shape=(512, 512)):
        """测试时增强"""
        augmentations = [
            lambda x: x,  # 原图
            lambda x: torch.flip(x, dims=[3]),  # 水平翻转
            lambda x: torch.flip(x, dims=[2]),  # 垂直翻转
            lambda x: torch.flip(torch.flip(x, dims=[3]), dims=[2]),  # 水平+垂直翻转
        ]

        # 对应的逆变换
        inverse_augmentations = [
            lambda x: x,  # 原图
            lambda x: torch.flip(x, dims=[3]),  # 水平翻转
            lambda x: torch.flip(x, dims=[2]),  # 垂直翻转
            lambda x: torch.flip(torch.flip(x, dims=[3]), dims=[2]),  # 水平+垂直翻转
        ]

        predictions = []

        with torch.no_grad():
            for aug, inv_aug in zip(augmentations, inverse_augmentations):
                # 应用增强
                aug_image = aug(image_tensor)

                # 预测
                output = model(aug_image)

                # 应用逆变换
                output = inv_aug(output)

                predictions.append(output)

        # 平均所有预测
        ensemble_output = torch.stack(predictions).mean(dim=0)

        return ensemble_output