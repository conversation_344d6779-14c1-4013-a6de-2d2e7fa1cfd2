"""
DeepLabV3+ 模型实现 - 立即可行的改进版本
支持多预训练权重混合的高性能语义分割模型
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from typing import Optional, List

class ASPP(nn.Module):
    """Atrous Spatial Pyramid Pooling"""
    def __init__(self, in_channels: int, out_channels: int = 256, rates: List[int] = [6, 12, 18]):
        super(ASPP, self).__init__()

        # 1x1 卷积
        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

        # 空洞卷积分支
        self.atrous_convs = nn.ModuleList()
        for rate in rates:
            self.atrous_convs.append(nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 3, padding=rate, dilation=rate, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ))

        # 全局平均池化分支
        self.global_avg_pool = nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Conv2d(in_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

        # 融合卷积
        self.project = nn.Sequential(
            nn.Conv2d(out_channels * (2 + len(rates)), out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5)
        )

    def forward(self, x):
        size = x.shape[-2:]

        # 1x1 卷积
        feat1 = self.conv1(x)

        # 空洞卷积
        atrous_feats = [conv(x) for conv in self.atrous_convs]

        # 全局平均池化
        global_feat = self.global_avg_pool(x)
        global_feat = F.interpolate(global_feat, size=size, mode='bilinear', align_corners=False)

        # 特征融合
        features = [feat1] + atrous_feats + [global_feat]
        features = torch.cat(features, dim=1)

        return self.project(features)

class Decoder(nn.Module):
    """DeepLabV3+ 解码器"""
    def __init__(self, low_level_channels: int, num_classes: int, aspp_channels: int = 256):
        super(Decoder, self).__init__()

        # 低级特征处理
        self.low_level_conv = nn.Sequential(
            nn.Conv2d(low_level_channels, 48, 1, bias=False),
            nn.BatchNorm2d(48),
            nn.ReLU(inplace=True)
        )

        # 特征融合
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(aspp_channels + 48, 256, 3, padding=1, bias=False),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Conv2d(256, 256, 3, padding=1, bias=False),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1)
        )

        # 分类头
        self.classifier = nn.Conv2d(256, num_classes, 1)

    def forward(self, high_level_feat, low_level_feat):
        # 处理低级特征
        low_level_feat = self.low_level_conv(low_level_feat)

        # 上采样高级特征
        high_level_feat = F.interpolate(
            high_level_feat,
            size=low_level_feat.shape[-2:],
            mode='bilinear',
            align_corners=False
        )

        # 特征融合
        fused_feat = torch.cat([high_level_feat, low_level_feat], dim=1)
        fused_feat = self.fusion_conv(fused_feat)

        # 分类
        output = self.classifier(fused_feat)

        return output

class DeepLabV3Plus(nn.Module):
    """
    DeepLabV3+ 模型
    支持多预训练权重混合
    """
    def __init__(self,
                 num_classes: int = 29,
                 backbone: str = 'resnet101',
                 pretrained: bool = True,
                 output_stride: int = 16,
                 aspp_rates: List[int] = [6, 12, 18],
                 dropout_rate: float = 0.1):
        super(DeepLabV3Plus, self).__init__()

        self.num_classes = num_classes
        self.backbone_name = backbone

        # 构建骨干网络
        if backbone == 'resnet50':
            backbone_model = models.resnet50(pretrained=pretrained)
            low_level_channels = 256
            high_level_channels = 2048
        elif backbone == 'resnet101':
            backbone_model = models.resnet101(pretrained=pretrained)
            low_level_channels = 256
            high_level_channels = 2048
        elif backbone == 'resnet152':
            backbone_model = models.resnet152(pretrained=pretrained)
            low_level_channels = 256
            high_level_channels = 2048
        else:
            raise ValueError(f"Unsupported backbone: {backbone}")

        # 提取骨干网络层
        self.conv1 = backbone_model.conv1
        self.bn1 = backbone_model.bn1
        self.relu = backbone_model.relu
        self.maxpool = backbone_model.maxpool

        self.layer1 = backbone_model.layer1  # 低级特征
        self.layer2 = backbone_model.layer2
        self.layer3 = backbone_model.layer3
        self.layer4 = backbone_model.layer4  # 高级特征

        # 修改layer3和layer4的空洞卷积以控制output_stride
        if output_stride == 16:
            self._make_layer_dilated(self.layer4, dilation=2)
        elif output_stride == 8:
            self._make_layer_dilated(self.layer3, dilation=2)
            self._make_layer_dilated(self.layer4, dilation=4)

        # ASPP模块
        self.aspp = ASPP(high_level_channels, rates=aspp_rates)

        # 解码器
        self.decoder = Decoder(low_level_channels, num_classes)

        # Dropout
        self.dropout = nn.Dropout2d(dropout_rate)

        # 初始化权重
        self._init_weights()

    def _make_layer_dilated(self, layer, dilation):
        """修改层的空洞卷积"""
        for module in layer.modules():
            if isinstance(module, nn.Conv2d) and module.kernel_size[0] == 3:
                module.dilation = (dilation, dilation)
                module.padding = (dilation, dilation)

    def _init_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        input_size = x.shape[-2:]

        # 骨干网络前向传播
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        # 提取特征
        low_level_feat = self.layer1(x)  # 1/4 分辨率
        x = self.layer2(low_level_feat)
        x = self.layer3(x)
        high_level_feat = self.layer4(x)  # 1/16 或 1/8 分辨率

        # ASPP处理高级特征
        high_level_feat = self.aspp(high_level_feat)

        # 解码器
        output = self.decoder(high_level_feat, low_level_feat)

        # 上采样到输入尺寸
        output = F.interpolate(output, size=input_size, mode='bilinear', align_corners=False)

        # Dropout
        output = self.dropout(output)

        return output

    def get_backbone_params(self):
        """获取骨干网络参数"""
        backbone_params = []
        backbone_modules = [
            self.conv1, self.bn1, self.layer1,
            self.layer2, self.layer3, self.layer4
        ]
        for module in backbone_modules:
            backbone_params.extend(list(module.parameters()))
        return backbone_params

    def get_decoder_params(self):
        """获取解码器参数"""
        decoder_params = []
        decoder_modules = [self.aspp, self.decoder]
        for module in decoder_modules:
            decoder_params.extend(list(module.parameters()))
        return decoder_params

def deeplabv3plus(num_classes: int = 29,
                  backbone: str = 'resnet101',
                  pretrained: bool = True,
                  **kwargs):
    """
    创建DeepLabV3+模型

    Args:
        num_classes: 类别数量
        backbone: 骨干网络 ('resnet50', 'resnet101', 'resnet152')
        pretrained: 是否使用预训练权重
        **kwargs: 其他参数

    Returns:
        DeepLabV3Plus模型
    """
    return DeepLabV3Plus(
        num_classes=num_classes,
        backbone=backbone,
        pretrained=pretrained,
        **kwargs
    )

# 测试函数
if __name__ == "__main__":
    # 测试模型
    model = deeplabv3plus(num_classes=29, backbone='resnet101')

    # 计算参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # 测试前向传播
    x = torch.randn(2, 3, 512, 512)
    with torch.no_grad():
        output = model(x)
        print(f"Input shape: {x.shape}")
        print(f"Output shape: {output.shape}")
