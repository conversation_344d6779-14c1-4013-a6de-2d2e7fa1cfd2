"""
标签对齐工具函数
用于将我们的标签与ADE20K标签对齐，提高迁移学习效果
"""
import os
import torch
import torch.nn as nn
import logging
from copy import deepcopy
import numpy as np
import matplotlib.pyplot as plt
from utils.ade20k_labels import LABEL_MAPPING, ADE20K_CLASSES, OUR_CLASSES, REVERSE_MAPPING
from utils.pretrain_utils import get_pretrained_weights, load_pretrained_weights

logger = logging.getLogger(__name__)

def create_aligned_model(pretrained_model, num_classes=29, label_mapping=None):
    """
    创建一个标签对齐的模型
    
    Args:
        pretrained_model: ADE20K预训练模型
        num_classes: 目标数据集的类别数
        label_mapping: 从目标数据集类别到ADE20K类别的映射字典
        
    Returns:
        对齐后的模型
    """
    logger.info("创建标签对齐模型...")
    
    # 创建新模型
    new_model = deepcopy(pretrained_model)
    
    # 获取预训练模型的分割头权重
    if hasattr(pretrained_model, 'segmentation_head') and isinstance(pretrained_model.segmentation_head, nn.Sequential):
        pretrained_head = pretrained_model.segmentation_head[0]
        pretrained_weight = pretrained_head.weight.data  # 形状为 [150, channels, kernel_size, kernel_size]
        in_channels = pretrained_weight.shape[1]
        kernel_size = pretrained_weight.shape[2]
        
        logger.info(f"预训练分割头权重形状: {pretrained_weight.shape}")
        
        # 替换分割头
        new_model.segmentation_head = nn.Sequential(
            nn.Conv2d(in_channels, num_classes, kernel_size=kernel_size),
            nn.UpsamplingBilinear2d(scale_factor=pretrained_model.segmentation_head[1].scale_factor 
                                    if len(pretrained_model.segmentation_head) > 1 else 1)
        )
        
        # 初始化新的分割头
        if label_mapping:
            with torch.no_grad():
                # 首先随机初始化
                nn.init.kaiming_normal_(new_model.segmentation_head[0].weight)
                nn.init.constant_(new_model.segmentation_head[0].bias, 0)
                
                # 然后使用映射的权重
                aligned_count = 0
                for target_idx, source_idx in label_mapping.items():
                    if source_idx != -1:  # -1表示没有对应的ADE20K类别
                        new_model.segmentation_head[0].weight.data[target_idx] = pretrained_weight[source_idx]
                        aligned_count += 1
                
                logger.info(f"成功对齐 {aligned_count}/{num_classes} 个类别的权重")
    else:
        logger.warning("无法找到预训练模型的分割头，无法进行标签对齐")
    
    return new_model

def build_aligned_model(cfg, device):
    """
    构建带有标签对齐的模型
    
    Args:
        cfg: 配置字典
        device: 设备
        
    Returns:
        对齐后的模型
    """
    import segmentation_models_pytorch as smp
    
    logger.info("构建带有标签对齐的模型...")
    
    # 首先加载ADE20K预训练模型
    ade20k_model = smp.DeepLabV3Plus(
        encoder_name=cfg['model']['backbone'],
        encoder_weights="imagenet",
        in_channels=3,
        classes=150  # ADE20K有150个类别
    )
    
    # 加载ADE20K预训练权重
    weights_path = get_pretrained_weights(
        dataset="ADE20K",
        backbone=cfg['model']['backbone'],
        weights_dir="pretrained_weights"
    )
    
    if weights_path and os.path.exists(weights_path):
        logger.info(f"加载ADE20K预训练权重: {weights_path}")
        ade20k_model = load_pretrained_weights(
            model=ade20k_model,
            weights_path=weights_path,
            num_classes=150,  # ADE20K类别数
            strict=False
        )
    else:
        logger.warning("未找到ADE20K预训练权重，将使用ImageNet预训练权重")
    
    # 创建标签对齐的模型
    model = create_aligned_model(
        pretrained_model=ade20k_model,
        num_classes=cfg['data']['num_classes'],
        label_mapping=LABEL_MAPPING
    )
    
    # 添加注意力机制
    if cfg['model'].get('use_attention', False):
        logger.info("添加注意力机制...")
        # 这里需要根据您的模型架构添加注意力机制
        # 由于不同的模型架构可能有不同的注意力实现方式，这里只是一个示例
        pass
    
    # 添加Dropout
    if cfg['model'].get('dropout_rate', 0) > 0:
        dropout_rate = cfg['model']['dropout_rate']
        logger.info(f"添加Dropout，比率为{dropout_rate}...")
        # 这里需要根据您的模型架构添加Dropout
        # 由于不同的模型架构可能有不同的Dropout实现方式，这里只是一个示例
        pass
    
    return model.to(device)

def visualize_label_alignment(model, ade20k_model):
    """
    可视化标签对齐效果
    
    Args:
        model: 对齐后的模型
        ade20k_model: ADE20K预训练模型
    """
    # 获取权重
    aligned_weight = model.segmentation_head[0].weight.data
    pretrained_weight = ade20k_model.segmentation_head[0].weight.data
    
    # 计算权重相似度
    similarities = []
    for our_idx, ade_idx in LABEL_MAPPING.items():
        if ade_idx != -1:
            our_weight = aligned_weight[our_idx].flatten()
            ade_weight = pretrained_weight[ade_idx].flatten()
            similarity = torch.cosine_similarity(our_weight.unsqueeze(0), ade_weight.unsqueeze(0)).item()
            similarities.append((our_idx, ade_idx, similarity))
    
    # 按相似度排序
    similarities.sort(key=lambda x: x[2], reverse=True)
    
    # 绘制相似度图
    plt.figure(figsize=(12, 8))
    our_indices = [x[0] for x in similarities]
    ade_indices = [x[1] for x in similarities]
    sim_values = [x[2] for x in similarities]
    
    plt.bar(range(len(similarities)), sim_values)
    plt.xticks(range(len(similarities)), [f"{OUR_CLASSES[our_idx]}-{ADE20K_CLASSES[ade_idx]}" for our_idx, ade_idx, _ in similarities], rotation=90)
    plt.ylabel('Cosine Similarity')
    plt.title('Weight Similarity Between Aligned Classes')
    plt.tight_layout()
    plt.savefig('label_alignment_similarity.png')
    
    logger.info(f"标签对齐相似度可视化已保存到 label_alignment_similarity.png")
    
    # 打印相似度最高和最低的类别
    logger.info("相似度最高的类别:")
    for our_idx, ade_idx, sim in similarities[:5]:
        logger.info(f"{OUR_CLASSES[our_idx]} -> {ADE20K_CLASSES[ade_idx]}: {sim:.4f}")
    
    logger.info("相似度最低的类别:")
    for our_idx, ade_idx, sim in similarities[-5:]:
        logger.info(f"{OUR_CLASSES[our_idx]} -> {ADE20K_CLASSES[ade_idx]}: {sim:.4f}")

def print_alignment_stats():
    """打印标签对齐统计信息"""
    from utils.ade20k_labels import print_mapping_info
    print_mapping_info()
