#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
"""

import torch
import torch.nn as nn
import segmentation_models_pytorch as smp

print("🧪 快速测试开始...")

# 测试设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🚀 设备: {device}")

# 测试模型创建
try:
    print("🧠 测试模型创建...")
    model = smp.Unet(
        encoder_name='resnet18',
        encoder_weights='imagenet',
        in_channels=3,
        classes=29,
        activation=None,
    ).to(device)
    print("✅ 模型创建成功")
    
    # 测试前向传播
    print("🔄 测试前向传播...")
    dummy_input = torch.randn(1, 3, 256, 256).to(device)
    with torch.no_grad():
        output = model(dummy_input)
    print(f"✅ 输出形状: {output.shape}")
    
    # 测试损失计算
    print("📊 测试损失计算...")
    criterion = nn.CrossEntropyLoss()
    dummy_target = torch.randint(0, 29, (1, 256, 256)).to(device)
    loss = criterion(output, dummy_target)
    print(f"✅ 损失值: {loss.item():.4f}")
    
    print("🎉 所有测试通过!")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
