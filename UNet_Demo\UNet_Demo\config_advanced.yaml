# 高级U-net训练配置文件
# 集成多种优化技术

# 数据配置
data:
  dataset_path: "."
  num_classes: 29
  input_size: [512, 512]
  train_list: "VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt"
  val_list: "VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt"

# 模型配置
model:
  backbone: "resnet50"
  pretrained: true
  dropout_rate: 0.25
  use_attention: true
  attention_type: "cbam"

# 训练配置
train:
  total_epochs: 200
  save_period: 10
  save_dir: "logs"
  use_cuda: true
  
  # 学习率设置
  init_lr: 0.002
  encoder_lr: 5e-4
  decoder_lr: 1e-3
  head_lr: 2e-3
  weight_decay: 1e-4
  
  # 批次设置
  freeze_batch_size: 12
  unfreeze_batch_size: 6
  gradient_accumulation: 4
  num_workers: 8
  
  # 高级功能
  mixed_precision: true
  progressive_unfreezing: true
  freeze_epochs: 50

# 优化器配置
optimizer:
  type: "adamw"
  beta1: 0.9
  beta2: 0.999

# 学习率调度器
scheduler:
  type: "cosine_warmup"
  max_lr: 0.002
  min_lr: 5e-6
  warmup_epochs: 10
  cycles: 3

# 损失函数配置
loss:
  use_ce: true
  use_dice: true
  use_focal: true
  use_lovasz: true
  
  weight_ce: 0.6
  weight_dice: 1.2
  weight_focal: 0.8
  weight_lovasz: 1.8
  
  use_class_weights: true
  class_weight_method: "inverse_frequency"
  
  focal_alpha: 0.6
  focal_gamma: 3.0
  label_smoothing: 0.1

# 评估配置
evaluation:
  metrics: ["accuracy", "miou", "dice", "precision", "recall", "f1"]
  save_predictions: true

# 回调配置
callbacks:
  early_stopping:
    enabled: true
    patience: 30
    monitor: "val_miou"
    mode: "max"
  
  model_checkpoint:
    monitor: "val_miou"
    mode: "max"
    save_best_only: true

# 可视化配置
visualization:
  tensorboard:
    enabled: true
    log_dir: "runs"
    log_images: true
