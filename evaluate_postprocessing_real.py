#!/usr/bin/env python3
"""
在真实模型上评估后处理效果
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader
import logging
from datetime import datetime
import cv2
from PIL import Image

# 添加路径
sys.path.append('UNet_Demo/UNet_Demo')

from UNet_Demo.UNet_Demo.advanced_postprocessing import AdvancedPostProcessor
from UNet_Demo.UNet_Demo.utils.utils_metrics import compute_mIoU_tensor
from UNet_Demo.UNet_Demo.nets.deeplabv3plus import unet
from UNet_Demo.UNet_Demo.utils.utils import resize_image, preprocess_input, cvtColor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'postprocessing_real_eval_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_best_model():
    """加载最佳模型"""
    # 查找最佳智能优化模型
    model_files = [f for f in os.listdir('.') if f.startswith('best_smart_optimized_miou_')]
    if not model_files:
        logger.error("未找到智能优化模型文件")
        return None, 0
    
    # 按mIoU排序，选择最佳的
    model_files.sort(key=lambda x: float(x.split('_')[-1].replace('.pth', '')), reverse=True)
    best_model_file = model_files[0]
    best_miou = float(best_model_file.split('_')[-1].replace('.pth', ''))
    
    logger.info(f"加载最佳模型: {best_model_file} (mIoU: {best_miou:.4f})")
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = unet(
        num_classes=29,
        backbone='resnet50',
        pretrained=False,
        dropout_rate=0.3,
        use_attention=True,
        attention_type='cbam'
    ).to(device)
    
    # 加载权重
    try:
        checkpoint = torch.load(best_model_file, map_location=device, weights_only=True)
        model.load_state_dict(checkpoint)
        model.eval()
        logger.info("模型加载成功")
        return model, best_miou
    except Exception as e:
        logger.error(f"模型加载失败: {e}")
        return None, 0

def load_test_images(num_images=10):
    """加载测试图像"""
    images_dir = "UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/JPEGImages"
    masks_dir = "UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/SegmentationClass"
    
    if not os.path.exists(images_dir) or not os.path.exists(masks_dir):
        logger.error("测试数据目录不存在")
        return [], []
    
    # 获取图像文件列表
    image_files = [f for f in os.listdir(images_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    image_files = image_files[:num_images]  # 限制数量
    
    images = []
    masks = []
    
    for img_file in image_files:
        img_path = os.path.join(images_dir, img_file)
        mask_file = img_file.replace('.jpg', '.png').replace('.jpeg', '.png')
        mask_path = os.path.join(masks_dir, mask_file)
        
        if os.path.exists(mask_path):
            images.append(img_path)
            masks.append(mask_path)
    
    logger.info(f"加载了 {len(images)} 个测试样本")
    return images, masks

def predict_image(model, image_path, input_shape=(512, 512)):
    """预测单张图像"""
    device = next(model.parameters()).device
    
    # 读取和预处理图像
    image = Image.open(image_path)
    image = cvtColor(image)
    original_h, original_w = np.array(image).shape[:2]
    
    # 预处理
    image_data, nw, nh = resize_image(image, input_shape)
    image_data = np.expand_dims(np.transpose(preprocess_input(np.array(image_data, np.float32)), (2, 0, 1)), 0)
    
    # 转换为tensor
    images = torch.from_numpy(image_data).to(device)
    
    # 预测
    with torch.no_grad():
        outputs = model(images)
    
    return outputs, (original_h, original_w), (nw, nh)

def load_mask(mask_path, input_shape=(512, 512)):
    """加载掩码"""
    mask = Image.open(mask_path)
    mask = mask.resize(input_shape, Image.NEAREST)
    mask = np.array(mask)
    return torch.from_numpy(mask).long()

def evaluate_postprocessing_on_real_data():
    """在真实数据上评估后处理效果"""
    logger.info("🔬 开始在真实数据上评估后处理效果")
    
    # 加载模型
    model, original_miou = load_best_model()
    if model is None:
        return
    
    device = next(model.parameters()).device
    
    # 加载测试数据
    image_paths, mask_paths = load_test_images(20)  # 测试20张图像
    if not image_paths:
        logger.error("没有找到测试数据")
        return
    
    # 创建后处理器
    postprocessor = AdvancedPostProcessor(num_classes=29, device=device)
    
    # 测试不同的后处理配置
    configs = {
        'baseline': {
            'use_tta': False,
            'use_morphology': False,
            'use_connected_components': False,
            'use_watershed': False,
            'use_boundary_refinement': False,
            'use_multi_scale_fusion': False,
            'use_confidence_filtering': False,
        },
        'tta_only': {
            'use_tta': True,
            'use_morphology': False,
            'use_connected_components': False,
            'use_watershed': False,
            'use_boundary_refinement': False,
            'use_multi_scale_fusion': False,
            'use_confidence_filtering': False,
        },
        'lightweight': {
            'use_tta': True,
            'use_morphology': True,
            'use_connected_components': True,
            'use_watershed': False,
            'use_boundary_refinement': False,
            'use_multi_scale_fusion': False,
            'use_confidence_filtering': True,
        },
        'full': {
            'use_tta': True,
            'use_morphology': True,
            'use_connected_components': True,
            'use_watershed': True,
            'use_boundary_refinement': True,
            'use_multi_scale_fusion': True,
            'use_confidence_filtering': True,
        }
    }
    
    results = {}
    
    for config_name, config in configs.items():
        logger.info(f"\n测试配置: {config_name}")
        
        # 更新后处理器配置
        postprocessor.config.update(config)
        
        miou_list = []
        processing_times = []
        
        for i, (img_path, mask_path) in enumerate(zip(image_paths, mask_paths)):
            try:
                # 预测
                start_time = datetime.now()
                
                if config['use_tta']:
                    # 使用TTA
                    outputs, (orig_h, orig_w), (nw, nh) = predict_image(model, img_path)
                    outputs = postprocessor.test_time_augmentation(model, 
                                                                 torch.from_numpy(np.expand_dims(np.transpose(preprocess_input(np.array(resize_image(cvtColor(Image.open(img_path)), (512, 512))[0], np.float32)), (2, 0, 1)), 0)).to(device))
                else:
                    outputs, (orig_h, orig_w), (nw, nh) = predict_image(model, img_path)
                
                # 后处理
                if config_name == 'baseline':
                    # 基础处理
                    pred = torch.argmax(outputs, dim=1)[0].cpu().numpy()
                else:
                    # 高级后处理
                    pred, confidence_map = postprocessor.process_prediction(outputs[0])
                
                end_time = datetime.now()
                processing_time = (end_time - start_time).total_seconds()
                processing_times.append(processing_time)
                
                # 加载真实掩码
                mask = load_mask(mask_path).to(device)
                
                # 计算mIoU
                pred_tensor = torch.from_numpy(pred).to(device)
                miou = compute_mIoU_tensor(pred_tensor.unsqueeze(0), mask.unsqueeze(0), num_classes=29)
                miou_list.append(miou)
                
                if i % 5 == 0:
                    logger.info(f"  已处理 {i+1}/{len(image_paths)} 张图像")
                    
            except Exception as e:
                logger.error(f"处理图像 {img_path} 失败: {e}")
                continue
        
        # 计算平均结果
        if miou_list:
            avg_miou = np.mean(miou_list)
            avg_time = np.mean(processing_times)
            results[config_name] = {
                'miou': avg_miou,
                'time': avg_time,
                'count': len(miou_list)
            }
            logger.info(f"  平均mIoU: {avg_miou:.4f}")
            logger.info(f"  平均处理时间: {avg_time:.2f}秒")
        else:
            logger.error(f"  配置 {config_name} 没有成功处理任何图像")
    
    # 输出最终结果
    logger.info("\n" + "=" * 60)
    logger.info("📊 后处理效果评估结果")
    logger.info("=" * 60)
    
    if 'baseline' in results:
        baseline_miou = results['baseline']['miou']
        
        for config_name, result in results.items():
            miou = result['miou']
            time = result['time']
            count = result['count']
            
            if config_name == 'baseline':
                logger.info(f"{config_name:12s}: mIoU={miou:.4f}, 时间={time:.2f}s, 样本={count}")
            else:
                improvement = miou - baseline_miou
                improvement_pct = (improvement / baseline_miou) * 100 if baseline_miou > 0 else 0
                logger.info(f"{config_name:12s}: mIoU={miou:.4f} (+{improvement:+.4f}, {improvement_pct:+.2f}%), 时间={time:.2f}s, 样本={count}")
        
        # 找到最佳配置
        best_config = max(results.keys(), key=lambda k: results[k]['miou'])
        best_miou = results[best_config]['miou']
        best_improvement = best_miou - baseline_miou
        
        logger.info(f"\n🏆 最佳后处理配置: {best_config}")
        logger.info(f"   最佳mIoU: {best_miou:.4f}")
        logger.info(f"   相比基础: +{best_improvement:.4f} ({(best_improvement/baseline_miou)*100:+.2f}%)")
        
        # 目标分析
        target_miou = 0.45
        if best_miou >= target_miou:
            logger.info(f"🎯 成功达到目标mIoU {target_miou:.2f}!")
        else:
            gap = target_miou - best_miou
            logger.info(f"⚠️  距离目标mIoU {target_miou:.2f} 还差 {gap:.4f}")
    
    logger.info(f"\n⏰ 评估完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    evaluate_postprocessing_on_real_data()

if __name__ == "__main__":
    main()
