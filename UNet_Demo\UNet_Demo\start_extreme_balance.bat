@echo off
chcp 65001 >nul
title 极端类别平衡训练启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    极端类别平衡训练启动器                      ║
echo ║                                                              ║
echo ║  🎯 目标: 解决严重的类别不平衡问题                            ║
echo ║  🔥 困难类别: 8, 10, 18, 23, 26, 27, 28                     ║
echo ║  ⚡ 策略: 极端权重 + 多损失函数 + 特殊增强                     ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [INFO] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo [INFO] 检查必要文件...
if not exist "config_extreme_balance.yaml" (
    echo [ERROR] 配置文件不存在: config_extreme_balance.yaml
    pause
    exit /b 1
)

if not exist "train_extreme_balance.py" (
    echo [ERROR] 训练脚本不存在: train_extreme_balance.py
    pause
    exit /b 1
)

echo [INFO] 检查数据目录...
if not exist "VOCdevkit\VOC2025" (
    echo [ERROR] 数据目录不存在: VOCdevkit\VOC2025
    echo [INFO] 请确保数据已正确放置
    pause
    exit /b 1
)

echo [INFO] 环境检查完成
echo.

echo ==========================================
echo 🚨 即将开始极端类别平衡训练
echo 📊 这将使用极高的类别权重来强制学习困难类别
echo ⏱️  预计训练时间: 数小时到数天
echo 💾 训练过程将自动保存最佳模型
echo 📈 可通过 http://localhost:6006 查看TensorBoard
echo ==========================================
echo.

set /p confirm="确认开始训练? (y/N): "
if /i not "%confirm%"=="y" if /i not "%confirm%"=="yes" (
    echo [INFO] 用户取消训练
    pause
    exit /b 0
)

echo.
echo [INFO] 启动训练...
echo.

python start_extreme_balance_training.py

if errorlevel 1 (
    echo.
    echo [ERROR] 训练失败
    echo [INFO] 请检查错误信息并重试
) else (
    echo.
    echo [SUCCESS] 训练完成
    echo [INFO] 结果保存在 logs/extreme_balance/ 目录
)

echo.
pause
