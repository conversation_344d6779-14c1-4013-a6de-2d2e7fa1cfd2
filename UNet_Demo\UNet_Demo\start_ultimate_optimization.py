#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极优化启动脚本
一键启动所有优化策略，目标mIoU > 0.4
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🚀 终极优化训练启动器                      ║
    ║                                                              ║
    ║  目标: 将mIoU从0.3355提升到0.4+，达到学术论文质量标准         ║
    ║                                                              ║
    ║  优化策略:                                                   ║
    ║  1. ⏰ 更长训练时间 (500+ epochs)                            ║
    ║  2. 🧠 更强backbone (EfficientNet-B7)                       ║
    ║  3. 🎨 高级数据增强 (CutMix, MixUp, TTA)                    ║
    ║  4. 🤝 模型集成学习 (5个不同模型)                            ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """检查环境要求"""
    print("🔍 检查环境要求...")
    
    # 检查GPU
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            print("⚠️  警告: 未检测到GPU，训练将非常缓慢")
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    
    # 检查必要的包
    required_packages = ['albumentations', 'timm', 'segmentation_models_pytorch']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n📦 请安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def show_optimization_menu():
    """显示优化选项菜单"""
    print("\n🎯 选择优化策略:")
    print("1. 🚀 快速优化 (当前模型 + 更长训练)")
    print("2. 🧠 强化模型 (EfficientNet-B7 + 高级增强)")
    print("3. 🎨 数据增强 (CutMix + MixUp + TTA)")
    print("4. 🤝 模型集成 (5个模型集成)")
    print("5. 🔥 终极优化 (所有策略组合)")
    print("6. 📊 性能分析 (分析当前模型)")
    
    while True:
        try:
            choice = int(input("\n请选择 (1-6): "))
            if 1 <= choice <= 6:
                return choice
            else:
                print("请输入1-6之间的数字")
        except ValueError:
            print("请输入有效的数字")

def run_quick_optimization():
    """运行快速优化"""
    print("\n🚀 启动快速优化...")
    print("策略: 当前成功模型 + 更长训练时间")
    
    # 修改配置文件，增加训练轮数
    print("📝 更新配置文件...")
    
    # 启动训练
    cmd = ["python", "train.py", "--cfg", "config.yaml"]
    print(f"🏃 执行命令: {' '.join(cmd)}")
    
    return subprocess.run(cmd)

def run_stronger_model():
    """运行强化模型训练"""
    print("\n🧠 启动强化模型训练...")
    print("策略: EfficientNet-B7 + 高级数据增强")
    
    # 使用强化配置
    cmd = ["python", "train.py", "--cfg", "config_stronger_backbone.yaml"]
    print(f"🏃 执行命令: {' '.join(cmd)}")
    
    return subprocess.run(cmd)

def run_advanced_augmentation():
    """运行高级数据增强训练"""
    print("\n🎨 启动高级数据增强训练...")
    print("策略: CutMix + MixUp + 测试时增强")
    
    # 创建增强配置
    augmentation_config = """
# 高级数据增强配置
augmentation:
  cutmix:
    enabled: true
    alpha: 1.0
    p: 0.3
  mixup:
    enabled: true
    alpha: 0.4
    p: 0.2
  tta:
    enabled: true
    scales: [0.75, 1.0, 1.25, 1.5]
    flip: true
    rotate: [0, 90, 180, 270]
"""
    
    with open('config_advanced_aug.yaml', 'w') as f:
        # 读取基础配置并添加增强配置
        with open('config.yaml', 'r') as base_f:
            base_config = base_f.read()
        f.write(base_config + "\n" + augmentation_config)
    
    cmd = ["python", "train.py", "--cfg", "config_advanced_aug.yaml"]
    print(f"🏃 执行命令: {' '.join(cmd)}")
    
    return subprocess.run(cmd)

def run_ensemble_training():
    """运行模型集成训练"""
    print("\n🤝 启动模型集成训练...")
    print("策略: 5个不同模型的集成学习")
    
    cmd = ["python", "utils/ensemble_learning.py"]
    print(f"🏃 执行命令: {' '.join(cmd)}")
    
    return subprocess.run(cmd)

def run_ultimate_optimization():
    """运行终极优化"""
    print("\n🔥 启动终极优化...")
    print("策略: 所有优化技术的组合")
    
    cmd = ["python", "train_ultimate_optimization.py"]
    print(f"🏃 执行命令: {' '.join(cmd)}")
    
    return subprocess.run(cmd)

def analyze_performance():
    """分析当前模型性能"""
    print("\n📊 分析当前模型性能...")
    
    # 查看最新的训练日志
    logs_dir = "logs"
    if os.path.exists(logs_dir):
        subdirs = [d for d in os.listdir(logs_dir) if os.path.isdir(os.path.join(logs_dir, d))]
        if subdirs:
            latest_dir = max(subdirs, key=lambda x: os.path.getctime(os.path.join(logs_dir, x)))
            latest_log_dir = os.path.join(logs_dir, latest_dir)
            
            print(f"📁 最新训练日志: {latest_log_dir}")
            
            # 读取mIoU历史
            miou_file = os.path.join(latest_log_dir, "epoch_miou.txt")
            if os.path.exists(miou_file):
                with open(miou_file, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        mious = [float(line.strip()) for line in lines if line.strip()]
                        if mious:
                            best_miou = max(mious)
                            final_miou = mious[-1]
                            
                            print(f"📈 最佳mIoU: {best_miou:.4f}")
                            print(f"📊 最终mIoU: {final_miou:.4f}")
                            print(f"🎯 距离目标0.4还差: {max(0, 0.4 - best_miou):.4f}")
                            
                            if best_miou >= 0.4:
                                print("🏆 恭喜！已达到学术论文质量标准！")
                            else:
                                improvement_needed = ((0.4 - best_miou) / best_miou) * 100
                                print(f"📈 需要提升: {improvement_needed:.1f}%")
                                
                                # 给出建议
                                if best_miou < 0.35:
                                    print("💡 建议: 使用更强的backbone模型")
                                elif best_miou < 0.38:
                                    print("💡 建议: 增加数据增强和更长训练时间")
                                else:
                                    print("💡 建议: 尝试模型集成学习")
            
            # 启动TensorBoard
            print(f"\n📊 启动TensorBoard查看详细训练曲线...")
            tensorboard_cmd = ["tensorboard", "--logdir", latest_log_dir, "--port", "6007"]
            subprocess.Popen(tensorboard_cmd)
            print(f"🌐 TensorBoard已启动: http://localhost:6007")
    
    else:
        print("❌ 未找到训练日志")

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_requirements():
        print("\n❌ 环境检查失败，请先安装必要的依赖")
        return
    
    # 显示菜单
    choice = show_optimization_menu()
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行选择的策略
    if choice == 1:
        result = run_quick_optimization()
    elif choice == 2:
        result = run_stronger_model()
    elif choice == 3:
        result = run_advanced_augmentation()
    elif choice == 4:
        result = run_ensemble_training()
    elif choice == 5:
        result = run_ultimate_optimization()
    elif choice == 6:
        analyze_performance()
        return
    
    # 计算训练时间
    end_time = time.time()
    training_time = end_time - start_time
    hours = int(training_time // 3600)
    minutes = int((training_time % 3600) // 60)
    
    print(f"\n⏱️  训练时间: {hours}小时{minutes}分钟")
    
    if result.returncode == 0:
        print("🎉 训练成功完成！")
        print("\n📊 建议查看TensorBoard了解详细训练过程:")
        print("tensorboard --logdir=logs/current")
    else:
        print("❌ 训练过程中出现错误")
        print("请检查日志文件获取详细信息")

if __name__ == "__main__":
    main()
