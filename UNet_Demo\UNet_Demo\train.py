import argparse
import os
import datetime
import logging
import torch
import torch.backends.cudnn as cudnn
from torch import nn, optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau, LambdaLR, OneCycleLR
from torch.cuda.amp import GradScaler, autocast

from nets.deeplabv3plus import unet
from nets.unet_training import weights_init
from utils.callbacks import LossHistory, EvalCallback
from utils.dataloader import SegmentationDataset
from utils.losses import CombinedLoss, compute_class_weights
from utils.utils import show_config
from utils.utils_fit import fit_one_epoch
from utils.pretrain_utils import get_pretrained_weights, load_pretrained_weights, apply_finetune_strategy
from utils.sampler import ClassBalancedSampler
from utils.transfer_learning import setup_layerwise_learning_rates, progressive_unfreezing, get_unfreeze_schedule_from_config, create_cosine_warmup_scheduler
from utils.scheduler import build_scheduler as build_scheduler_new
from utils.label_alignment import build_aligned_model, print_alignment_stats
from utils.class_weights import get_class_weights

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
# 设置数据加载器的日志级别为WARNING，减少图像变换信息输出
logging.getLogger('utils.dataloader').setLevel(logging.WARNING)
logging.getLogger('utils.domain_augmentation').setLevel(logging.WARNING)
logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser(description="Semantic Segmentation Training")
    parser.add_argument('--cfg', type=str, default='config.yaml', help='Path to config file')
    return parser.parse_args()


def load_config(path):
    import yaml
    with open(path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def build_model(cfg, device):
    """构建模型并应用预训练策略"""
    # 获取模型参数
    dropout_rate = cfg['model'].get('dropout_rate', 0.2)
    use_attention = cfg['model'].get('use_attention', True)
    attention_type = cfg['model'].get('attention_type', 'cbam')
    transfer_learning = cfg['model'].get('transfer_learning', False)
    finetune_mode = cfg['model'].get('finetune_mode', 'progressive')
    use_label_alignment = cfg['model'].get('label_alignment', False)

    logger.info(f"模型配置: backbone={cfg['model']['backbone']}, "
          f"dropout_rate={dropout_rate}, use_attention={use_attention}, "
          f"attention_type={attention_type}, "
          f"transfer_learning={transfer_learning}, finetune_mode={finetune_mode}, "
          f"use_label_alignment={use_label_alignment}")

    # 如果启用了标签对齐
    if use_label_alignment:
        logger.info("使用标签对齐模型...")
        # 打印标签对齐统计信息
        print_alignment_stats()
        # 构建标签对齐模型
        model = build_aligned_model(cfg, device)
    else:
        # 创建基础模型
        model = unet(num_classes=cfg['data']['num_classes'],
                    backbone=cfg['model']['backbone'],
                    pretrained=cfg['model']['pretrained'],
                    dropout_rate=dropout_rate,
                    use_attention=use_attention,
                    attention_type=attention_type)

        # 初始化解码器权重
        for name, module in model.named_modules():
            if 'decoder' in name or 'segmentation_head' in name:
                weights_init(module)

        # 应用预训练
        if cfg.get('pretrain', {}).get('enabled', False) or transfer_learning:
            pretrain_cfg = cfg.get('pretrain', {})
            weights_path = cfg['model'].get('pretrained_weights', '') or pretrain_cfg.get('weights_path', '')

            if not weights_path:
                # 自动下载预训练权重
                dataset = pretrain_cfg.get('dataset', 'ade20k')  # 默认使用ADE20K
                logger.info(f"尝试下载{dataset}预训练权重...")

                # 使用改进的下载函数
                weights_path = get_pretrained_weights(
                    dataset=dataset,
                    backbone=cfg['model']['backbone'],
                    weights_dir="pretrained_weights"
                )

                if weights_path is None:
                    logger.warning("预训练权重下载失败，继续使用ImageNet预训练权重")

            # 加载预训练权重
            model = load_pretrained_weights(
                model=model,
                weights_path=weights_path,
                num_classes=cfg['data']['num_classes'],
                strict=False
            )

        # 将模型移动到设备
        model = model.to(device)

        # 使用channels_last内存格式，提高GPU利用率
        if device.type == 'cuda' and cfg['train'].get('use_channels_last', False):
            model = model.to(memory_format=torch.channels_last)
            print("使用channels_last内存格式，提高GPU利用率")

    # 如果启用了迁移学习，应用渐进式解冻策略
    if transfer_learning and finetune_mode == 'progressive':
        logger.info("应用渐进式解冻策略")
        # 首先冻结所有参数
        for param in model.parameters():
            param.requires_grad = False

        # 只解冻分割头
        for name, param in model.named_parameters():
            if 'segmentation_head' in name:
                param.requires_grad = True

        # 打印可训练参数数量
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        total_params = sum(p.numel() for p in model.parameters())
        logger.info(f"初始可训练参数: {trainable_params:,}/{total_params:,} ({trainable_params/total_params:.2%})")

    return model


def build_optimizer(cfg, model):
    """构建优化器，支持分层学习率"""
    # 检查是否使用迁移学习和分层学习率
    transfer_learning = cfg['model'].get('transfer_learning', False)

    # 从配置中获取优化器类型，默认为AdamW
    optimizer_type = cfg.get('optimizer', {}).get('type', 'adamw').lower()

    # 获取权重衰减值
    weight_decay = float(cfg['train'].get('weight_decay', 1e-4))

    # 获取动量值（用于SGD）
    momentum = float(cfg.get('optimizer', {}).get('momentum', 0.9))

    # 获取beta值（用于Adam/AdamW）
    beta1 = float(cfg.get('optimizer', {}).get('beta1', 0.9))
    beta2 = float(cfg.get('optimizer', {}).get('beta2', 0.999))

    if transfer_learning:
        logger.info("使用分层学习率优化器")
        # 使用分层学习率
        param_groups = setup_layerwise_learning_rates(model, cfg)

        # 根据优化器类型创建优化器
        if optimizer_type == 'sgd':
            optimizer = optim.SGD(param_groups, momentum=momentum, nesterov=True, weight_decay=weight_decay)
            logger.info(f"使用SGD优化器: momentum={momentum}, weight_decay={weight_decay}")
        elif optimizer_type == 'adam':
            optimizer = optim.Adam(param_groups, betas=(beta1, beta2), weight_decay=weight_decay)
            logger.info(f"使用Adam优化器: beta1={beta1}, beta2={beta2}, weight_decay={weight_decay}")
        else:  # 默认使用AdamW
            optimizer = optim.AdamW(param_groups, betas=(beta1, beta2), weight_decay=weight_decay)
            logger.info(f"使用AdamW优化器: beta1={beta1}, beta2={beta2}, weight_decay={weight_decay}")
    else:
        # 传统方式：分离编码器和头部参数
        encoder_params = list(model.encoder.parameters())
        encoder_ids = set(map(id, encoder_params))
        head_params = [p for p in model.parameters() if id(p) not in encoder_ids]

        # 强制将配置值转为 float，避免字符串导致错误
        init_lr = float(cfg['train']['init_lr'])
        encoder_scale = float(cfg['train'].get('encoder_lr_scale', 0.1))

        # 创建参数组
        param_groups = [
            {'params': encoder_params, 'lr': init_lr * encoder_scale},
            {'params': head_params,    'lr': init_lr}
        ]

        # 根据优化器类型创建优化器
        if optimizer_type == 'sgd':
            optimizer = optim.SGD(param_groups, momentum=momentum, nesterov=True, weight_decay=weight_decay)
            logger.info(f"使用SGD优化器: 基础学习率={init_lr}, 编码器学习率={init_lr * encoder_scale}")
        elif optimizer_type == 'adam':
            optimizer = optim.Adam(param_groups, betas=(beta1, beta2), weight_decay=weight_decay)
            logger.info(f"使用Adam优化器: 基础学习率={init_lr}, 编码器学习率={init_lr * encoder_scale}")
        else:  # 默认使用AdamW
            optimizer = optim.AdamW(param_groups, betas=(beta1, beta2), weight_decay=weight_decay)
            logger.info(f"使用AdamW优化器: 基础学习率={init_lr}, 编码器学习率={init_lr * encoder_scale}")

    return optimizer


def build_scheduler(cfg, optimizer, total_epochs, steps_per_epoch=None):
    """构建学习率调度器，支持多种调度策略"""
    # 检查是否使用迁移学习
    transfer_learning = cfg['model'].get('transfer_learning', False)

    if transfer_learning:
        logger.info("使用新的学习率调度器构建函数")
        # 使用新的调度器构建函数
        return build_scheduler_new(optimizer, cfg, steps_per_epoch, total_epochs)

    # 以下是原始的调度器构建逻辑
    sched_cfg = cfg['scheduler']
    sched_type = sched_cfg['type']
    min_lr = float(sched_cfg.get('min_lr', 1e-6))

    if sched_type == 'cosine':
        # 余弦退火调度
        scheduler = CosineAnnealingLR(
            optimizer,
            T_max=total_epochs - cfg['train'].get('freeze_epochs', 0),
            eta_min=min_lr
        )
        logger.info(f"使用余弦退火学习率调度，T_max={total_epochs - cfg['train'].get('freeze_epochs', 0)}，最小学习率={min_lr}")

    elif sched_type == 'plateau':
        # 根据验证损失调整学习率
        factor = float(sched_cfg.get('factor', 0.5))
        patience = int(sched_cfg.get('patience', 5))
        threshold = float(sched_cfg.get('threshold', 0.01))
        cooldown = int(sched_cfg.get('cooldown', 0))

        scheduler = ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=factor,
            patience=patience,
            threshold=threshold,
            cooldown=cooldown,
            min_lr=min_lr,
            verbose=True
        )
        logger.info(f"使用ReduceLROnPlateau学习率调度，factor={factor}，"
              f"patience={patience}，threshold={threshold}，"
              f"cooldown={cooldown}，最小学习率={min_lr}")

    elif sched_type == 'onecycle':
        # One Cycle Policy - 更激进的学习率策略
        if steps_per_epoch is None:
            raise ValueError("OneCycleLR需要指定steps_per_epoch参数")

        # 从配置中获取参数，如果没有则使用默认值
        max_lr = float(sched_cfg.get('max_lr', float(cfg['train'].get('init_lr', 0.001)) * 10))
        pct_start = float(sched_cfg.get('pct_start', 0.3))
        div_factor = float(sched_cfg.get('div_factor', 25.0))
        final_div_factor = float(sched_cfg.get('final_div_factor', 1000.0))

        scheduler = OneCycleLR(
            optimizer,
            max_lr=max_lr,
            total_steps=steps_per_epoch * total_epochs,
            pct_start=pct_start,
            div_factor=div_factor,
            final_div_factor=final_div_factor,
            anneal_strategy='cos'
        )
        logger.info(f"使用OneCycleLR学习率调度，最大学习率={max_lr}，"
              f"pct_start={pct_start}，div_factor={div_factor}，"
              f"final_div_factor={final_div_factor}，总步数={steps_per_epoch * total_epochs}")

    elif sched_type == 'cosine_warmup':
        # 余弦退火+预热
        if steps_per_epoch is None:
            raise ValueError("cosine_warmup需要指定steps_per_epoch参数")

        # 使用自定义的余弦退火+预热调度器
        scheduler = create_cosine_warmup_scheduler(optimizer, cfg, steps_per_epoch)

    elif sched_type == 'reduce_on_plateau':
        # 改进1(1).py中使用的ReduceLROnPlateau调度器
        factor = float(sched_cfg.get('factor', 0.5))
        patience = int(sched_cfg.get('patience', 3))
        threshold = float(sched_cfg.get('threshold', 0.01))
        cooldown = int(sched_cfg.get('cooldown', 0))

        scheduler = ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=factor,
            patience=patience,
            threshold=threshold,
            cooldown=cooldown,
            min_lr=min_lr,
            verbose=True
        )
        logger.info(f"使用ReduceLROnPlateau学习率调度（改进版），factor={factor}，"
              f"patience={patience}，threshold={threshold}，"
              f"cooldown={cooldown}，最小学习率={min_lr}")

    else:
        raise ValueError(f"不支持的学习率调度类型: {sched_type}")

    # 预热阶段 (对于非onecycle和非cosine_warmup)
    warmup_epochs = int(cfg['scheduler'].get('warmup_epochs', 0))
    if warmup_epochs > 0 and sched_type not in ['onecycle', 'cosine_warmup']:  # 这些调度器已包含预热
        logger.info(f"应用学习率预热，预热轮数={warmup_epochs}")

        def warmup_lambda(epoch):
            if epoch < warmup_epochs:
                return min(1.0, (epoch + 1) / warmup_epochs)
            return 1.0

        scheduler = LambdaLR(optimizer, lr_lambda=warmup_lambda)

    return scheduler


def main():
    args = parse_args()
    cfg = load_config(args.cfg)

    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() and cfg['train']['use_cuda'] else 'cpu')

    # 优化CUDA性能
    if device.type == 'cuda':
        # 启用cudnn基准测试，优化卷积操作
        if cfg['train'].get('benchmark_cudnn', True):
            cudnn.benchmark = True
            print("启用cudnn基准测试，优化卷积操作")

        # 设置cudnn确定性模式（如果需要可重现结果）
        cudnn.deterministic = cfg.get('reproducible', False)

        # 设置CUDA缓存分配器
        torch.cuda.empty_cache()
        # 使用更保守的内存分配策略
        torch.cuda.set_per_process_memory_fraction(0.85)  # 使用85%的GPU内存

        # 启用内存优化
        if hasattr(torch.cuda, 'memory_stats'):
            print(f"当前GPU内存使用: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")

        # 设置更积极的缓存清理
        if hasattr(torch.cuda, 'memory_summary'):
            print("启用积极的CUDA内存管理")

    print(f"使用设备: {device} - {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}")

    # 创建模型
    model = build_model(cfg, device)

    # 如果配置了冻结轮数且没有使用预训练微调策略
    if cfg['train']['freeze_epochs'] > 0 and not cfg.get('pretrain', {}).get('enabled', False):
        print(f"冻结编码器 {cfg['train']['freeze_epochs']} 轮")
        for p in model.encoder.parameters(): p.requires_grad = False

    # 数据加载器
    print("准备数据集...")

    # 构建完整的文件路径
    train_list_path = os.path.join(cfg['data']['root_dir'], cfg['data']['train_list'])
    val_list_path = os.path.join(cfg['data']['root_dir'], cfg['data']['val_list'])

    # 读取文件列表
    with open(train_list_path, 'r', encoding='utf-8') as f:
        train_files = [line.strip() for line in f if line.strip()]

    with open(val_list_path, 'r', encoding='utf-8') as f:
        val_files = [line.strip() for line in f if line.strip()]

    print(f"从文件列表中读取: 训练集 {len(train_files)} 个文件, 验证集 {len(val_files)} 个文件")

    # 创建数据集，传入配置以支持特定领域数据增强
    train_ds = SegmentationDataset(train_files,
                                   cfg['data']['root_dir'],
                                   tuple(cfg['data']['input_size']),
                                   cfg['data']['num_classes'],
                                   train=True,
                                   cfg=cfg)  # 传入配置
    val_ds   = SegmentationDataset(val_files,
                                   cfg['data']['root_dir'],
                                   tuple(cfg['data']['input_size']),
                                   cfg['data']['num_classes'],
                                   train=False,
                                   cfg=cfg)  # 传入配置

    print(f"训练集: {len(train_ds)} 样本, 验证集: {len(val_ds)} 样本")

    # 初始批量大小
    batch_size = cfg['train']['freeze_batch_size']

    # 暂时不使用类别平衡采样器，先使用普通采样
    print("使用普通采样器...")

    # 优化的DataLoader配置
    train_loader = DataLoader(
        train_ds,
        batch_size=batch_size,
        shuffle=True,
        num_workers=cfg['train']['num_workers'],
        pin_memory=True,
        pin_memory_device='cuda' if torch.cuda.is_available() else '',  # 直接固定到CUDA设备
        persistent_workers=True if cfg['train']['num_workers'] > 0 else False,  # 保持工作进程活跃
        prefetch_factor=2,  # 预取因子
        drop_last=True
    )

    val_loader = DataLoader(
        val_ds,
        batch_size=batch_size,
        shuffle=False,
        num_workers=cfg['train']['num_workers'],
        pin_memory=True,
        pin_memory_device='cuda' if torch.cuda.is_available() else '',  # 直接固定到CUDA设备
        persistent_workers=True if cfg['train']['num_workers'] > 0 else False,  # 保持工作进程活跃
        prefetch_factor=2,  # 预取因子
        drop_last=False
    )

    # 优化器
    optimizer = build_optimizer(cfg, model)

    # 学习率调度器
    steps_per_epoch = len(train_loader)
    scheduler = build_scheduler(cfg, optimizer, cfg['train']['total_epochs'], steps_per_epoch)

    # 损失函数
    # 使用新的类别权重计算函数
    cls_weights = get_class_weights(cfg)
    if cls_weights is not None:
        cls_weights = cls_weights.to(device)
        logger.info(f"类别权重计算完成，使用方法: {cfg['loss'].get('class_weight_method', 'inverse_frequency')}")
        # 打印每个类别的权重
        for i, w in enumerate(cls_weights):
            logger.info(f"  类别 {i}: 权重 = {w.item():.4f}")

    # 忽略索引
    ignore_index = cfg['loss'].get('ignore_index', -1)
    if ignore_index == -1 or ignore_index is None:
        ignore_index = cfg['data']['num_classes']

    # 确保ignore_index是整数
    ignore_index = int(ignore_index)

    # 创建损失函数
    loss_fn = CombinedLoss(cfg['loss'], cls_weights).to(device)

    # 回调函数 - 使用唯一的时间戳创建新的日志目录
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = os.path.join(cfg['train']['save_dir'], f"{cfg['model']['backbone']}_{timestamp}")

    # 创建当前训练的目录，方便TensorBoard只显示当前训练
    current_log_dir = os.path.join(cfg['train']['save_dir'], "current")

    # 如果目录已存在，先删除它
    if os.path.exists(current_log_dir):
        import shutil
        shutil.rmtree(current_log_dir)

    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)

    # 创建一个新的current目录
    os.makedirs(current_log_dir, exist_ok=True)

    logger.info(f"创建日志目录: {log_dir}")
    logger.info(f"创建当前训练目录: {current_log_dir}")

    # 创建TensorBoard写入器
    from torch.utils.tensorboard import SummaryWriter
    current_writer = SummaryWriter(current_log_dir)

    # 创建损失历史记录器
    loss_history = LossHistory(log_dir, model, input_shape=cfg['data']['input_size'])
    # 添加当前训练的TensorBoard写入器
    loss_history.current_writer = current_writer
    eval_callback = EvalCallback(val_loader,
                                 num_classes=cfg['data']['num_classes'],
                                 ignore_index=ignore_index,
                                 device=device,
                                 period=cfg['train']['eval_period'])

    # 打印配置
    if cfg['train']['verbose']:
        show_config(**cfg['train'], **cfg['scheduler'], **cfg['loss'])

    # 混合精度训练
    use_amp = cfg['train'].get('mixed_precision', False)
    scaler = GradScaler() if use_amp else None
    if use_amp:
        print("启用混合精度训练")

    # 早停
    early_stopping = cfg['train'].get('early_stopping', 0)
    if early_stopping > 0:
        print(f"启用早停，耐心值: {early_stopping}")
    best_val_loss = float('inf')
    patience_counter = 0

    # 训练循环
    logger.info(f"开始训练，总轮数: {cfg['train']['total_epochs']}")

    # 获取迁移学习相关配置
    transfer_learning = cfg['model'].get('transfer_learning', False)
    progressive_unfreezing_enabled = cfg['train'].get('progressive_unfreezing', False)

    # 如果启用了渐进式解冻，获取解冻计划
    unfreeze_schedule = None
    if transfer_learning and progressive_unfreezing_enabled:
        unfreeze_schedule = get_unfreeze_schedule_from_config(cfg)
        logger.info(f"渐进式解冻计划: {unfreeze_schedule}")

    for epoch in range(cfg['train']['total_epochs']):
        # 应用渐进式解冻策略
        if transfer_learning and progressive_unfreezing_enabled and unfreeze_schedule:
            logger.info(f"轮次 {epoch+1}: 应用渐进式解冻")
            progressive_unfreezing(model, epoch, unfreeze_schedule)

        # 传统解冻方式
        elif epoch == cfg['train'].get('freeze_epochs', 0) and not transfer_learning and not cfg.get('pretrain', {}).get('enabled', False):
            logger.info("解冻编码器")
            for p in model.encoder.parameters(): p.requires_grad = True

            # 更新批量大小
            batch_size = cfg['train']['unfreeze_batch_size']
            logger.info(f"更新批量大小: {batch_size}")

            # 更新批量大小，使用优化的DataLoader配置
            train_loader = DataLoader(
                train_ds,
                batch_size=batch_size,
                shuffle=True,
                num_workers=cfg['train']['num_workers'],
                pin_memory=True,
                pin_memory_device='cuda' if torch.cuda.is_available() else '',  # 直接固定到CUDA设备
                persistent_workers=True if cfg['train']['num_workers'] > 0 else False,  # 保持工作进程活跃
                prefetch_factor=2,  # 预取因子
                drop_last=True
            )

            val_loader = DataLoader(
                val_ds,
                batch_size=batch_size,
                shuffle=False,
                num_workers=cfg['train']['num_workers'],
                pin_memory=True,
                pin_memory_device='cuda' if torch.cuda.is_available() else '',  # 直接固定到CUDA设备
                persistent_workers=True if cfg['train']['num_workers'] > 0 else False,  # 保持工作进程活跃
                prefetch_factor=2,  # 预取因子
                drop_last=False
            )

            # 更新评估回调
            eval_callback.loader = val_loader

        # 训练一个轮次
        epoch_metrics = fit_one_epoch(
            model=model,
            loss_fn=loss_fn,
            optimizer=optimizer,
            scheduler=scheduler,
            train_loader=train_loader,
            val_loader=val_loader,
            epoch=epoch,
            device=device,
            loss_history=loss_history,
            eval_callback=eval_callback,
            cfg=cfg,
            scaler=scaler,
            use_amp=use_amp,
            accumulation_steps=cfg['train'].get('gradient_accumulation', 1)  # 从配置中获取梯度累积步数
        )

        # 早停检查
        if early_stopping > 0:
            val_loss = epoch_metrics.get('val_loss', float('inf'))
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_loss': val_loss,
                    'cfg': cfg
                }, os.path.join(log_dir, 'best_model.pth'))
                print(f"保存最佳模型，验证损失: {val_loss:.4f}")
            else:
                patience_counter += 1
                if patience_counter >= early_stopping:
                    print(f"早停触发，{patience_counter}轮未改善")
                    break

    # 关闭TensorBoard写入器
    loss_history.writer.close()
    if hasattr(loss_history, 'current_writer') and loss_history.current_writer is not None:
        loss_history.current_writer.close()
    print(f"训练完成，日志保存在: {log_dir}")
    print(f"可以使用以下命令查看当前训练的TensorBoard: tensorboard --logdir={os.path.join(cfg['train']['save_dir'], 'current')}")

if __name__ == '__main__':
    main()
