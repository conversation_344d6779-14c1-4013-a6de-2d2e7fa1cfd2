# 🚀 高级U-net训练系统

这是一个功能完整的语义分割训练框架，集成了多种先进的优化技术和最佳实践。

## 📋 新增功能

### 🎯 核心优化功能

1. **高级训练脚本** (`train_advanced.py`)
   - 分层学习率设置
   - 渐进式解冻策略
   - 混合精度训练
   - 梯度累积
   - 自动检查点保存

2. **模型集成预测** (`ensemble_predict.py`)
   - 多模型集成
   - 测试时增强(TTA)
   - 批量预测
   - 概率图保存

3. **自动超参数调优** (`hyperparameter_tuning.py`)
   - 基于Optuna的自动调优
   - 多种超参数搜索
   - 分布式调优支持
   - 结果可视化

4. **高级评估系统** (`advanced_evaluate.py`)
   - 详细性能指标
   - 混淆矩阵可视化
   - 每类IoU分析
   - 错误案例分析

5. **一键训练** (`one_click_train.py`)
   - 自动化训练流程
   - 环境检查
   - 自动评估
   - 报告生成

## 🛠️ 快速开始

### 1. 环境测试
```bash
# 运行快速测试，验证环境配置
python quick_test.py
```

### 2. 一键训练（推荐新手）
```bash
# Windows用户
start_advanced_training.bat

# 或直接运行
python one_click_train.py --config config_advanced.yaml --tensorboard
```

### 3. 高级训练
```bash
# 使用优化配置训练
python train_advanced.py --config config_advanced.yaml

# 恢复训练
python train_advanced.py --config config_advanced.yaml --resume logs/best_model.pth
```

## 📊 性能优化建议

### RTX 4070 Ti Super 16GB 优化配置

```yaml
train:
  freeze_batch_size: 12      # 冻结阶段批次大小
  unfreeze_batch_size: 6     # 解冻阶段批次大小
  gradient_accumulation: 4   # 梯度累积步数
  mixed_precision: true      # 混合精度训练
  
loss:
  weight_ce: 0.6            # 交叉熵权重
  weight_dice: 1.2          # Dice损失权重
  weight_focal: 0.8         # Focal损失权重
  weight_lovasz: 1.8        # Lovász损失权重
```

### 学习率策略

```yaml
scheduler:
  type: "cosine_warmup"
  max_lr: 0.002
  min_lr: 5e-6
  warmup_epochs: 10
  cycles: 3
```

## 🔧 高级功能使用

### 1. 超参数调优
```bash
# 自动搜索最佳超参数
python hyperparameter_tuning.py \
  --config config_advanced.yaml \
  --study_name unet_optimization \
  --n_trials 100
```

### 2. 模型集成
```bash
# 配置集成模型
# 编辑 ensemble_config.yaml

# 运行集成预测
python ensemble_predict.py \
  --config ensemble_config.yaml \
  --input test_images/ \
  --output predictions/ \
  --tta
```

### 3. 高级评估
```bash
# 详细评估模型性能
python advanced_evaluate.py \
  --model logs/best_model.pth \
  --config config_advanced.yaml \
  --image_dir test_images/ \
  --label_dir test_labels/ \
  --output_dir evaluation_results/
```

### 4. GPU监控
```bash
# 实时监控GPU使用情况
python gpu_memory_monitor.py --optimize --interval 2 --plot --log
```

## 📈 训练监控

### TensorBoard
```bash
# 启动TensorBoard
tensorboard --logdir=logs --port=6006
# 访问 http://localhost:6006
```

### 实时监控
- 训练损失和验证损失
- 学习率变化
- GPU内存使用
- mIoU指标变化

## 🎯 最佳实践

### 1. 训练策略
- 使用渐进式解冻，先训练分割头
- 采用分层学习率，编码器使用较小学习率
- 启用混合精度训练节省显存
- 使用梯度累积模拟大批次训练

### 2. 数据增强
- 适度的几何变换（旋转、翻转、缩放）
- 颜色空间增强（亮度、对比度、饱和度）
- 领域特定增强（针对具体任务）

### 3. 损失函数
- 组合多种损失函数
- 使用类别权重处理不平衡数据
- Focal Loss处理困难样本
- Lovász Loss优化IoU指标

### 4. 模型优化
- 使用注意力机制提升性能
- 适当的Dropout防止过拟合
- 预训练权重加速收敛

## 🔍 故障排除

### 常见问题

1. **显存不足**
   ```bash
   # 减小批次大小
   freeze_batch_size: 8
   unfreeze_batch_size: 4
   
   # 启用梯度累积
   gradient_accumulation: 8
   ```

2. **训练不收敛**
   ```bash
   # 降低学习率
   init_lr: 0.001
   
   # 增加warmup
   warmup_epochs: 15
   ```

3. **过拟合**
   ```bash
   # 增加正则化
   dropout_rate: 0.3
   weight_decay: 1e-3
   
   # 增强数据增强
   augmentation_strength: "strong"
   ```

## 📁 文件结构

```
UNet_Demo/
├── train_advanced.py          # 高级训练脚本
├── ensemble_predict.py        # 模型集成预测
├── hyperparameter_tuning.py   # 超参数调优
├── advanced_evaluate.py       # 高级评估
├── one_click_train.py         # 一键训练
├── quick_test.py              # 快速测试
├── config_advanced.yaml       # 高级配置文件
├── ensemble_config.yaml       # 集成配置文件
├── start_advanced_training.bat # Windows启动脚本
└── README_ADVANCED.md         # 本文档
```

## 🎉 预期性能提升

使用这些优化技术，你可以期待：

- **训练速度提升**: 30-50%（混合精度+优化批次）
- **模型性能提升**: 2-5% mIoU（集成+TTA）
- **显存使用优化**: 20-30%（梯度累积+混合精度）
- **训练稳定性**: 显著提升（学习率调度+渐进解冻）

## 📞 支持

如果遇到问题：
1. 首先运行 `python quick_test.py` 检查环境
2. 查看训练日志和TensorBoard
3. 检查GPU内存使用情况
4. 参考常见问题解决方案

祝你训练愉快！🚀
