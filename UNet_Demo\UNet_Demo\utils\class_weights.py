"""
类别权重计算工具
用于处理类别不平衡问题
"""
import os
import numpy as np
import torch
import cv2
import logging
from typing import Dict, List, Optional, Tuple, Union
from glob import glob
from tqdm import tqdm

logger = logging.getLogger(__name__)

def compute_class_weights(
    dataset_path: str,
    num_classes: int,
    method: str = "inverse_frequency",
    ignore_index: int = 255,
    beta: float = 0.999,
    samples_per_class: Optional[List[int]] = None
) -> torch.Tensor:
    """
    计算类别权重

    Args:
        dataset_path: 数据集路径
        num_classes: 类别数量
        method: 权重计算方法，可选值：
            - "inverse_frequency": 逆频率权重
            - "effective_samples": 有效样本数权重
            - "balanced": 平衡权重
        ignore_index: 忽略的索引
        beta: 有效样本数方法的参数
        samples_per_class: 每个类别的样本数，如果为None则自动计算

    Returns:
        类别权重张量
    """
    if samples_per_class is None:
        # 自动计算每个类别的样本数
        logger.info(f"计算每个类别的样本数...")
        samples_per_class = count_samples_per_class(dataset_path, num_classes, ignore_index)

    logger.info(f"每个类别的样本数: {samples_per_class}")

    # 计算权重
    if method == "inverse_frequency":
        # 逆频率权重 - 使用更稳定的计算方式
        # 将样本数转换为频率
        frequencies = np.array(samples_per_class) / sum(samples_per_class)
        # 避免除以零，添加小的平滑值
        epsilon = 1e-7
        # 计算逆频率，并限制最大值
        class_weights = 1.0 / (frequencies + epsilon)
        # 归一化权重，使其均值为1
        class_weights = class_weights / np.mean(class_weights)
    elif method == "effective_samples":
        # 有效样本数权重
        effective_num = 1.0 - np.power(beta, samples_per_class)
        # 避免除以零
        epsilon = 1e-7
        class_weights = (1.0 - beta) / (np.array(effective_num) + epsilon)
        # 归一化权重，使其均值为1
        class_weights = class_weights / np.mean(class_weights)
    elif method == "balanced":
        # 平衡权重 - 简化计算
        frequencies = np.array(samples_per_class) / sum(samples_per_class)
        # 避免除以零
        epsilon = 1e-7
        # 计算平衡权重
        class_weights = 1.0 / (frequencies + epsilon)
        # 归一化权重，使其均值为1
        class_weights = class_weights / np.mean(class_weights)
    elif method == "log_smooth":
        # 新增：对数平滑权重 - 解决极端权重问题
        # 使用对数函数平滑权重分布
        frequencies = np.array(samples_per_class) / sum(samples_per_class)
        epsilon = 1e-7
        # 对数平滑，减少极端权重
        class_weights = np.log(1.0 / (frequencies + epsilon) + 1.0)
        # 归一化权重
        class_weights = class_weights / np.mean(class_weights)
    elif method == "sqrt_smooth":
        # 新增：平方根平滑权重
        frequencies = np.array(samples_per_class) / sum(samples_per_class)
        epsilon = 1e-7
        # 平方根平滑
        class_weights = np.sqrt(1.0 / (frequencies + epsilon))
        # 归一化权重
        class_weights = class_weights / np.mean(class_weights)
    elif method == "extreme_balance":
        # 新增：极端平衡方法 - 针对严重类别不平衡
        frequencies = np.array(samples_per_class) / sum(samples_per_class)
        epsilon = 1e-8

        # 使用更激进的权重计算
        # 对于极少数类别，给予极高权重
        class_weights = 1.0 / (frequencies + epsilon)

        # 识别极少数类别（频率小于平均频率的1/10）
        mean_freq = np.mean(frequencies)
        rare_classes = frequencies < (mean_freq / 10)

        # 对极少数类别应用额外的权重放大
        class_weights[rare_classes] *= 5.0

        # 对完全未学习的类别（根据历史表现）给予最高权重
        zero_performance_classes = [8, 18, 28]  # 根据记忆中的信息
        for cls in zero_performance_classes:
            if cls < len(class_weights):
                class_weights[cls] = max(class_weights) * 2.0

        # 归一化权重
        class_weights = class_weights / np.mean(class_weights)
    elif method == "manual":
        # 手动指定权重 - 从配置中获取
        logger.info("使用手动指定的类别权重")
        # 这里需要从外部传入手动权重，暂时使用默认值
        class_weights = np.ones(num_classes, dtype=np.float32)
    else:
        raise ValueError(f"不支持的权重计算方法: {method}")

    # 优化：根据方法调整权重范围
    if method in ["log_smooth", "sqrt_smooth"]:
        # 对于平滑方法，使用更保守的权重范围
        min_weight = 0.1
        max_weight = 5.0  # 限制最大权重，避免极端值
    else:
        # 对于传统方法，保持原有范围
        min_weight = 0.1
        max_weight = 25.0

    class_weights = np.clip(class_weights, min_weight, max_weight)

    # 根据当前模型表现调整权重
    # 为表现差的类别额外增加权重
    poor_performing_classes = [5, 7, 8, 9, 10, 11, 14, 15, 17, 18, 19, 22, 23, 24, 25, 26, 27, 28]
    for cls in poor_performing_classes:
        if cls < len(class_weights):
            # 为表现差的类别增加权重
            class_weights[cls] = min(class_weights[cls] * 2.5, max_weight)

    # 为完全未识别的类别(IoU=0)设置最高权重
    zero_iou_classes = [7, 8, 9, 11, 15, 17, 18, 22, 24, 25, 26, 27, 28]
    for cls in zero_iou_classes:
        if cls < len(class_weights):
            class_weights[cls] = max_weight

    # 将权重转换为张量
    weights_tensor = torch.FloatTensor(class_weights)

    logger.info(f"类别权重 ({method}): {weights_tensor.tolist()}")

    return weights_tensor

def count_samples_per_class(
    dataset_path: str,
    num_classes: int,
    ignore_index: int = 255
) -> List[int]:
    """
    计算每个类别的样本数

    Args:
        dataset_path: 数据集路径
        num_classes: 类别数量
        ignore_index: 忽略的索引

    Returns:
        每个类别的样本数列表
    """
    # 查找所有标签文件
    label_files = glob(os.path.join(dataset_path, "VOCdevkit/VOC2025/SegmentationClass/*.png"))

    if not label_files:
        logger.warning(f"未找到标签文件: {os.path.join(dataset_path, 'VOCdevkit/VOC2025/SegmentationClass/*.png')}")
        # 返回均匀分布
        return [1] * num_classes

    # 初始化计数器
    samples_per_class = [0] * num_classes

    # 统计每个类别的像素数
    for label_file in tqdm(label_files, desc="统计类别分布", leave=True, bar_format='{l_bar}{bar}{r_bar}'):
        try:
            # 读取标签
            label = cv2.imread(label_file, cv2.IMREAD_GRAYSCALE)

            if label is None:
                logger.warning(f"无法读取标签文件: {label_file}")
                continue

            # 统计每个类别的像素数
            for class_idx in range(num_classes):
                samples_per_class[class_idx] += np.sum(label == class_idx)
        except Exception as e:
            logger.error(f"处理标签文件时出错: {label_file}, 错误: {e}")

    # 确保每个类别至少有一个样本
    samples_per_class = [max(1, count) for count in samples_per_class]

    return samples_per_class

def get_class_weights(cfg: Dict) -> Optional[torch.Tensor]:
    """
    根据配置获取类别权重

    Args:
        cfg: 配置字典

    Returns:
        类别权重张量，如果不使用类别权重则返回None
    """
    # 检查是否使用类别权重
    use_class_weights = cfg.get('loss', {}).get('use_class_weights', False)

    if not use_class_weights:
        logger.info("不使用类别权重")
        return None

    # 获取权重计算方法
    method = cfg.get('loss', {}).get('class_weight_method', "effective_samples")

    # 获取数据集路径和类别数
    dataset_path = cfg.get('data', {}).get('dataset_path', ".")
    num_classes = cfg.get('data', {}).get('num_classes', 29)
    ignore_index = cfg.get('loss', {}).get('ignore_index', 255)

    # 如果是手动权重，直接从配置中获取
    if method == "manual":
        manual_weights = cfg.get('loss', {}).get('manual_weights', None)
        if manual_weights is None:
            logger.warning("manual方法需要指定manual_weights，使用默认权重")
            return None

        if len(manual_weights) != num_classes:
            logger.error(f"manual_weights长度({len(manual_weights)})与类别数({num_classes})不匹配")
            return None

        logger.info(f"使用手动指定的类别权重: {manual_weights}")
        weights = torch.FloatTensor(manual_weights)
        return weights

    # 计算类别权重
    logger.info(f"使用 {method} 方法计算类别权重")
    weights = compute_class_weights(
        dataset_path=dataset_path,
        num_classes=num_classes,
        method=method,
        ignore_index=ignore_index
    )

    return weights
