# 模型集成配置文件
# 用于集成多个训练好的模型进行预测

models:
  # 模型1: ResNet50骨干的DeepLabV3+
  - model_path: "logs/resnet50_20250519_121518/best_model.pth"
    weight: 0.4  # 权重
    config:
      num_classes: 29
      backbone: "resnet50"
      dropout_rate: 0.2
      use_attention: true
      attention_type: "cbam"
  
  # 模型2: ResNet101骨干的DeepLabV3+
  - model_path: "logs/resnet101_best/best_model.pth"
    weight: 0.3
    config:
      num_classes: 29
      backbone: "resnet101"
      dropout_rate: 0.2
      use_attention: true
      attention_type: "cbam"
  
  # 模型3: EfficientNet-B4骨干的DeepLabV3+
  - model_path: "logs/efficientnet-b4_20250518_112122/best_model.pth"
    weight: 0.3
    config:
      num_classes: 29
      backbone: "efficientnet-b4"
      dropout_rate: 0.2
      use_attention: true
      attention_type: "cbam"

# 预测设置
prediction:
  input_shape: [512, 512]
  use_tta: true  # 是否使用测试时增强
  save_probability: true  # 是否保存概率图
  
# 后处理设置
postprocess:
  # 条件随机场(CRF)后处理
  use_crf: false
  crf_iterations: 10
  
  # 形态学操作
  use_morphology: false
  morphology_kernel_size: 3
  
  # 连通组件分析
  use_connected_components: false
  min_component_size: 100
