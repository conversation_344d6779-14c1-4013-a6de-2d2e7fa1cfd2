# 极端类别平衡配置 - 专门解决类别不平衡问题
# 目标：让所有类别都能学习，特别是完全未学习的类别 8,10,18,23,26,27,28

# 数据配置
data:
  num_classes: 29
  input_shape: [512, 512, 3]
  
  # 数据路径
  train_annotation_path: "VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt"
  val_annotation_path: "VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt"
  
  # 极端数据增强策略
  data_augmentation:
    enabled: true
    
    # 基础增强 - 更激进
    basic:
      horizontal_flip: 0.8
      vertical_flip: 0.4
      rotation: 30
      brightness: 0.4
      contrast: 0.4
      saturation: 0.3
      hue: 0.2
    
    # 几何变换 - 极端变换
    geometric:
      random_crop: 0.9
      crop_scale: [0.5, 1.0]
      elastic_transform: 0.5
      grid_distortion: 0.4
      perspective: 0.3
    
    # 困难类别特殊增强
    difficult_class_augmentation:
      enabled: true
      target_classes: [8, 10, 18, 23, 26, 27, 28]  # 完全未学习的类别
      oversample_ratio: 10.0  # 10倍过采样
      special_transforms:
        mixup_alpha: 0.6
        cutmix_alpha: 1.5
        copy_paste_prob: 0.8  # 复制粘贴困难类别
        mosaic_prob: 0.5
        
    # 高级增强技术
    advanced:
      autoaugment: true
      randaugment_n: 3
      randaugment_m: 15
      trivialaugment: true

# 模型配置 - 增强版架构
model:
  backbone: "resnet50"
  pretrained: true
  
  # 多尺度特征融合
  multi_scale_features: true
  feature_pyramid_network: true
  
  # 注意力机制
  attention:
    enabled: true
    type: "cbam"  # Channel and Spatial Attention
    reduction: 8
    
  # 深度监督
  deep_supervision:
    enabled: true
    aux_loss_weight: 0.6
    intermediate_supervision: true
    
  # 正则化
  dropout_rate: 0.4
  batch_norm_momentum: 0.01
  
  # 特殊的困难类别处理模块
  difficult_class_module:
    enabled: true
    target_classes: [8, 10, 18, 23, 26, 27, 28]
    dedicated_head: true  # 为困难类别创建专门的分类头
    feature_enhancement: true

# 训练配置 - 极端优化
training:
  total_epochs: 300
  batch_size: 4  # 减小batch size以容纳更多增强
  
  # 分层学习率策略
  learning_rate:
    backbone_lr: 5e-5      # 骨干网络保守学习率
    decoder_lr: 2e-4       # 解码器中等学习率
    classifier_lr: 5e-3    # 分类头激进学习率
    difficult_class_lr: 1e-2  # 困难类别专用头超高学习率
    
  # 学习率调度
  scheduler:
    type: "cosine_restart"
    warmup_epochs: 20
    restart_epochs: [80, 160, 240]
    min_lr: 1e-7
    
  # 优化器配置
  optimizer:
    type: "adamw"
    weight_decay: 0.02
    beta1: 0.9
    beta2: 0.999
    eps: 1e-8
    
  # 梯度处理
  gradient_clipping: 2.0
  gradient_accumulation: 4  # 梯度累积增加有效batch size
  
  # 混合精度训练
  mixed_precision: true
  
  # 渐进式解冻策略
  progressive_unfreezing:
    enabled: true
    unfreeze_schedule: [5, 15, 30, 50]  # 更激进的解冻

# 极端类别平衡策略
class_balancing:
  # 极端类别权重
  class_weights:
    # 完全未学习的类别 - 极高权重
    8: 100.0
    10: 90.0
    18: 80.0
    23: 70.0
    26: 60.0
    27: 50.0
    28: 40.0
    
    # 中等表现类别 - 中等权重
    5: 15.0
    7: 12.0
    9: 10.0
    11: 8.0
    15: 6.0
    17: 5.0
    19: 20.0
    22: 15.0
    25: 10.0
    
    # 优秀表现类别 - 极低权重
    0: 0.01   # 背景
    3: 0.01   # 天空
    13: 0.01  # 道路
    2: 0.05
    21: 0.05
    
    # 其他类别 - 低权重
    1: 0.5
    4: 0.8
    6: 0.6
    12: 0.4
    14: 0.3
    16: 0.7
    20: 0.6
    24: 0.5
  
  # 平衡采样策略
  balanced_sampling:
    enabled: true
    strategy: "class_aware"  # 确保每个batch包含困难类别
    min_samples_per_class: 1
    oversample_difficult: true
    
  # 困难类别专门处理
  difficult_class_focus:
    enabled: true
    target_classes: [8, 10, 18, 23, 26, 27, 28]
    dedicated_loss_weight: 5.0  # 困难类别损失额外权重
    focal_gamma_boost: 2.0  # 困难类别focal loss gamma加成

# 损失函数 - 多重优化
loss:
  # 主要损失组合
  components:
    cross_entropy:
      enabled: true
      weight: 0.1  # 大幅降低CE权重
      label_smoothing: 0.15
      
    focal_loss:
      enabled: true
      weight: 3.0  # 大幅增加Focal权重
      gamma: 6.0   # 极高gamma值，专注困难样本
      alpha: 0.95
      
    dice_loss:
      enabled: true
      weight: 2.5
      smooth: 1e-6
      
    tversky_loss:
      enabled: true
      weight: 2.0
      alpha: 0.2  # 偏向召回率
      beta: 0.8
      
    lovasz_loss:
      enabled: true
      weight: 1.8
      
    # 困难类别专用损失
    difficult_class_focal:
      enabled: true
      weight: 5.0
      gamma: 8.0  # 超高gamma
      target_classes: [8, 10, 18, 23, 26, 27, 28]
  
  # 动态损失权重调整
  dynamic_weighting:
    enabled: true
    adjust_frequency: 10  # 每10轮调整一次权重
    performance_threshold: 0.01  # IoU改善阈值
    
  ignore_index: 255

# 数据采样策略
sampling:
  # 困难类别过采样
  oversample_strategy:
    enabled: true
    target_classes: [8, 10, 18, 23, 26, 27, 28]
    oversample_ratio: 15.0  # 15倍过采样
    
  # 平衡批次采样
  balanced_batch:
    enabled: true
    ensure_difficult_classes: true
    min_difficult_samples: 2  # 每个batch至少2个困难类别样本
    
  # 困难样本挖掘
  hard_negative_mining:
    enabled: true
    ratio: 0.3
    update_frequency: 5

# 验证和评估
evaluation:
  eval_period: 1
  metrics: ["miou", "class_iou", "pixel_accuracy", "class_accuracy", "dice", "precision", "recall", "f1"]
  
  # 困难类别专门监控
  difficult_class_monitoring:
    enabled: true
    target_classes: [8, 10, 18, 23, 26, 27, 28]
    alert_threshold: 0.001  # IoU低于0.001时报警
    
  # 早停策略 - 更宽松
  early_stopping:
    enabled: true
    patience: 40  # 增加耐心
    monitor: "val_miou"
    min_delta: 0.0005
    restore_best_weights: true
    
  # 模型保存
  save_strategy:
    save_best_only: false
    save_top_k: 5
    save_frequency: 10
    save_difficult_class_improvements: true  # 困难类别有改善就保存

# 回调和监控
callbacks:
  # 学习率监控
  lr_monitor:
    enabled: true
    log_momentum: true
    
  # 类别统计监控
  class_stats_monitor:
    enabled: true
    log_frequency: 1  # 每轮都记录
    plot_confusion_matrix: true
    
  # 困难类别专门监控
  difficult_class_callback:
    enabled: true
    target_classes: [8, 10, 18, 23, 26, 27, 28]
    log_frequency: 1
    save_predictions: true
    alert_on_zero_iou: true
    
  # 预测可视化
  prediction_visualization:
    enabled: true
    save_frequency: 5
    num_samples: 12
    focus_difficult_classes: true
    
  # 损失组件监控
  loss_component_monitor:
    enabled: true
    log_all_components: true
    plot_trends: true

# 硬件优化
hardware:
  use_cuda: true
  mixed_precision: true
  benchmark_cudnn: true
  use_channels_last: true
  num_workers: 8
  pin_memory: true
  
  # 内存优化
  gradient_checkpointing: true
  empty_cache_frequency: 10
  
  # 多GPU支持
  multi_gpu: false
  sync_batchnorm: false

# 日志配置
logging:
  log_dir: "logs/extreme_balance"
  experiment_name: "extreme_class_balance_v1"
  tensorboard: true
  wandb: false
  save_predictions: true
  save_model_graph: true
  verbose: true
