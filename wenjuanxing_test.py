#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问卷星自动上传测试脚本
用于测试和验证功能，只创建1-2题进行验证
"""

import os
import time
import glob
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
from datetime import datetime

def setup_logging():
    """设置日志"""
    log_filename = f"wenjuanxing_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def setup_driver():
    """设置浏览器驱动"""
    try:
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service

        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # 使用webdriver-manager自动管理ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.maximize_window()

        return driver, WebDriverWait(driver, 20)

    except Exception as e:
        print(f"设置浏览器驱动失败: {e}")
        return None, None

def load_test_images(image_folder, count=20):
    """加载测试图片"""
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif']
    images = []

    for ext in image_extensions:
        pattern = os.path.join(image_folder, ext)
        images.extend(glob.glob(pattern))
        pattern_upper = os.path.join(image_folder, ext.upper())
        images.extend(glob.glob(pattern_upper))

    images.sort()
    return images[:count]  # 只返回前20张用于测试

def find_element_by_multiple_selectors(wait, selectors, timeout=10):
    """尝试多个选择器找到元素"""
    for selector in selectors:
        try:
            element = WebDriverWait(wait._driver, timeout).until(
                EC.element_to_be_clickable((By.XPATH, selector))
            )
            return element
        except TimeoutException:
            continue
    return None

def test_page_elements(driver, wait, logger):
    """测试页面元素是否可以找到"""
    logger.info("开始测试页面元素...")

    # 测试添加题目按钮
    add_question_selectors = [
        "//span[contains(text(), '添加题目')]",
        "//button[contains(text(), '添加题目')]",
        "//div[contains(text(), '添加题目')]",
        "//a[contains(text(), '添加题目')]",
        "//*[contains(@class, 'add-question')]",
        "//*[contains(@id, 'addQuestion')]"
    ]

    add_btn = find_element_by_multiple_selectors(wait, add_question_selectors)
    if add_btn:
        logger.info("✅ 找到添加题目按钮")
        return True
    else:
        logger.error("❌ 找不到添加题目按钮")
        # 截图保存当前页面
        driver.save_screenshot(f"page_screenshot_{datetime.now().strftime('%H%M%S')}.png")
        return False

def create_test_question(driver, wait, logger, question_number, image_paths):
    """创建测试题目"""
    try:
        logger.info(f"开始创建测试题目 {question_number}...")

        # 1. 点击添加题目
        add_question_selectors = [
            "//span[contains(text(), '添加题目')]",
            "//button[contains(text(), '添加题目')]",
            "//div[contains(text(), '添加题目')]",
            "//a[contains(text(), '添加题目')]"
        ]

        add_btn = find_element_by_multiple_selectors(wait, add_question_selectors)
        if not add_btn:
            raise Exception("找不到添加题目按钮")

        add_btn.click()
        logger.info("✅ 点击添加题目按钮成功")
        time.sleep(3)

        # 2. 查找循环评价选项
        loop_evaluation_selectors = [
            "//div[contains(text(), '循环评价')]",
            "//span[contains(text(), '循环评价')]",
            "//li[contains(text(), '循环评价')]",
            "//*[contains(@title, '循环评价')]"
        ]

        loop_btn = find_element_by_multiple_selectors(wait, loop_evaluation_selectors)
        if loop_btn:
            loop_btn.click()
            logger.info("✅ 选择循环评价题型成功")
            time.sleep(3)
        else:
            logger.warning("⚠️ 找不到循环评价选项，尝试其他题型...")

        # 3. 设置题目标题
        title_selectors = [
            "//input[@placeholder='请输入题目']",
            "//input[contains(@placeholder, '题目')]",
            "//textarea[contains(@placeholder, '题目')]",
            "//*[contains(@class, 'question-title')]//input"
        ]

        title_input = find_element_by_multiple_selectors(wait, title_selectors)
        if title_input:
            title_input.clear()
            title_input.send_keys(f"测试图片评价题目 {question_number}")
            logger.info("✅ 设置题目标题成功")
            time.sleep(2)

        # 4. 查找评价对象设置
        evaluation_object_selectors = [
            "//span[contains(text(), '评价对象设置')]",
            "//button[contains(text(), '评价对象')]",
            "//div[contains(text(), '评价对象')]",
            "//*[contains(text(), '对象设置')]"
        ]

        eval_btn = find_element_by_multiple_selectors(wait, evaluation_object_selectors)
        if eval_btn:
            eval_btn.click()
            logger.info("✅ 进入评价对象设置成功")
            time.sleep(3)

        # 5. 选择图片类型
        image_type_selectors = [
            "//div[contains(text(), '图片')]",
            "//span[contains(text(), '图片')]",
            "//li[contains(text(), '图片')]"
        ]

        image_btn = find_element_by_multiple_selectors(wait, image_type_selectors)
        if image_btn:
            image_btn.click()
            logger.info("✅ 选择图片类型成功")
            time.sleep(3)

        # 6. 测试上传图片（只上传前3张进行测试）
        test_upload_images(driver, wait, logger, image_paths[:3])

        # 7. 保存题目
        save_selectors = [
            "//span[contains(text(), '保存')]",
            "//button[contains(text(), '保存')]",
            "//div[contains(text(), '保存')]"
        ]

        save_btn = find_element_by_multiple_selectors(wait, save_selectors)
        if save_btn:
            save_btn.click()
            logger.info("✅ 保存题目成功")
            time.sleep(3)

        logger.info(f"✅ 测试题目 {question_number} 创建完成")
        return True

    except Exception as e:
        logger.error(f"❌ 创建测试题目失败: {e}")
        driver.save_screenshot(f"error_test_q{question_number}_{datetime.now().strftime('%H%M%S')}.png")
        return False

def test_upload_images(driver, wait, logger, image_paths):
    """测试上传图片功能"""
    try:
        logger.info(f"开始测试上传 {len(image_paths)} 张图片...")

        for i, image_path in enumerate(image_paths):
            logger.info(f"测试上传第 {i+1} 张图片: {os.path.basename(image_path)}")

            # 查找上传按钮
            upload_selectors = [
                "//span[contains(text(), '上传图片')]",
                "//button[contains(text(), '上传')]",
                "//div[contains(text(), '上传')]",
                "//*[contains(@class, 'upload')]"
            ]

            upload_btn = find_element_by_multiple_selectors(wait, upload_selectors)
            if upload_btn:
                upload_btn.click()
                time.sleep(2)

                # 查找文件输入
                file_inputs = driver.find_elements(By.XPATH, "//input[@type='file']")
                if file_inputs:
                    file_inputs[0].send_keys(image_path)
                    logger.info(f"✅ 图片 {i+1} 上传成功")
                    time.sleep(3)

                    # 确认上传
                    confirm_btns = driver.find_elements(By.XPATH, "//span[contains(text(), '确定')]")
                    if confirm_btns:
                        confirm_btns[0].click()
                        time.sleep(2)
                else:
                    logger.warning(f"⚠️ 找不到文件输入框")
            else:
                logger.warning(f"⚠️ 找不到上传按钮")

        logger.info("✅ 图片上传测试完成")

    except Exception as e:
        logger.error(f"❌ 图片上传测试失败: {e}")

def main():
    """主测试函数"""
    print("🧪 问卷星自动上传测试工具")
    print("=" * 50)

    # 配置
    image_folder = r"D:\1a_taohuacun"
    questionnaire_url = "https://www.wjx.cn/wjxdesignnew/designnew.aspx?version=7&openType=redesign&curid=316618829&nqt=&sguid=8d03ce0d-b368-47b9-8646-e129a13932f2"

    # 设置日志
    logger = setup_logging()

    # 检查图片文件夹
    if not os.path.exists(image_folder):
        print(f"❌ 图片文件夹不存在: {image_folder}")
        return

    # 加载测试图片
    test_images = load_test_images(image_folder, 20)
    if len(test_images) < 10:
        print(f"❌ 图片数量不足，需要至少10张，当前只有{len(test_images)}张")
        return

    print(f"📁 图片文件夹: {image_folder}")
    print(f"🖼️ 测试图片数量: {len(test_images)}")
    print(f"🌐 问卷地址: {questionnaire_url}")

    # 确认执行
    confirm = input("\n确认开始测试? (y/n): ").lower()
    if confirm != 'y':
        print("测试已取消")
        return

    # 设置浏览器
    driver, wait = setup_driver()
    if not driver:
        return

    try:
        # 打开页面
        logger.info("正在打开问卷编辑页面...")
        driver.get(questionnaire_url)
        time.sleep(5)

        # 检查登录
        if "login" in driver.current_url.lower():
            logger.info("需要手动登录...")
            input("请在浏览器中完成登录，然后按回车键继续...")
            driver.get(questionnaire_url)
            time.sleep(3)

        # 测试页面元素
        if not test_page_elements(driver, wait, logger):
            logger.error("页面元素测试失败")
            return

        # 创建测试题目
        logger.info("开始创建测试题目...")

        # 测试创建1题
        success = create_test_question(driver, wait, logger, 1, test_images[:10])

        if success:
            logger.info("🎉 测试成功！脚本功能正常")
            print("\n🎉 测试成功！")
            print("现在可以运行完整版本的脚本")
        else:
            logger.error("❌ 测试失败")
            print("\n❌ 测试失败，请检查日志文件")

        input("\n按回车键关闭浏览器...")

    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        print(f"\n❌ 测试失败: {e}")

    finally:
        if driver:
            driver.quit()
            logger.info("浏览器已关闭")

if __name__ == "__main__":
    main()
