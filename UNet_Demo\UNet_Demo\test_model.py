"""
测试最终模型并可视化结果
"""
import os
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from tqdm import tqdm
import albumentations as A
from albumentations.pytorch import ToTensorV2
import argparse
import yaml

from nets.deeplabv3plus import unet
from utils.utils_metrics import compute_mIoU_tensor

# 颜色映射，用于可视化
def get_color_map(num_classes=29):
    """
    生成颜色映射，用于可视化分割结果
    """
    color_map = np.zeros((num_classes, 3), dtype=np.uint8)
    for i in range(num_classes):
        r = np.random.randint(0, 256)
        g = np.random.randint(0, 256)
        b = np.random.randint(0, 256)
        color_map[i] = [r, g, b]
    return color_map

def load_model(model_path, config_path, device):
    """
    加载模型

    参数:
    - model_path: 模型权重路径
    - config_path: 配置文件路径
    - device: 设备

    返回:
    - model: 加载的模型
    """
    # 加载配置
    with open(config_path, 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)

    # 创建模型
    model = unet(num_classes=cfg['data']['num_classes'],
                 backbone=cfg['model']['backbone'],
                 pretrained=False)

    # 加载权重
    checkpoint = torch.load(model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)

    model = model.to(device)
    model.eval()

    return model, cfg

def predict_image(model, image_path, cfg, device):
    """
    预测单张图像

    参数:
    - model: 模型
    - image_path: 图像路径
    - cfg: 配置
    - device: 设备

    返回:
    - pred: 预测结果
    - img: 原始图像
    """
    # 读取图像
    img = cv2.imread(image_path)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    h, w = img.shape[:2]

    # 预处理
    transform = A.Compose([
        A.Resize(height=cfg['data']['input_size'][0], width=cfg['data']['input_size'][1]),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ])

    augmented = transform(image=img)
    img_tensor = augmented['image'].unsqueeze(0).to(device)

    # 预测
    with torch.no_grad():
        output = model(img_tensor)
        pred = torch.argmax(output, dim=1).squeeze().cpu().numpy()

    # 调整大小为原始尺寸
    pred = cv2.resize(pred.astype(np.uint8), (w, h), interpolation=cv2.INTER_NEAREST)

    return pred, img

def visualize_prediction(img, pred, color_map, save_path=None):
    """
    可视化预测结果

    参数:
    - img: 原始图像
    - pred: 预测结果
    - color_map: 颜色映射
    - save_path: 保存路径
    """
    # 创建彩色分割图
    seg_color = np.zeros((pred.shape[0], pred.shape[1], 3), dtype=np.uint8)
    for c in range(len(color_map)):
        seg_color[pred == c] = color_map[c]

    # 创建叠加图
    alpha = 0.5
    overlay = cv2.addWeighted(img, 1 - alpha, seg_color, alpha, 0)

    # 显示结果
    plt.figure(figsize=(15, 5))

    plt.subplot(1, 3, 1)
    plt.imshow(img)
    plt.title('Original Image')
    plt.axis('off')

    plt.subplot(1, 3, 2)
    plt.imshow(seg_color)
    plt.title('Segmentation')
    plt.axis('off')

    plt.subplot(1, 3, 3)
    plt.imshow(overlay)
    plt.title('Overlay')
    plt.axis('off')

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path)
        plt.close()
    else:
        plt.show()

def evaluate_model(model, test_dir, gt_dir, cfg, device, color_map, output_dir='results'):
    """
    评估模型

    参数:
    - model: 模型
    - test_dir: 测试图像目录
    - gt_dir: 真实标签目录
    - cfg: 配置
    - device: 设备
    - color_map: 颜色映射
    - output_dir: 输出目录

    返回:
    - miou: 平均IoU
    """
    os.makedirs(output_dir, exist_ok=True)

    # 获取测试图像列表
    test_images = [f for f in os.listdir(test_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]

    # 预测所有图像
    preds = []
    gts = []

    for img_file in tqdm(test_images, desc="Evaluating"):
        # 预测
        img_path = os.path.join(test_dir, img_file)
        pred, img = predict_image(model, img_path, cfg, device)
        preds.append(pred)

        # 读取真实标签
        gt_path = os.path.join(gt_dir, os.path.splitext(img_file)[0] + '.png')
        if os.path.exists(gt_path):
            gt = cv2.imread(gt_path, cv2.IMREAD_GRAYSCALE)
            gts.append(gt)

            # 可视化
            save_path = os.path.join(output_dir, os.path.splitext(img_file)[0] + '_result.png')
            visualize_prediction(img, pred, color_map, save_path)

    # 计算mIoU
    if gts:
        # 将预测和真实标签转换为tensor
        preds_tensor = torch.tensor(preds)
        gts_tensor = torch.tensor(gts)

        # 计算mIoU
        miou, per_class_ious = compute_mIoU_tensor(preds_tensor, gts_tensor, cfg['data']['num_classes'], return_per_class=True)

        # 打印总体mIoU
        print(f"mIoU: {miou:.4f}")

        # 打印每个类别的IoU
        print("\n每个类别的IoU:")
        for i, iou in enumerate(per_class_ious):
            print(f"  class_{i}: {iou:.4f}")

        return miou
    else:
        print("No ground truth labels found for evaluation.")
        return None

def main():
    parser = argparse.ArgumentParser(description="Test segmentation model")
    parser.add_argument('--model', type=str, default='logs/best_model.pth', help='Path to model weights')
    parser.add_argument('--config', type=str, default='config.yaml', help='Path to config file')
    parser.add_argument('--test_dir', type=str, default='VOCdevkit/VOC2025/JPEGImages', help='Test images directory')
    parser.add_argument('--gt_dir', type=str, default='VOCdevkit/VOC2025/SegmentationClass', help='Ground truth directory')
    parser.add_argument('--output_dir', type=str, default='results', help='Output directory')
    args = parser.parse_args()

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 加载模型
    model, cfg = load_model(args.model, args.config, device)
    print(f"Model loaded from {args.model}")

    # 生成颜色映射
    color_map = get_color_map(cfg['data']['num_classes'])

    # 评估模型
    evaluate_model(model, args.test_dir, args.gt_dir, cfg, device, color_map, args.output_dir)

if __name__ == '__main__':
    main()
