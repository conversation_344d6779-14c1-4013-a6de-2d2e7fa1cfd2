#!/usr/bin/env python3
"""
快速测试立即改进方案
验证新的损失函数、类别权重和数据增强是否正常工作
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_tversky_loss():
    """测试Tversky损失函数"""
    print("测试Tversky损失函数...")
    
    from utils.losses import TverskyLoss
    
    # 创建测试数据
    batch_size, num_classes, height, width = 2, 29, 64, 64
    pred = torch.randn(batch_size, num_classes, height, width)
    target = torch.randint(0, num_classes, (batch_size, height, width))
    
    # 创建Tversky损失
    tversky_loss = TverskyLoss(alpha=0.3, beta=0.7)
    
    # 计算损失
    loss = tversky_loss(pred, target)
    
    print(f"Tversky损失值: {loss.item():.4f}")
    print("✓ Tversky损失函数测试通过")
    return True

def test_extreme_class_weights():
    """测试极端类别权重计算"""
    print("\n测试极端类别权重计算...")
    
    from utils.class_weights import compute_class_weights
    
    # 模拟样本数据
    samples_per_class = [10000, 5000, 3000, 2000, 1000, 500, 200, 100, 50, 20, 10, 5, 2, 1] + [100] * 15
    
    try:
        # 测试极端平衡方法
        weights = compute_class_weights(
            dataset_path=".",
            num_classes=29,
            method="extreme_balance",
            samples_per_class=samples_per_class
        )
        
        print(f"极端权重 (前10个类别): {weights[:10].tolist()}")
        print(f"困难类别权重 (类别8, 18, 28): {weights[8]:.2f}, {weights[18]:.2f}, {weights[28]:.2f}")
        print("✓ 极端类别权重计算测试通过")
        return True
    except Exception as e:
        print(f"✗ 极端类别权重计算测试失败: {e}")
        return False

def test_combined_loss():
    """测试组合损失函数"""
    print("\n测试组合损失函数...")
    
    from utils.losses import CombinedLoss
    
    # 创建测试配置
    loss_config = {
        'use_ce': True,
        'use_dice': True,
        'use_focal': True,
        'use_lovasz': True,
        'use_tversky': True,
        'weight_ce': 0.3,
        'weight_dice': 1.5,
        'weight_focal': 2.0,
        'weight_lovasz': 1.8,
        'weight_tversky': 1.2,
        'focal_gamma': 3.0,
        'focal_alpha': 0.75,
        'tversky_alpha': 0.3,
        'tversky_beta': 0.7,
        'label_smoothing': 0.1,
        'ignore_index': 255
    }
    
    # 创建类别权重
    class_weights = torch.ones(29)
    class_weights[8] = 20.0  # 困难类别高权重
    class_weights[18] = 15.0
    class_weights[28] = 12.0
    
    # 创建组合损失
    criterion = CombinedLoss(loss_config, class_weights)
    
    # 创建测试数据
    batch_size, num_classes, height, width = 2, 29, 64, 64
    pred = torch.randn(batch_size, num_classes, height, width)
    target = torch.randint(0, num_classes, (batch_size, height, width))
    
    # 计算损失
    try:
        loss = criterion(pred, target)
        print(f"组合损失值: {loss.item():.4f}")
        print("✓ 组合损失函数测试通过")
        return True
    except Exception as e:
        print(f"✗ 组合损失函数测试失败: {e}")
        return False

def test_config_loading():
    """测试配置文件加载"""
    print("\n测试配置文件加载...")
    
    config_path = "config_immediate_improvements.yaml"
    
    if not os.path.exists(config_path):
        print(f"✗ 配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查关键配置项
        required_keys = ['data', 'model', 'training', 'loss', 'class_optimization']
        for key in required_keys:
            if key not in config:
                print(f"✗ 配置文件缺少关键项: {key}")
                return False
        
        print("✓ 配置文件加载测试通过")
        print(f"  - 数据类别数: {config['data']['num_classes']}")
        print(f"  - 训练轮数: {config['training']['total_epochs']}")
        print(f"  - 批次大小: {config['training']['batch_size']}")
        print(f"  - 困难类别: {config['class_optimization']['difficult_classes']}")
        return True
    except Exception as e:
        print(f"✗ 配置文件加载测试失败: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n测试模型创建...")
    
    try:
        from nets.unet import Unet
        
        # 创建模型
        model = Unet(
            num_classes=29,
            pretrained=False,  # 测试时不使用预训练权重
            backbone='resnet50'
        )
        
        # 测试前向传播
        batch_size, channels, height, width = 2, 3, 256, 256
        x = torch.randn(batch_size, channels, height, width)
        
        with torch.no_grad():
            output = model(x)
        
        expected_shape = (batch_size, 29, height, width)
        if output.shape == expected_shape:
            print(f"✓ 模型创建测试通过，输出形状: {output.shape}")
            return True
        else:
            print(f"✗ 模型输出形状错误，期望: {expected_shape}, 实际: {output.shape}")
            return False
    except Exception as e:
        print(f"✗ 模型创建测试失败: {e}")
        return False

def test_data_loading():
    """测试数据加载"""
    print("\n测试数据加载...")
    
    # 检查数据文件是否存在
    train_annotation = "VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt"
    
    if not os.path.exists(train_annotation):
        print(f"✗ 训练数据注释文件不存在: {train_annotation}")
        return False
    
    try:
        from utils.dataloader import SegmentationDataset
        
        # 创建数据集（只测试前几个样本）
        dataset = SegmentationDataset(
            annotation_path=train_annotation,
            input_shape=[256, 256],
            num_classes=29,
            train=True
        )
        
        if len(dataset) > 0:
            # 测试获取一个样本
            image, mask = dataset[0]
            print(f"✓ 数据加载测试通过")
            print(f"  - 数据集大小: {len(dataset)}")
            print(f"  - 图像形状: {image.shape}")
            print(f"  - 掩码形状: {mask.shape}")
            print(f"  - 掩码唯一值: {torch.unique(mask)[:10].tolist()}...")
            return True
        else:
            print("✗ 数据集为空")
            return False
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 50)
    print("立即改进方案 - 快速测试")
    print("=" * 50)
    
    tests = [
        ("Tversky损失函数", test_tversky_loss),
        ("极端类别权重", test_extreme_class_weights),
        ("组合损失函数", test_combined_loss),
        ("配置文件加载", test_config_loading),
        ("模型创建", test_model_creation),
        ("数据加载", test_data_loading),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name}测试出现异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以开始训练。")
        print("\n启动训练命令:")
        print("python train_immediate_improvements.py")
        print("或运行: start_immediate_improvements.bat")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
