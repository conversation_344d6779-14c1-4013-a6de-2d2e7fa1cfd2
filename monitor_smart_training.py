#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import time
import glob
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def get_latest_log():
    """获取最新的训练日志"""
    log_dirs = glob.glob('logs/smart_optimized_*')
    if not log_dirs:
        return None
    
    latest_dir = max(log_dirs, key=os.path.getctime)
    log_file = os.path.join(latest_dir, 'training.log')
    
    if os.path.exists(log_file):
        return log_file
    return None

def parse_training_log(log_file):
    """解析训练日志"""
    epochs = []
    train_losses = []
    val_losses = []
    val_mious = []
    stages = []
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            if 'Global_Epoch' in line and 'train_loss=' in line:
                try:
                    # 解析epoch
                    epoch_part = line.split('Global_Epoch ')[1].split(' ')[0]
                    epoch = int(epoch_part)
                    
                    # 解析stage
                    stage_part = line.split('(')[1].split(')')[0]
                    
                    # 解析损失和mIoU
                    train_loss = float(line.split('train_loss=')[1].split(',')[0])
                    val_loss = float(line.split('val_loss=')[1].split(',')[0])
                    val_miou = float(line.split('val_miou=')[1].split(',')[0])
                    
                    epochs.append(epoch)
                    train_losses.append(train_loss)
                    val_losses.append(val_loss)
                    val_mious.append(val_miou)
                    stages.append(stage_part)
                    
                except:
                    continue
    except:
        pass
    
    return epochs, train_losses, val_losses, val_mious, stages

def get_best_models():
    """获取最佳模型信息"""
    model_files = glob.glob('best_smart_optimized_miou_*.pth')
    if not model_files:
        return None, None
    
    # 提取mIoU值并排序
    model_scores = []
    for file in model_files:
        try:
            miou_str = file.split('_')[-1].replace('.pth', '')
            miou = float(miou_str)
            model_scores.append((miou, file))
        except:
            continue
    
    if not model_scores:
        return None, None
    
    model_scores.sort(reverse=True)
    best_miou, best_file = model_scores[0]
    
    return best_miou, len(model_scores)

def create_progress_visualization(epochs, train_losses, val_losses, val_mious, stages):
    """创建训练进度可视化"""
    if not epochs:
        return
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('🚀 智能优化训练实时监控', fontsize=16, fontweight='bold')
    
    # 训练和验证损失
    ax1.plot(epochs, train_losses, 'b-', label='训练损失', linewidth=2)
    ax1.plot(epochs, val_losses, 'r-', label='验证损失', linewidth=2)
    ax1.set_xlabel('轮次')
    ax1.set_ylabel('损失')
    ax1.set_title('📉 损失变化趋势')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # mIoU趋势
    ax2.plot(epochs, val_mious, 'g-', linewidth=3, marker='o', markersize=4)
    ax2.axhline(y=0.45, color='red', linestyle='--', alpha=0.7, label='目标 (0.45)')
    ax2.axhline(y=0.40, color='orange', linestyle='--', alpha=0.7, label='里程碑 (0.40)')
    ax2.axhline(y=0.35, color='yellow', linestyle='--', alpha=0.7, label='进展 (0.35)')
    ax2.set_xlabel('轮次')
    ax2.set_ylabel('mIoU')
    ax2.set_title('📈 mIoU提升趋势')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 阶段分布
    stage_colors = {'Stage1_Gentle': 'lightblue', 'Stage2_Normal': 'lightgreen', 'Stage3_Enhanced': 'lightcoral'}
    unique_stages = list(set(stages))
    stage_counts = [stages.count(stage) for stage in unique_stages]
    
    ax3.pie(stage_counts, labels=unique_stages, autopct='%1.1f%%', 
            colors=[stage_colors.get(stage, 'gray') for stage in unique_stages])
    ax3.set_title('🎯 训练阶段分布')
    
    # 性能统计
    if val_mious:
        current_miou = val_mious[-1]
        max_miou = max(val_mious)
        improvement = ((current_miou - val_mious[0]) / val_mious[0]) * 100 if val_mious[0] > 0 else 0
        
        stats_text = f"""
📊 当前统计:
• 当前mIoU: {current_miou:.4f}
• 最佳mIoU: {max_miou:.4f}
• 总提升: {improvement:.1f}%
• 目标完成: {(current_miou/0.45)*100:.1f}%
• 训练轮次: {len(epochs)}

🎯 目标进度:
• 目标: 0.45
• 差距: {0.45-current_miou:.4f}
• 预计完成: {'已达成' if current_miou >= 0.45 else f'{((0.45-current_miou)/(max(val_mious[-5:]) - min(val_mious[-5:]) + 1e-6) * 5):.0f}轮后' if len(val_mious) >= 5 else '计算中...'}
        """
        
        ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')
        ax4.set_title('📋 实时统计')
    
    plt.tight_layout()
    plt.savefig(f'smart_training_progress_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png', 
                dpi=300, bbox_inches='tight')
    plt.close()

def print_progress_summary(epochs, train_losses, val_losses, val_mious, stages):
    """打印进度摘要"""
    if not epochs:
        print("❌ 暂无训练数据")
        return
    
    current_epoch = epochs[-1]
    current_miou = val_mious[-1]
    current_train_loss = train_losses[-1]
    current_val_loss = val_losses[-1]
    current_stage = stages[-1]
    
    # 计算改善
    if len(val_mious) >= 2:
        recent_improvement = val_mious[-1] - val_mious[-2]
        total_improvement = val_mious[-1] - val_mious[0]
    else:
        recent_improvement = 0
        total_improvement = 0
    
    print(f"\n🚀 智能优化训练实时状态")
    print("=" * 60)
    print(f"📊 当前进度: 第 {current_epoch} 轮 ({current_stage})")
    print(f"🎯 当前mIoU: {current_miou:.4f}")
    print(f"📉 训练损失: {current_train_loss:.4f}")
    print(f"📈 验证损失: {current_val_loss:.4f}")
    print(f"⚡ 最近改善: {recent_improvement:+.4f}")
    print(f"🚀 总体改善: {total_improvement:+.4f}")
    
    # 目标进度
    target_progress = (current_miou / 0.45) * 100
    print(f"🎯 目标完成度: {target_progress:.1f}%")
    
    if current_miou >= 0.45:
        print("🏆🏆🏆 恭喜！已达到目标！🏆🏆🏆")
    elif current_miou >= 0.40:
        print("🎯 接近目标，继续加油！")
    elif current_miou >= 0.35:
        print("✅ 进展良好，继续努力！")
    elif current_miou >= 0.30:
        print("💪 稳步提升中...")
    else:
        print("🚀 训练进行中...")
    
    # 阶段统计
    stage_counts = {}
    for stage in stages:
        stage_counts[stage] = stage_counts.get(stage, 0) + 1
    
    print(f"\n📋 阶段统计:")
    for stage, count in stage_counts.items():
        print(f"  {stage}: {count} 轮")

def main():
    """主监控函数"""
    print("🔍 智能优化训练监控器启动")
    print("=" * 50)
    
    while True:
        try:
            # 获取最新日志
            log_file = get_latest_log()
            if not log_file:
                print("⏳ 等待训练日志生成...")
                time.sleep(10)
                continue
            
            # 解析日志
            epochs, train_losses, val_losses, val_mious, stages = parse_training_log(log_file)
            
            if not epochs:
                print("⏳ 等待训练数据...")
                time.sleep(10)
                continue
            
            # 清屏并显示进度
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # 打印进度摘要
            print_progress_summary(epochs, train_losses, val_losses, val_mious, stages)
            
            # 获取最佳模型信息
            best_miou, model_count = get_best_models()
            if best_miou:
                print(f"\n🏆 最佳模型: mIoU {best_miou:.4f} (共保存 {model_count} 个模型)")
            
            # 创建可视化
            if len(epochs) >= 2:
                create_progress_visualization(epochs, train_losses, val_losses, val_mious, stages)
                print(f"📊 进度图表已保存")
            
            print(f"\n⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 60)
            
            # 检查是否达到目标
            if val_mious and val_mious[-1] >= 0.45:
                print("🎊 训练目标已达成！监控结束。")
                break
            
            # 等待下次更新
            time.sleep(30)  # 每30秒更新一次
            
        except KeyboardInterrupt:
            print("\n👋 监控器已停止")
            break
        except Exception as e:
            print(f"❌ 监控出错: {e}")
            time.sleep(10)

if __name__ == "__main__":
    main()
