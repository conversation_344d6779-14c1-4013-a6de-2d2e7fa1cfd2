#!/usr/bin/env python3
"""
快速测试后处理效果
"""

import os
import sys
import torch
import numpy as np
import logging
from datetime import datetime

# 添加路径
sys.path.append('UNet_Demo/UNet_Demo')

from UNet_Demo.UNet_Demo.advanced_postprocessing import AdvancedPostProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def quick_test():
    """快速测试后处理功能"""
    logger.info("🔬 快速测试后处理功能")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建后处理器
    postprocessor = AdvancedPostProcessor(num_classes=29, device=device)
    
    # 创建模拟数据
    batch_size = 2
    num_classes = 29
    height, width = 512, 512
    
    # 模拟模型输出 (logits)
    logits = torch.randn(batch_size, num_classes, height, width).to(device)
    
    # 模拟真实标签
    targets = torch.randint(0, num_classes, (batch_size, height, width)).to(device)
    
    logger.info(f"输入形状: {logits.shape}")
    logger.info(f"目标形状: {targets.shape}")
    
    # 测试基础预测
    with torch.no_grad():
        basic_pred = torch.argmax(logits, dim=1)
        logger.info(f"基础预测形状: {basic_pred.shape}")
        
        # 测试后处理
        logger.info("开始后处理...")
        
        processed_results = []
        for b in range(batch_size):
            try:
                processed_pred, confidence_map = postprocessor.process_prediction(
                    logits[b], 
                    original_size=None
                )
                processed_results.append(processed_pred)
                logger.info(f"样本 {b+1} 后处理成功")
                logger.info(f"  - 预测形状: {processed_pred.shape}")
                logger.info(f"  - 置信度图形状: {confidence_map.shape}")
                logger.info(f"  - 预测类别数: {len(np.unique(processed_pred))}")
                
            except Exception as e:
                logger.error(f"样本 {b+1} 后处理失败: {e}")
                import traceback
                traceback.print_exc()
        
        if processed_results:
            logger.info("✅ 后处理功能测试成功!")
            
            # 测试各个组件
            logger.info("\n测试各个后处理组件:")
            
            # 测试置信度过滤
            try:
                probs = torch.softmax(logits[0], dim=0)
                filtered_probs, conf_map = postprocessor._confidence_filtering(probs)
                logger.info("✅ 置信度过滤测试成功")
            except Exception as e:
                logger.error(f"❌ 置信度过滤测试失败: {e}")
            
            # 测试多尺度融合
            try:
                probs = torch.softmax(logits[0], dim=0)
                fused_probs = postprocessor._multi_scale_fusion(probs)
                logger.info("✅ 多尺度融合测试成功")
            except Exception as e:
                logger.error(f"❌ 多尺度融合测试失败: {e}")
            
            # 测试形态学操作
            try:
                pred = torch.argmax(logits[0], dim=0).cpu().numpy()
                morph_pred = postprocessor._morphological_operations(pred)
                logger.info("✅ 形态学操作测试成功")
            except Exception as e:
                logger.error(f"❌ 形态学操作测试失败: {e}")
            
            # 测试连通组件分析
            try:
                pred = torch.argmax(logits[0], dim=0).cpu().numpy()
                cc_pred = postprocessor._connected_components_analysis(pred)
                logger.info("✅ 连通组件分析测试成功")
            except Exception as e:
                logger.error(f"❌ 连通组件分析测试失败: {e}")
            
        else:
            logger.error("❌ 后处理功能测试失败!")

def test_postprocessing_configs():
    """测试不同的后处理配置"""
    logger.info("\n🔧 测试不同的后处理配置")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    configs = [
        {
            'name': '仅TTA',
            'config': {
                'use_tta': True,
                'use_morphology': False,
                'use_connected_components': False,
                'use_watershed': False,
                'use_boundary_refinement': False,
                'use_multi_scale_fusion': False,
                'use_confidence_filtering': False,
            }
        },
        {
            'name': '仅形态学',
            'config': {
                'use_tta': False,
                'use_morphology': True,
                'use_connected_components': False,
                'use_watershed': False,
                'use_boundary_refinement': False,
                'use_multi_scale_fusion': False,
                'use_confidence_filtering': False,
            }
        },
        {
            'name': '轻量级组合',
            'config': {
                'use_tta': True,
                'use_morphology': True,
                'use_connected_components': True,
                'use_watershed': False,
                'use_boundary_refinement': False,
                'use_multi_scale_fusion': False,
                'use_confidence_filtering': True,
            }
        },
        {
            'name': '完整后处理',
            'config': {
                'use_tta': True,
                'use_morphology': True,
                'use_connected_components': True,
                'use_watershed': True,
                'use_boundary_refinement': True,
                'use_multi_scale_fusion': True,
                'use_confidence_filtering': True,
            }
        }
    ]
    
    # 创建测试数据
    logits = torch.randn(1, 29, 256, 256).to(device)  # 较小尺寸以加快测试
    
    for config_info in configs:
        logger.info(f"\n测试配置: {config_info['name']}")
        
        try:
            postprocessor = AdvancedPostProcessor(num_classes=29, device=device)
            postprocessor.config.update(config_info['config'])
            
            start_time = datetime.now()
            processed_pred, confidence_map = postprocessor.process_prediction(logits[0])
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            
            logger.info(f"  ✅ 成功 - 处理时间: {processing_time:.2f}秒")
            logger.info(f"  - 输出形状: {processed_pred.shape}")
            logger.info(f"  - 预测类别数: {len(np.unique(processed_pred))}")
            
        except Exception as e:
            logger.error(f"  ❌ 失败: {e}")

def analyze_current_model_potential():
    """分析当前模型的后处理潜力"""
    logger.info("\n📊 分析当前模型的后处理潜力")
    
    # 查找最佳模型
    model_files = [f for f in os.listdir('.') if f.startswith('best_smart_optimized_miou_')]
    if not model_files:
        logger.warning("未找到智能优化模型文件")
        return
    
    # 按mIoU排序
    model_files.sort(key=lambda x: float(x.split('_')[-1].replace('.pth', '')), reverse=True)
    best_model_file = model_files[0]
    current_miou = float(best_model_file.split('_')[-1].replace('.pth', ''))
    
    logger.info(f"当前最佳模型: {best_model_file}")
    logger.info(f"当前mIoU: {current_miou:.4f}")
    
    # 预估后处理可能的改善
    potential_improvements = {
        'TTA (测试时增强)': 0.005,  # 通常能提升0.5%
        '形态学操作': 0.003,        # 通常能提升0.3%
        '连通组件分析': 0.002,      # 通常能提升0.2%
        '置信度过滤': 0.002,        # 通常能提升0.2%
        '多尺度融合': 0.004,        # 通常能提升0.4%
        '边界细化': 0.003,          # 通常能提升0.3%
    }
    
    logger.info("\n预估后处理改善潜力:")
    total_potential = 0
    for technique, improvement in potential_improvements.items():
        total_potential += improvement
        new_miou = current_miou + improvement
        logger.info(f"  {technique:15s}: +{improvement:.3f} -> {new_miou:.4f}")
    
    max_potential_miou = current_miou + total_potential * 0.7  # 考虑技术间的重叠
    logger.info(f"\n综合后处理潜在mIoU: {max_potential_miou:.4f}")
    logger.info(f"潜在总改善: +{max_potential_miou - current_miou:.4f}")
    
    # 目标分析
    target_miou = 0.45
    if max_potential_miou >= target_miou:
        logger.info(f"🎯 有望通过后处理达到目标mIoU {target_miou:.2f}!")
        gap = target_miou - current_miou
        postprocess_contribution = max_potential_miou - current_miou
        logger.info(f"  需要弥补差距: {gap:.4f}")
        logger.info(f"  后处理可贡献: {postprocess_contribution:.4f}")
    else:
        logger.info(f"⚠️  仅通过后处理可能无法达到目标mIoU {target_miou:.2f}")
        logger.info(f"  还需要其他优化手段")

def main():
    logger.info("🚀 开始后处理功能测试")
    logger.info("=" * 50)
    
    # 基础功能测试
    quick_test()
    
    # 配置测试
    test_postprocessing_configs()
    
    # 潜力分析
    analyze_current_model_potential()
    
    logger.info(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
