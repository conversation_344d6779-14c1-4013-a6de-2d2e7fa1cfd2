#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型集成学习模块
通过集成多个模型的预测结果来提升性能
目标：突破mIoU 0.4，达到学术论文质量
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import os
from typing import List, Dict, Tuple
import logging
from collections import defaultdict

class ModelEnsemble:
    """模型集成类"""
    
    def __init__(self, models: List[nn.Module], weights: List[float] = None, device='cuda'):
        """
        初始化模型集成
        
        Args:
            models: 模型列表
            weights: 模型权重列表，如果为None则使用等权重
            device: 设备
        """
        self.models = models
        self.device = device
        self.num_models = len(models)
        
        # 设置模型权重
        if weights is None:
            self.weights = [1.0 / self.num_models] * self.num_models
        else:
            assert len(weights) == self.num_models, "权重数量必须与模型数量相同"
            # 归一化权重
            total_weight = sum(weights)
            self.weights = [w / total_weight for w in weights]
        
        # 将所有模型移到指定设备并设为评估模式
        for model in self.models:
            model.to(device)
            model.eval()
        
        logging.info(f"初始化模型集成，包含{self.num_models}个模型")
        logging.info(f"模型权重: {self.weights}")
    
    def predict(self, x: torch.Tensor, use_tta: bool = False) -> torch.Tensor:
        """
        集成预测
        
        Args:
            x: 输入图像 [B, C, H, W]
            use_tta: 是否使用测试时增强
            
        Returns:
            集成预测结果 [B, num_classes, H, W]
        """
        predictions = []
        
        with torch.no_grad():
            for i, model in enumerate(self.models):
                if use_tta:
                    pred = self._predict_with_tta(model, x)
                else:
                    pred = model(x)
                
                # 应用softmax获得概率分布
                pred = F.softmax(pred, dim=1)
                
                # 应用权重
                pred = pred * self.weights[i]
                predictions.append(pred)
        
        # 集成预测结果
        ensemble_pred = torch.stack(predictions).sum(dim=0)
        
        return ensemble_pred
    
    def _predict_with_tta(self, model: nn.Module, x: torch.Tensor) -> torch.Tensor:
        """
        使用测试时增强进行预测
        
        Args:
            model: 模型
            x: 输入图像
            
        Returns:
            TTA预测结果
        """
        predictions = []
        
        # 原始图像
        pred = model(x)
        predictions.append(pred)
        
        # 水平翻转
        x_flip = torch.flip(x, dims=[3])
        pred_flip = model(x_flip)
        pred_flip = torch.flip(pred_flip, dims=[3])
        predictions.append(pred_flip)
        
        # 垂直翻转
        x_vflip = torch.flip(x, dims=[2])
        pred_vflip = model(x_vflip)
        pred_vflip = torch.flip(pred_vflip, dims=[2])
        predictions.append(pred_vflip)
        
        # 多尺度预测
        scales = [0.75, 1.25]
        original_size = x.shape[-2:]
        
        for scale in scales:
            new_size = [int(s * scale) for s in original_size]
            x_scaled = F.interpolate(x, size=new_size, mode='bilinear', align_corners=False)
            pred_scaled = model(x_scaled)
            pred_scaled = F.interpolate(pred_scaled, size=original_size, mode='bilinear', align_corners=False)
            predictions.append(pred_scaled)
        
        # 平均所有预测
        tta_pred = torch.stack(predictions).mean(dim=0)
        
        return tta_pred
    
    def evaluate(self, dataloader, num_classes: int) -> Dict[str, float]:
        """
        评估集成模型性能
        
        Args:
            dataloader: 数据加载器
            num_classes: 类别数量
            
        Returns:
            评估指标字典
        """
        total_correct = 0
        total_pixels = 0
        class_correct = torch.zeros(num_classes)
        class_total = torch.zeros(num_classes)
        intersection = torch.zeros(num_classes)
        union = torch.zeros(num_classes)
        
        with torch.no_grad():
            for images, targets in dataloader:
                images = images.to(self.device)
                targets = targets.to(self.device)
                
                # 集成预测
                predictions = self.predict(images, use_tta=True)
                pred_labels = torch.argmax(predictions, dim=1)
                
                # 计算指标
                correct = (pred_labels == targets)
                total_correct += correct.sum().item()
                total_pixels += targets.numel()
                
                # 计算每个类别的指标
                for c in range(num_classes):
                    class_mask = (targets == c)
                    pred_mask = (pred_labels == c)
                    
                    class_correct[c] += (correct & class_mask).sum().item()
                    class_total[c] += class_mask.sum().item()
                    
                    # IoU计算
                    intersection[c] += (pred_mask & class_mask).sum().item()
                    union[c] += (pred_mask | class_mask).sum().item()
        
        # 计算最终指标
        pixel_accuracy = total_correct / total_pixels
        
        # 类别准确率
        class_accuracy = class_correct / (class_total + 1e-8)
        mean_class_accuracy = class_accuracy.mean().item()
        
        # IoU和mIoU
        iou = intersection / (union + 1e-8)
        miou = iou.mean().item()
        
        return {
            'pixel_accuracy': pixel_accuracy,
            'mean_class_accuracy': mean_class_accuracy,
            'miou': miou,
            'class_iou': iou.tolist()
        }

class EnsembleTrainer:
    """集成训练器"""
    
    def __init__(self, base_config: dict, ensemble_configs: List[dict]):
        """
        初始化集成训练器
        
        Args:
            base_config: 基础配置
            ensemble_configs: 集成模型配置列表
        """
        self.base_config = base_config
        self.ensemble_configs = ensemble_configs
        self.trained_models = []
        
    def train_individual_models(self):
        """训练单个模型"""
        from train import UNetTrainer  # 假设有这个训练器
        
        for i, config in enumerate(self.ensemble_configs):
            logging.info(f"开始训练第{i+1}个模型...")
            
            # 合并配置
            merged_config = {**self.base_config, **config}
            
            # 创建训练器
            trainer = UNetTrainer(merged_config)
            
            # 训练模型
            model, best_miou = trainer.train()
            
            # 保存模型
            model_path = f"ensemble_model_{i+1}_miou_{best_miou:.4f}.pth"
            torch.save(model.state_dict(), model_path)
            
            self.trained_models.append({
                'model': model,
                'path': model_path,
                'miou': best_miou,
                'config': config
            })
            
            logging.info(f"第{i+1}个模型训练完成，mIoU: {best_miou:.4f}")
    
    def create_ensemble(self, weight_method: str = 'performance') -> ModelEnsemble:
        """
        创建模型集成
        
        Args:
            weight_method: 权重计算方法 ('equal', 'performance', 'diversity')
            
        Returns:
            模型集成对象
        """
        models = [item['model'] for item in self.trained_models]
        
        if weight_method == 'equal':
            weights = None
        elif weight_method == 'performance':
            # 基于性能的权重
            mious = [item['miou'] for item in self.trained_models]
            weights = [miou / sum(mious) for miou in mious]
        elif weight_method == 'diversity':
            # 基于多样性的权重（简化版本）
            weights = self._calculate_diversity_weights()
        else:
            raise ValueError(f"不支持的权重方法: {weight_method}")
        
        ensemble = ModelEnsemble(models, weights)
        return ensemble
    
    def _calculate_diversity_weights(self) -> List[float]:
        """计算基于多样性的权重"""
        # 简化实现：基于配置差异计算权重
        num_models = len(self.trained_models)
        weights = [1.0] * num_models
        
        # 这里可以实现更复杂的多样性计算逻辑
        # 例如：基于模型预测的差异性
        
        return weights

def create_ensemble_configs() -> List[dict]:
    """创建集成模型配置"""
    configs = [
        # 配置1: ResNet50 + 强数据增强
        {
            'model': {'backbone': 'resnet50'},
            'train': {'total_epochs': 400},
            'augmentation_strength': 'high'
        },
        
        # 配置2: EfficientNet-B4 + 中等数据增强
        {
            'model': {'backbone': 'efficientnet-b4'},
            'train': {'total_epochs': 350},
            'augmentation_strength': 'medium'
        },
        
        # 配置3: ResNeXt101 + 轻度数据增强
        {
            'model': {'backbone': 'resnext101_32x8d'},
            'train': {'total_epochs': 300},
            'augmentation_strength': 'low'
        },
        
        # 配置4: Wide ResNet + 极强数据增强
        {
            'model': {'backbone': 'wide_resnet101_2'},
            'train': {'total_epochs': 450},
            'augmentation_strength': 'extreme'
        },
        
        # 配置5: RegNet + 自适应增强
        {
            'model': {'backbone': 'regnet_y_32gf'},
            'train': {'total_epochs': 380},
            'augmentation_strength': 'adaptive'
        }
    ]
    
    return configs

def main_ensemble_training():
    """主集成训练函数"""
    logging.basicConfig(level=logging.INFO)
    
    # 基础配置
    base_config = {
        'data': {'num_classes': 29},
        'loss': {
            'class_weight_method': 'manual',
            'manual_weights': [0.01, 1.0, 0.01, 0.01, 1.0, 1.0, 1.0, 1.0, 100.0, 1.0, 90.0, 1.0, 1.0, 0.01, 1.0, 1.0, 1.0, 1.0, 80.0, 1.0, 1.0, 0.01, 1.0, 70.0, 1.0, 1.0, 60.0, 50.0, 40.0]
        }
    }
    
    # 集成配置
    ensemble_configs = create_ensemble_configs()
    
    # 创建集成训练器
    trainer = EnsembleTrainer(base_config, ensemble_configs)
    
    # 训练单个模型
    trainer.train_individual_models()
    
    # 创建集成
    ensemble = trainer.create_ensemble(weight_method='performance')
    
    logging.info("🎉 模型集成创建完成！")
    
    return ensemble

if __name__ == "__main__":
    ensemble = main_ensemble_training()
    print("✅ 模型集成训练完成")
