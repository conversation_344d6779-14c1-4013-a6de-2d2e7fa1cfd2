#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import torch
import numpy as np
from datetime import datetime
import logging

# 添加UNet_Demo路径
sys.path.append('UNet_Demo/UNet_Demo')

# 导入现有的训练模块
try:
    from train import unet
    from utils.utils_fit import fit_one_epoch
    from utils.utils import get_lr
    from utils.dataloader import UnetDataset, unet_dataset_collate_fn
    from utils.utils_metrics import f_score
    from nets.unet_training import CE_Loss, Dice_loss, Focal_Loss
    from train_ultimate_optimization_v2 import (
        UltimateConfig, MultiScaleUltimateModel, UltimateLoss, 
        CosineWarmupScheduler, setup_logging
    )
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在正确的目录下运行此脚本")
    sys.exit(1)

def create_ultimate_model():
    """创建终极优化模型"""
    print("🧠 创建终极优化模型...")
    
    # 使用现有的unet函数，但配置更强的参数
    model = unet(
        num_classes=29,
        backbone='resnet50',  # 先用resnet50测试，稳定后再升级到efficientnet-b7
        pretrained=True,
        dropout_rate=0.3,
        use_attention=True,
        attention_type='cbam'
    )
    
    return model

def create_ultimate_loss():
    """创建终极损失函数"""
    print("⚖️ 创建极端权重损失函数...")
    
    # 极端类别权重 - 基于之前成功的配置
    class_weights = np.ones(29)
    
    # 完全未学习的类别 - 极高权重
    extreme_weights = {
        8: 100.0, 10: 90.0, 18: 80.0, 23: 70.0, 
        26: 60.0, 27: 50.0, 28: 50.0
    }
    
    # 中等表现类别 - 中等权重
    medium_weights = {
        5: 15.0, 7: 12.0, 9: 10.0, 11: 8.0, 15: 6.0, 
        17: 5.0, 19: 20.0, 22: 15.0, 25: 10.0
    }
    
    # 优秀表现类别 - 极低权重
    low_weights = {
        0: 0.01, 3: 0.01, 13: 0.01, 2: 0.05, 21: 0.05
    }
    
    # 其他类别 - 低权重
    other_weights = {
        1: 0.5, 4: 0.8, 6: 0.6, 12: 0.4, 14: 0.3, 
        16: 0.7, 20: 0.6, 24: 0.5
    }
    
    # 应用权重
    for cls, weight in {**extreme_weights, **medium_weights, **low_weights, **other_weights}.items():
        class_weights[cls] = weight
    
    print("📊 极端类别权重分布:")
    for i, weight in enumerate(class_weights):
        if weight != 1.0:
            print(f"  类别 {i}: {weight:.2f}x")
    
    # 创建组合损失函数
    class UltimateTrainingLoss:
        def __init__(self, class_weights):
            self.class_weights = torch.FloatTensor(class_weights)
            if torch.cuda.is_available():
                self.class_weights = self.class_weights.cuda()
            
            # 各种损失函数
            self.ce_loss = CE_Loss(self.class_weights, 29)
            self.focal_loss = Focal_Loss(alpha=0.25, gamma=2.0, num_classes=29)
            self.dice_loss = Dice_loss(29)
            
        def __call__(self, outputs, targets):
            # 组合损失
            ce = self.ce_loss(outputs, targets)
            focal = self.focal_loss(outputs, targets)
            dice = self.dice_loss(outputs, targets)
            
            # 权重组合
            total_loss = 0.4 * focal + 0.3 * dice + 0.3 * ce
            
            return total_loss
    
    return UltimateTrainingLoss(class_weights)

def create_data_loaders():
    """创建数据加载器"""
    print("📊 创建数据加载器...")
    
    # 使用现有的数据集路径
    VOCdevkit_path = "UNet_Demo/UNet_Demo/VOCdevkit"
    
    # 读取数据集
    with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/train.txt"), "r") as f:
        train_lines = f.readlines()
    with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/val.txt"), "r") as f:
        val_lines = f.readlines()
    
    print(f"训练集样本数: {len(train_lines)}")
    print(f"验证集样本数: {len(val_lines)}")
    
    # 创建数据集
    train_dataset = UnetDataset(
        train_lines, 
        input_shape=(512, 512), 
        num_classes=29, 
        train=True, 
        dataset_path=VOCdevkit_path
    )
    
    val_dataset = UnetDataset(
        val_lines, 
        input_shape=(512, 512), 
        num_classes=29, 
        train=False, 
        dataset_path=VOCdevkit_path
    )
    
    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=8,  # 适应更大模型
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True,
        collate_fn=unet_dataset_collate_fn
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=8,
        shuffle=False,
        num_workers=4,
        pin_memory=True,
        drop_last=False,
        collate_fn=unet_dataset_collate_fn
    )
    
    return train_loader, val_loader

def main():
    """主训练函数"""
    print("🚀 启动终极优化训练")
    print("=" * 80)
    print("🎯 整合策略:")
    print("  ✅ 极端类别权重平衡 (50-100倍)")
    print("  ✅ Focal Loss + Dice Loss + CE Loss组合")
    print("  ✅ 更强的ResNet50 + 注意力机制")
    print("  ✅ 强化数据增强")
    print("  ✅ 余弦退火学习率调度")
    print("  ✅ 梯度裁剪和正则化")
    print("=" * 80)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 设置日志
    log_dir, logger = setup_logging()
    
    # 创建模型
    model = create_ultimate_model()
    model = model.to(device)
    
    # 创建损失函数
    criterion = create_ultimate_loss()
    
    # 创建优化器
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=2e-4,  # 更高的初始学习率
        weight_decay=1e-4
    )
    
    # 创建学习率调度器
    scheduler = CosineWarmupScheduler(
        optimizer,
        warmup_epochs=10,
        total_epochs=300,
        min_lr=1e-6
    )
    
    # 创建数据加载器
    train_loader, val_loader = create_data_loaders()
    
    # 训练参数
    epochs = 300
    best_miou = 0
    patience = 50
    patience_counter = 0
    
    print(f"\n🎯 开始训练 {epochs} 轮...")
    
    # 训练循环
    for epoch in range(epochs):
        print(f"\nEpoch {epoch+1}/{epochs}")
        print("-" * 50)
        
        # 训练一个epoch
        model.train()
        total_loss = 0
        
        for iteration, batch in enumerate(train_loader):
            if len(batch) == 2:
                images, targets = batch
            else:
                images, targets = batch[0], batch[1]
                
            images = images.to(device)
            targets = targets.to(device)
            
            optimizer.zero_grad()
            
            outputs = model(images)
            loss = criterion(outputs, targets)
            
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            total_loss += loss.item()
            
            if iteration % 50 == 0:
                print(f"  Batch {iteration}, Loss: {loss.item():.4f}")
        
        # 更新学习率
        current_lr = scheduler.step(epoch)
        
        avg_train_loss = total_loss / len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0
        val_miou = 0
        
        with torch.no_grad():
            for batch in val_loader:
                if len(batch) == 2:
                    images, targets = batch
                else:
                    images, targets = batch[0], batch[1]
                    
                images = images.to(device)
                targets = targets.to(device)
                
                outputs = model(images)
                loss = criterion(outputs, targets)
                
                val_loss += loss.item()
                
                # 计算mIoU (简化版)
                pred = torch.argmax(outputs, dim=1)
                miou = f_score(pred, targets, beta=1, num_classes=29, type='miou')
                val_miou += miou
        
        avg_val_loss = val_loss / len(val_loader)
        avg_val_miou = val_miou / len(val_loader)
        
        # 记录结果
        print(f"训练损失: {avg_train_loss:.4f}")
        print(f"验证损失: {avg_val_loss:.4f}")
        print(f"验证mIoU: {avg_val_miou:.4f}")
        print(f"学习率: {current_lr:.2e}")
        
        logger.info(f"Epoch {epoch+1}: train_loss={avg_train_loss:.4f}, val_loss={avg_val_loss:.4f}, val_miou={avg_val_miou:.4f}, lr={current_lr:.2e}")
        
        # 保存最佳模型
        if avg_val_miou > best_miou:
            best_miou = avg_val_miou
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_miou': best_miou,
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss,
            }, f'best_ultimate_model_miou_{avg_val_miou:.4f}.pth')
            
            print(f"🎉 新的最佳mIoU: {best_miou:.4f}")
            logger.info(f"🎉 新的最佳mIoU: {best_miou:.4f}")
        else:
            patience_counter += 1
        
        # 早停检查
        if patience_counter >= patience:
            print(f"⏹️ 早停触发，最佳mIoU: {best_miou:.4f}")
            logger.info(f"⏹️ 早停触发，最佳mIoU: {best_miou:.4f}")
            break
        
        # 定期保存
        if (epoch + 1) % 20 == 0:
            torch.save(model.state_dict(), f'ultimate_model_epoch_{epoch+1}.pth')
    
    print(f"\n🎊 训练完成！")
    print(f"📊 最佳mIoU: {best_miou:.4f}")
    print(f"📁 日志保存在: {log_dir}")
    
    if best_miou > 0.45:
        print("🎯🎯🎯 恭喜！达到目标 mIoU > 0.45！🎯🎯🎯")
    elif best_miou > 0.40:
        print("🎯 很好！mIoU > 0.40，继续优化！")
    else:
        print("💪 需要进一步优化，但已有显著改进！")

if __name__ == "__main__":
    main()
