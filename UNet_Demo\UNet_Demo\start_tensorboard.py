"""
启动TensorBoard，只显示当前训练的日志
"""
import os
import argparse
import shutil
import subprocess
import time
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="启动TensorBoard，只显示当前训练的日志")
    parser.add_argument('--logs_dir', type=str, default='logs', help='日志根目录')
    parser.add_argument('--port', type=int, default=6006, help='TensorBoard端口')
    parser.add_argument('--latest_only', action='store_true', help='只显示最新的日志')
    parser.add_argument('--clean', action='store_true', help='清理旧的日志')
    parser.add_argument('--max_keep', type=int, default=5, help='保留最新的N个日志目录')
    return parser.parse_args()

def get_log_dirs(logs_dir):
    """获取所有日志目录，按修改时间排序"""
    if not os.path.exists(logs_dir):
        print(f"日志目录 {logs_dir} 不存在")
        return []
    
    # 获取所有子目录
    subdirs = [os.path.join(logs_dir, d) for d in os.listdir(logs_dir) 
               if os.path.isdir(os.path.join(logs_dir, d))]
    
    # 按修改时间排序
    subdirs.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    return subdirs

def clean_old_logs(logs_dir, max_keep=5):
    """清理旧的日志，只保留最新的N个"""
    subdirs = get_log_dirs(logs_dir)
    
    # 跳过current目录
    subdirs = [d for d in subdirs if os.path.basename(d) != 'current']
    
    if len(subdirs) <= max_keep:
        print(f"日志数量 ({len(subdirs)}) 小于等于保留数量 ({max_keep})，不需要清理")
        return
    
    # 删除旧的日志
    for d in subdirs[max_keep:]:
        print(f"删除旧日志: {d}")
        shutil.rmtree(d)

def create_current_dir(logs_dir, latest_only=False):
    """创建current目录，链接到最新的日志目录"""
    current_dir = os.path.join(logs_dir, 'current')
    
    # 如果current目录已存在，先删除
    if os.path.exists(current_dir):
        if os.path.isdir(current_dir):
            shutil.rmtree(current_dir)
    
    # 创建current目录
    os.makedirs(current_dir, exist_ok=True)
    
    if latest_only:
        # 获取最新的日志目录
        subdirs = get_log_dirs(logs_dir)
        
        # 跳过current目录
        subdirs = [d for d in subdirs if os.path.basename(d) != 'current']
        
        if not subdirs:
            print("没有找到日志目录")
            return
        
        latest_dir = subdirs[0]
        print(f"链接到最新日志: {latest_dir}")
        
        # 复制最新日志目录中的所有文件到current目录
        for item in os.listdir(latest_dir):
            s = os.path.join(latest_dir, item)
            d = os.path.join(current_dir, item)
            if os.path.isdir(s):
                shutil.copytree(s, d, dirs_exist_ok=True)
            else:
                shutil.copy2(s, d)

def start_tensorboard(logs_dir, port=6006):
    """启动TensorBoard"""
    # 如果使用latest_only，则指向current目录
    target_dir = os.path.join(logs_dir, 'current')
    
    print(f"启动TensorBoard，监控目录: {target_dir}")
    cmd = f"tensorboard --logdir={target_dir} --port={port}"
    
    # 在新进程中启动TensorBoard
    process = subprocess.Popen(cmd, shell=True)
    
    # 等待TensorBoard启动
    time.sleep(2)
    
    # 打开浏览器
    url = f"http://localhost:{port}"
    print(f"TensorBoard已启动，请访问: {url}")
    
    try:
        # 在Windows上打开浏览器
        os.system(f"start {url}")
    except:
        pass
    
    try:
        # 等待用户按Ctrl+C
        process.wait()
    except KeyboardInterrupt:
        print("正在关闭TensorBoard...")
        process.terminate()

def main():
    args = parse_args()
    
    if args.clean:
        clean_old_logs(args.logs_dir, args.max_keep)
    
    if args.latest_only:
        create_current_dir(args.logs_dir, args.latest_only)
    
    start_tensorboard(args.logs_dir, args.port)

if __name__ == "__main__":
    main()
