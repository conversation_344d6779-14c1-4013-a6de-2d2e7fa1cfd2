#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境测试脚本
"""

import sys
import os

print("🔍 环境检测开始...")
print(f"Python版本: {sys.version}")
print(f"当前目录: {os.getcwd()}")
print(f"Python路径: {sys.executable}")

# 检查PyTorch
try:
    import torch
    print(f"✅ PyTorch版本: {torch.__version__}")
    print(f"✅ CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
except ImportError as e:
    print(f"❌ PyTorch导入失败: {e}")

# 检查必要文件
files_to_check = [
    'config.yaml',
    'nets/unet.py',
    'utils/dataloader.py',
    'VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt'
]

for file_path in files_to_check:
    if os.path.exists(file_path):
        print(f"✅ 找到文件: {file_path}")
    else:
        print(f"❌ 缺少文件: {file_path}")

print("🔍 环境检测完成")

# 尝试简单的训练启动
print("\n🚀 尝试启动训练...")

try:
    # 导入必要模块
    sys.path.append('.')
    from nets.unet import Unet
    print("✅ UNet模型导入成功")
    
    # 创建模型
    model = Unet(num_classes=29, backbone='resnet50', pretrained=True)
    print("✅ 模型创建成功")
    
    # 移动到GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    print(f"✅ 模型移动到{device}成功")
    
    print("🎉 环境测试通过！可以开始训练")
    
    # 启动实际训练
    print("🚀 启动实际训练...")
    import subprocess
    result = subprocess.run([sys.executable, 'train.py'], 
                          capture_output=True, text=True, timeout=30)
    
    if result.returncode == 0:
        print("✅ 训练启动成功！")
        print(result.stdout)
    else:
        print("❌ 训练启动失败")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
        
except Exception as e:
    print(f"❌ 环境测试失败: {e}")
    import traceback
    traceback.print_exc()
