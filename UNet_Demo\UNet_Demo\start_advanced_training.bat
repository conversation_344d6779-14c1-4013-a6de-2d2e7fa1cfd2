@echo off
echo ========================================
echo     高级U-net训练启动脚本
echo ========================================
echo.

:: 设置环境变量
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
set CUDA_LAUNCH_BLOCKING=0
set PYTHONPATH=%cd%

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    pause
    exit /b 1
)

:: 检查CUDA
python -c "import torch; print('CUDA可用:', torch.cuda.is_available())"

echo.
echo 选择训练模式:
echo 1. 一键训练 (推荐新手)
echo 2. 高级训练 (自定义配置)
echo 3. 超参数调优
echo 4. 模型集成预测
echo 5. 高级评估
echo 6. 启动GPU监控
echo 7. 启动TensorBoard
echo.

set /p choice="请选择 (1-7): "

if "%choice%"=="1" goto one_click_train
if "%choice%"=="2" goto advanced_train
if "%choice%"=="3" goto hyperparameter_tuning
if "%choice%"=="4" goto ensemble_predict
if "%choice%"=="5" goto advanced_evaluate
if "%choice%"=="6" goto gpu_monitor
if "%choice%"=="7" goto tensorboard
goto invalid_choice

:one_click_train
echo.
echo 启动一键训练...
echo.
echo 可用的配置文件:
dir /b *.yaml
echo.
set /p config="选择配置文件 (默认: config.yaml): "
if "%config%"=="" set config=config.yaml

echo.
echo 可选参数:
echo --backbone: 骨干网络 (resnet50, resnet101, efficientnet-b4)
echo --epochs: 训练轮数
echo --batch_size: 批次大小
echo --learning_rate: 学习率
echo --tensorboard: 启动TensorBoard
echo.
set /p extra_args="输入额外参数 (可选): "

python one_click_train.py --config %config% %extra_args%
goto end

:advanced_train
echo.
echo 启动高级训练...
echo.
echo 可用的配置文件:
dir /b *.yaml
echo.
set /p config="选择配置文件 (默认: config.yaml): "
if "%config%"=="" set config=config.yaml

set /p resume="恢复训练权重路径 (可选): "

if "%resume%"=="" (
    python train_advanced.py --config %config%
) else (
    python train_advanced.py --config %config% --resume %resume%
)
goto end

:hyperparameter_tuning
echo.
echo 启动超参数调优...
echo.
set /p config="基础配置文件 (默认: config.yaml): "
if "%config%"=="" set config=config.yaml

set /p study_name="研究名称: "
if "%study_name%"=="" set study_name=unet_tuning

set /p n_trials="试验次数 (默认: 50): "
if "%n_trials%"=="" set n_trials=50

python hyperparameter_tuning.py --config %config% --study_name %study_name% --n_trials %n_trials%
goto end

:ensemble_predict
echo.
echo 启动模型集成预测...
echo.
set /p ensemble_config="集成配置文件 (默认: ensemble_config.yaml): "
if "%ensemble_config%"=="" set ensemble_config=ensemble_config.yaml

set /p input_path="输入图像路径或目录: "
set /p output_path="输出目录: "

set /p use_tta="使用测试时增强? (y/n, 默认: n): "
if "%use_tta%"=="y" (
    python ensemble_predict.py --config %ensemble_config% --input %input_path% --output %output_path% --tta
) else (
    python ensemble_predict.py --config %ensemble_config% --input %input_path% --output %output_path%
)
goto end

:advanced_evaluate
echo.
echo 启动高级评估...
echo.
set /p model_path="模型权重路径: "
set /p config_path="模型配置文件: "
set /p image_dir="测试图像目录: "
set /p label_dir="测试标签目录: "
set /p output_dir="输出目录: "

python advanced_evaluate.py --model %model_path% --config %config_path% --image_dir %image_dir% --label_dir %label_dir% --output_dir %output_dir%
goto end

:gpu_monitor
echo.
echo 启动GPU监控...
echo.
set /p interval="监控间隔(秒, 默认: 2): "
if "%interval%"=="" set interval=2

python gpu_memory_monitor.py --optimize --interval %interval% --plot --log
goto end

:tensorboard
echo.
echo 启动TensorBoard...
echo.
set /p log_dir="日志目录 (默认: logs): "
if "%log_dir%"=="" set log_dir=logs

echo 启动TensorBoard，访问地址: http://localhost:6006
tensorboard --logdir=%log_dir% --port=6006
goto end

:invalid_choice
echo 无效选择，请重新运行脚本
pause
exit /b 1

:end
echo.
echo 操作完成
pause
