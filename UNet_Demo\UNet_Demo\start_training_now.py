#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
立即启动训练脚本
基于成功的极端平衡策略，延长训练时间
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def main():
    print("🚀 立即启动融合优化训练！")
    print("=" * 60)
    print("基于您成功的极端平衡策略")
    print("目标：mIoU从0.3355提升到0.4+")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists('train.py'):
        print("❌ 未找到train.py，请确保在正确目录")
        return
    
    if not os.path.exists('config.yaml'):
        print("❌ 未找到config.yaml，请确保配置文件存在")
        return
    
    print("✅ 环境检查通过")
    print("📋 训练配置:")
    print("  • 模型: ResNet50 (成功验证)")
    print("  • 训练轮数: 500 (延长训练)")
    print("  • 极端类别平衡: 保持成功策略")
    print("  • 预期mIoU: 0.36-0.40")
    print("  • 预期时间: 8-12小时")
    
    # 启动训练
    print(f"\n⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🏃 启动训练...")
    
    try:
        # 使用subprocess启动训练
        result = subprocess.run([
            sys.executable, 'train.py'
        ], check=True)
        
        print("🎉 训练成功完成！")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练过程中出现错误: {e}")
        print("请检查日志文件获取详细信息")
    
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    
    except Exception as e:
        print(f"❌ 未知错误: {e}")

if __name__ == "__main__":
    main()
