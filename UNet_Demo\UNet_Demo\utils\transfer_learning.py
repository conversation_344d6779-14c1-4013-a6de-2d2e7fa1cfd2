"""
迁移学习和微调工具函数
"""
import torch
import torch.nn as nn
import logging
import re
from typing import Dict, List, Optional, Union

logger = logging.getLogger(__name__)

def setup_layerwise_learning_rates(model: nn.Module, cfg: Dict) -> List[Dict]:
    """
    为模型的不同部分设置不同的学习率
    
    Args:
        model: 模型
        cfg: 配置字典，包含学习率设置
        
    Returns:
        参数组列表，每组使用不同的学习率
    """
    # 获取学习率
    encoder_lr = float(cfg['train'].get('encoder_lr', 1e-5))
    decoder_lr = float(cfg['train'].get('decoder_lr', 5e-5))
    head_lr = float(cfg['train'].get('head_lr', 1e-4))
    
    logger.info(f"设置分层学习率: 编码器={encoder_lr}, 解码器={decoder_lr}, 分割头={head_lr}")
    
    # 将模型参数分为三组
    encoder_params = []
    decoder_params = []
    head_params = []
    
    # 分类参数
    for name, param in model.named_parameters():
        if 'encoder' in name:
            encoder_params.append(param)
        elif 'segmentation_head' in name:
            head_params.append(param)
        else:  # decoder
            decoder_params.append(param)
    
    # 创建参数组
    param_groups = [
        {'params': encoder_params, 'lr': encoder_lr},  # 编码器使用最小学习率
        {'params': decoder_params, 'lr': decoder_lr},  # 解码器使用中等学习率
        {'params': head_params, 'lr': head_lr}         # 分割头使用最大学习率
    ]
    
    # 打印每组参数数量
    logger.info(f"编码器参数: {sum(p.numel() for p in encoder_params):,}")
    logger.info(f"解码器参数: {sum(p.numel() for p in decoder_params):,}")
    logger.info(f"分割头参数: {sum(p.numel() for p in head_params):,}")
    
    return param_groups

def progressive_unfreezing(model: nn.Module, current_epoch: int, unfreeze_schedule: Dict) -> None:
    """
    根据训练轮次逐步解冻模型层
    
    Args:
        model: 模型
        current_epoch: 当前训练轮次
        unfreeze_schedule: 字典，键为轮次，值为要解冻的层名模式列表
    """
    # 首先冻结所有参数
    for param in model.parameters():
        param.requires_grad = False
    
    # 根据当前轮次解冻相应层
    for epoch, patterns in sorted(unfreeze_schedule.items()):
        if current_epoch >= int(epoch):
            for name, param in model.named_parameters():
                for pattern in patterns:
                    if re.search(pattern, name):
                        param.requires_grad = True
    
    # 打印当前解冻状态
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"可训练参数: {trainable_params:,}/{total_params:,} ({trainable_params/total_params:.2%})")
    
    # 打印每层的解冻状态
    if logger.isEnabledFor(logging.DEBUG):
        for name, param in model.named_parameters():
            logger.debug(f"{name}: {'可训练' if param.requires_grad else '冻结'}")

def get_unfreeze_schedule_from_config(cfg: Dict) -> Dict:
    """
    从配置中获取解冻计划
    
    Args:
        cfg: 配置字典
        
    Returns:
        解冻计划字典
    """
    if 'unfreeze_schedule' in cfg['train']:
        return cfg['train']['unfreeze_schedule']
    else:
        # 默认解冻计划
        return {
            0: ["segmentation_head"],  # 第0轮开始只训练分割头
            10: ["decoder"],           # 第10轮开始解冻解码器
            30: ["encoder.layer4"],    # 第30轮开始解冻编码器最后一层
            50: ["encoder.layer3"],    # 第50轮开始解冻更多层
            70: ["encoder.layer2"],    # 依此类推
            90: ["encoder.layer1"]
        }

def create_cosine_warmup_scheduler(optimizer, cfg: Dict, steps_per_epoch: int):
    """
    创建余弦退火+预热学习率调度器
    
    Args:
        optimizer: 优化器
        cfg: 配置字典
        steps_per_epoch: 每轮的步数
        
    Returns:
        学习率调度器
    """
    from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
    from utils.scheduler import GradualWarmupScheduler
    
    # 获取配置
    warmup_epochs = cfg['scheduler'].get('warmup_epochs', 10)
    cycles = cfg['scheduler'].get('cycles', 3)
    min_lr = cfg['scheduler'].get('min_lr', 1e-7)
    
    # 计算每个周期的长度
    cycle_length = cfg['train']['total_epochs'] // cycles
    
    # 创建余弦退火调度器
    cosine_scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=cycle_length,  # 第一次重启的轮次
        T_mult=1,          # 每次重启后周期长度的倍数
        eta_min=min_lr     # 最小学习率
    )
    
    # 如果需要预热，添加预热调度器
    if warmup_epochs > 0:
        scheduler = GradualWarmupScheduler(
            optimizer,
            multiplier=1.0,
            total_epoch=warmup_epochs,
            after_scheduler=cosine_scheduler
        )
    else:
        scheduler = cosine_scheduler
    
    return scheduler
