#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问卷星自动上传图片脚本
功能：批量上传图片到问卷星，创建循环评价题目
- 每题上传10张按顺序排列的图片
- 每个问卷创建30题循环评价项目
- 自动添加评价题选项
"""

import os
import time
import glob
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
from datetime import datetime

class WenjuanxingUploader:
    """问卷星自动上传器"""

    def __init__(self, image_folder_path, questionnaire_url):
        """
        初始化上传器

        Args:
            image_folder_path: 图片文件夹路径
            questionnaire_url: 问卷编辑页面URL
        """
        self.image_folder = image_folder_path
        self.questionnaire_url = questionnaire_url
        self.driver = None
        self.wait = None
        self.images = []
        self.current_question = 0

        # 设置日志
        self.setup_logging()

        # 获取图片列表
        self.load_images()

    def setup_logging(self):
        """设置日志"""
        log_filename = f"wenjuanxing_upload_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_images(self):
        """加载图片列表"""
        # 支持的图片格式
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif']

        for ext in image_extensions:
            pattern = os.path.join(self.image_folder, ext)
            self.images.extend(glob.glob(pattern))
            # 也搜索大写扩展名
            pattern_upper = os.path.join(self.image_folder, ext.upper())
            self.images.extend(glob.glob(pattern_upper))

        # 按文件名排序
        self.images.sort()

        self.logger.info(f"找到 {len(self.images)} 张图片")
        if len(self.images) < 300:  # 30题 × 10张图片
            self.logger.warning(f"图片数量不足，需要至少300张图片，当前只有{len(self.images)}张")

        # 打印前10张图片路径用于验证
        for i, img in enumerate(self.images[:10]):
            self.logger.info(f"图片 {i+1}: {os.path.basename(img)}")

    def setup_driver(self):
        """设置浏览器驱动"""
        try:
            # Chrome选项
            from selenium.webdriver.chrome.options import Options
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service

            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 设置下载路径（如果需要）
            prefs = {
                "profile.default_content_settings.popups": 0,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 设置等待
            self.wait = WebDriverWait(self.driver, 20)

            # 最大化窗口
            self.driver.maximize_window()

            self.logger.info("浏览器驱动设置完成")
            return True

        except Exception as e:
            self.logger.error(f"设置浏览器驱动失败: {e}")
            return False

    def login_and_navigate(self):
        """登录并导航到问卷编辑页面"""
        try:
            self.logger.info("正在打开问卷编辑页面...")
            self.driver.get(self.questionnaire_url)

            # 等待页面加载
            time.sleep(5)

            # 检查是否需要登录
            if "login" in self.driver.current_url.lower() or "登录" in self.driver.title:
                self.logger.info("需要手动登录，请在浏览器中完成登录...")
                input("登录完成后，按回车键继续...")

            # 再次导航到编辑页面
            self.driver.get(self.questionnaire_url)
            time.sleep(3)

            self.logger.info("成功进入问卷编辑页面")
            return True

        except Exception as e:
            self.logger.error(f"导航到编辑页面失败: {e}")
            return False

    def add_loop_evaluation_question(self, question_number, image_paths):
        """
        添加循环评价题目

        Args:
            question_number: 题目编号
            image_paths: 该题目的图片路径列表（10张）
        """
        try:
            self.logger.info(f"开始创建第 {question_number} 题循环评价...")

            # 1. 点击添加题目按钮 - 尝试多种可能的选择器
            add_question_selectors = [
                "//span[contains(text(), '添加题目')]",
                "//button[contains(text(), '添加题目')]",
                "//div[contains(text(), '添加题目')]",
                "//a[contains(text(), '添加题目')]",
                "//*[contains(@class, 'add-question')]",
                "//*[contains(@id, 'addQuestion')]"
            ]

            add_question_btn = None
            for selector in add_question_selectors:
                try:
                    add_question_btn = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if not add_question_btn:
                raise Exception("找不到添加题目按钮")

            add_question_btn.click()
            time.sleep(3)

            # 2. 选择循环评价题型 - 尝试多种选择器
            loop_evaluation_selectors = [
                "//div[contains(text(), '循环评价')]",
                "//span[contains(text(), '循环评价')]",
                "//li[contains(text(), '循环评价')]",
                "//*[contains(@title, '循环评价')]",
                "//*[contains(@data-type, 'loop')]"
            ]

            loop_evaluation_btn = None
            for selector in loop_evaluation_selectors:
                try:
                    loop_evaluation_btn = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if not loop_evaluation_btn:
                raise Exception("找不到循环评价选项")

            loop_evaluation_btn.click()
            time.sleep(3)

            # 3. 设置题目标题
            title_selectors = [
                "//input[@placeholder='请输入题目']",
                "//input[contains(@placeholder, '题目')]",
                "//textarea[contains(@placeholder, '题目')]",
                "//*[contains(@class, 'question-title')]//input",
                "//*[contains(@class, 'title-input')]"
            ]

            title_input = None
            for selector in title_selectors:
                try:
                    title_input = self.wait.until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if title_input:
                title_input.clear()
                title_input.send_keys(f"图片评价题目 {question_number}")
                time.sleep(1)

            # 4. 进入评价对象设置
            self.logger.info("设置评价对象...")
            evaluation_object_selectors = [
                "//span[contains(text(), '评价对象设置')]",
                "//button[contains(text(), '评价对象')]",
                "//div[contains(text(), '评价对象')]",
                "//*[contains(@class, 'evaluation-object')]",
                "//*[contains(text(), '对象设置')]"
            ]

            evaluation_object_btn = None
            for selector in evaluation_object_selectors:
                try:
                    evaluation_object_btn = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if evaluation_object_btn:
                evaluation_object_btn.click()
                time.sleep(3)

            # 5. 选择图片类型
            image_type_selectors = [
                "//div[contains(text(), '图片')]",
                "//span[contains(text(), '图片')]",
                "//li[contains(text(), '图片')]",
                "//*[contains(@data-type, 'image')]",
                "//*[contains(@class, 'image-type')]"
            ]

            image_type_btn = None
            for selector in image_type_selectors:
                try:
                    image_type_btn = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if image_type_btn:
                image_type_btn.click()
                time.sleep(3)

            # 6. 上传图片
            self.upload_images_for_question(image_paths)

            # 7. 添加评价选项
            self.add_evaluation_options()

            # 8. 保存题目
            save_selectors = [
                "//span[contains(text(), '保存')]",
                "//button[contains(text(), '保存')]",
                "//div[contains(text(), '保存')]",
                "//*[contains(@class, 'save-btn')]",
                "//*[contains(@id, 'save')]"
            ]

            save_btn = None
            for selector in save_selectors:
                try:
                    save_btn = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    break
                except TimeoutException:
                    continue

            if save_btn:
                save_btn.click()
                time.sleep(3)

            self.logger.info(f"第 {question_number} 题创建完成")
            return True

        except Exception as e:
            self.logger.error(f"创建第 {question_number} 题失败: {e}")
            # 截图保存错误状态
            try:
                screenshot_path = f"error_screenshot_q{question_number}_{datetime.now().strftime('%H%M%S')}.png"
                self.driver.save_screenshot(screenshot_path)
                self.logger.info(f"错误截图已保存: {screenshot_path}")
            except:
                pass
            return False

    def upload_images_for_question(self, image_paths):
        """为单个题目上传图片"""
        try:
            self.logger.info(f"开始上传 {len(image_paths)} 张图片...")

            for i, image_path in enumerate(image_paths):
                self.logger.info(f"上传第 {i+1} 张图片: {os.path.basename(image_path)}")

                # 点击上传图片按钮
                upload_btn = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '上传图片')]"))
                )
                upload_btn.click()
                time.sleep(2)

                # 找到文件输入元素
                file_input = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, "//input[@type='file']"))
                )

                # 上传文件
                file_input.send_keys(image_path)
                time.sleep(3)

                # 等待上传完成
                self.wait_for_upload_complete()

                # 确认上传
                confirm_btn = self.driver.find_elements(By.XPATH, "//span[contains(text(), '确定')]")
                if confirm_btn:
                    confirm_btn[0].click()
                    time.sleep(2)

            self.logger.info("图片上传完成")

        except Exception as e:
            self.logger.error(f"上传图片失败: {e}")
            raise

    def wait_for_upload_complete(self):
        """等待图片上传完成"""
        try:
            # 等待上传进度条消失或上传完成标识出现
            WebDriverWait(self.driver, 30).until(
                lambda driver: not driver.find_elements(By.XPATH, "//div[contains(@class, 'upload-progress')]")
            )
            time.sleep(2)
        except TimeoutException:
            self.logger.warning("等待上传完成超时，继续执行...")

    def add_evaluation_options(self):
        """添加评价选项"""
        try:
            self.logger.info("添加评价选项...")

            # 默认评价选项
            evaluation_options = [
                "非常不满意",
                "不满意",
                "一般",
                "满意",
                "非常满意"
            ]

            for i, option in enumerate(evaluation_options):
                # 点击添加选项按钮
                add_option_btn = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), '添加选项')]"))
                )
                add_option_btn.click()
                time.sleep(1)

                # 输入选项内容
                option_input = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, f"//input[@placeholder='选项{i+1}']"))
                )
                option_input.clear()
                option_input.send_keys(option)
                time.sleep(1)

            self.logger.info("评价选项添加完成")

        except Exception as e:
            self.logger.error(f"添加评价选项失败: {e}")

    def create_questionnaire(self, questions_per_questionnaire=30):
        """
        创建完整问卷

        Args:
            questions_per_questionnaire: 每个问卷的题目数量
        """
        try:
            self.logger.info(f"开始创建问卷，共 {questions_per_questionnaire} 题")

            images_per_question = 10
            total_images_needed = questions_per_questionnaire * images_per_question

            if len(self.images) < total_images_needed:
                self.logger.error(f"图片数量不足，需要 {total_images_needed} 张，实际 {len(self.images)} 张")
                return False

            # 创建题目
            for question_num in range(1, questions_per_questionnaire + 1):
                start_idx = (question_num - 1) * images_per_question
                end_idx = start_idx + images_per_question
                question_images = self.images[start_idx:end_idx]

                success = self.add_loop_evaluation_question(question_num, question_images)
                if not success:
                    self.logger.error(f"创建第 {question_num} 题失败，停止执行")
                    return False

                # 每5题休息一下
                if question_num % 5 == 0:
                    self.logger.info(f"已完成 {question_num} 题，休息5秒...")
                    time.sleep(5)

            self.logger.info("问卷创建完成！")
            return True

        except Exception as e:
            self.logger.error(f"创建问卷失败: {e}")
            return False

    def run(self):
        """运行主程序"""
        try:
            self.logger.info("开始执行问卷星自动上传任务")

            # 1. 设置浏览器
            if not self.setup_driver():
                return False

            # 2. 登录并导航
            if not self.login_and_navigate():
                return False

            # 3. 创建问卷
            success = self.create_questionnaire(30)

            if success:
                self.logger.info("🎉 任务执行成功！")
                input("任务完成，按回车键关闭浏览器...")
            else:
                self.logger.error("❌ 任务执行失败")
                input("任务失败，按回车键关闭浏览器...")

            return success

        except Exception as e:
            self.logger.error(f"执行任务时发生错误: {e}")
            return False

        finally:
            # 关闭浏览器
            if self.driver:
                self.driver.quit()
                self.logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("🚀 问卷星自动上传工具")
    print("=" * 50)

    # 配置参数
    image_folder = r"D:\1a_taohuacun"
    questionnaire_url = "https://www.wjx.cn/wjxdesignnew/designnew.aspx?version=7&openType=redesign&curid=316618829&nqt=&sguid=8d03ce0d-b368-47b9-8646-e129a13932f2"

    # 检查图片文件夹
    if not os.path.exists(image_folder):
        print(f"❌ 图片文件夹不存在: {image_folder}")
        return

    print(f"📁 图片文件夹: {image_folder}")
    print(f"🌐 问卷地址: {questionnaire_url}")
    print(f"📋 任务: 创建30题循环评价，每题10张图片")

    # 确认执行
    confirm = input("\n确认开始执行? (y/n): ").lower()
    if confirm != 'y':
        print("任务已取消")
        return

    # 创建上传器并执行
    uploader = WenjuanxingUploader(image_folder, questionnaire_url)
    success = uploader.run()

    if success:
        print("🎉 任务执行成功！")
    else:
        print("❌ 任务执行失败，请查看日志文件")

if __name__ == "__main__":
    main()
