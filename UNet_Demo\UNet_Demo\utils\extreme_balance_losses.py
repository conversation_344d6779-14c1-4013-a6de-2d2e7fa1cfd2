#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
极端类别平衡损失函数
专门解决严重类别不平衡问题
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ExtremeBalanceLoss(nn.Module):
    """极端类别平衡损失函数组合"""

    def __init__(self, num_classes=29, difficult_classes=[8, 10, 18, 23, 26, 27, 28]):
        super(ExtremeBalanceLoss, self).__init__()
        self.num_classes = num_classes
        self.difficult_classes = difficult_classes

        # 创建极端权重
        self.class_weights = self.create_extreme_weights()

        # 损失函数组件
        self.ce_loss = nn.CrossEntropyLoss(weight=self.class_weights, ignore_index=255)
        self.focal_loss = FocalLoss(alpha=self.class_weights, gamma=6.0)
        self.dice_loss = DiceLoss(num_classes=num_classes)
        self.tversky_loss = TverskyLoss(alpha=0.2, beta=0.8)  # 偏向召回率
        self.lovasz_loss = LovaszSoftmaxLoss()

        # 困难类别专用损失
        self.difficult_focal = DifficultClassFocalLoss(
            difficult_classes=difficult_classes,
            gamma=8.0,
            num_classes=num_classes
        )

        # 损失权重
        self.loss_weights = {
            'ce': 0.1,
            'focal': 3.0,
            'dice': 2.5,
            'tversky': 2.0,
            'lovasz': 1.8,
            'difficult_focal': 5.0
        }

    def create_extreme_weights(self):
        """创建极端类别权重"""
        weights = torch.ones(self.num_classes)

        # 困难类别极高权重
        difficult_weights = {8: 100.0, 10: 90.0, 18: 80.0, 23: 70.0, 26: 60.0, 27: 50.0, 28: 40.0}
        for class_id, weight in difficult_weights.items():
            weights[class_id] = weight

        # 优秀类别极低权重
        excellent_classes = [0, 3, 13, 2, 21]
        for class_id in excellent_classes:
            weights[class_id] = 0.01

        return weights

    def forward(self, predictions, targets):
        """前向传播"""
        total_loss = 0.0
        loss_dict = {}

        # 处理输入维度
        if predictions.dim() == 4:  # [B, C, H, W]
            B, C, H, W = predictions.shape
            predictions_2d = predictions.view(B, C, -1).transpose(1, 2).contiguous().view(-1, C)  # [B*H*W, C]
        else:
            predictions_2d = predictions

        if targets.dim() == 3:  # [B, H, W]
            targets_1d = targets.view(-1)  # [B*H*W]
        else:
            targets_1d = targets

        # 移除ignore_index
        valid_mask = targets_1d != 255
        if valid_mask.sum() == 0:
            return torch.tensor(0.0, device=predictions.device, requires_grad=True), {'total': 0.0}

        valid_predictions = predictions_2d[valid_mask]
        valid_targets = targets_1d[valid_mask]

        # 交叉熵损失 - 确保权重在正确设备上
        device_weights = self.class_weights.to(predictions.device)
        ce_loss = F.cross_entropy(valid_predictions, valid_targets, weight=device_weights)
        loss_dict['ce'] = ce_loss
        total_loss += self.loss_weights['ce'] * ce_loss

        # Focal损失
        focal_loss = self.focal_loss(predictions, targets)
        loss_dict['focal'] = focal_loss
        total_loss += self.loss_weights['focal'] * focal_loss

        # Dice损失
        dice_loss = self.dice_loss(predictions, targets)
        loss_dict['dice'] = dice_loss
        total_loss += self.loss_weights['dice'] * dice_loss

        # Tversky损失
        tversky_loss = self.tversky_loss(predictions, targets)
        loss_dict['tversky'] = tversky_loss
        total_loss += self.loss_weights['tversky'] * tversky_loss

        # Lovász损失
        lovasz_loss = self.lovasz_loss(predictions, targets)
        loss_dict['lovasz'] = lovasz_loss
        total_loss += self.loss_weights['lovasz'] * lovasz_loss

        # 困难类别专用Focal损失
        difficult_focal_loss = self.difficult_focal(predictions, targets)
        loss_dict['difficult_focal'] = difficult_focal_loss
        total_loss += self.loss_weights['difficult_focal'] * difficult_focal_loss

        loss_dict['total'] = total_loss
        return total_loss, loss_dict

class FocalLoss(nn.Module):
    """Focal Loss实现"""

    def __init__(self, alpha=None, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        # 确保输入维度正确
        if inputs.dim() == 4:  # [B, C, H, W]
            inputs = inputs.view(inputs.size(0), inputs.size(1), -1)  # [B, C, H*W]
            inputs = inputs.transpose(1, 2).contiguous().view(-1, inputs.size(1))  # [B*H*W, C]
        if targets.dim() == 3:  # [B, H, W]
            targets = targets.view(-1)  # [B*H*W]

        # 移除ignore_index
        valid_mask = targets != 255
        if valid_mask.sum() == 0:
            return torch.tensor(0.0, device=inputs.device, requires_grad=True)

        inputs = inputs[valid_mask]
        targets = targets[valid_mask]

        # 确保权重在正确设备上
        alpha_device = self.alpha.to(inputs.device) if self.alpha is not None else None
        ce_loss = F.cross_entropy(inputs, targets, reduction='none', weight=alpha_device)
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class DifficultClassFocalLoss(nn.Module):
    """困难类别专用Focal损失"""

    def __init__(self, difficult_classes, gamma=8.0, num_classes=29):
        super(DifficultClassFocalLoss, self).__init__()
        self.difficult_classes = difficult_classes
        self.gamma = gamma
        self.num_classes = num_classes

        # 为困难类别创建超高权重
        self.alpha = torch.ones(num_classes)
        for class_id in difficult_classes:
            self.alpha[class_id] = 50.0  # 超高权重

    def forward(self, inputs, targets):
        device = inputs.device
        self.alpha = self.alpha.to(device)

        # 处理输入维度
        if inputs.dim() == 4:  # [B, C, H, W]
            B, C, H, W = inputs.shape
            inputs = inputs.view(B, C, -1).transpose(1, 2).contiguous().view(-1, C)  # [B*H*W, C]
        if targets.dim() == 3:  # [B, H, W]
            targets = targets.view(-1)  # [B*H*W]

        # 移除ignore_index
        valid_mask = targets != 255
        if valid_mask.sum() == 0:
            return torch.tensor(0.0, device=device, requires_grad=True)

        inputs = inputs[valid_mask]
        targets = targets[valid_mask]

        # 只计算困难类别的损失
        mask = torch.zeros_like(targets, dtype=torch.bool)
        for class_id in self.difficult_classes:
            mask |= (targets == class_id)

        if mask.sum() == 0:
            return torch.tensor(0.0, device=device, requires_grad=True)

        # 提取困难类别的预测和标签
        difficult_inputs = inputs[mask]
        difficult_targets = targets[mask]

        # 计算Focal损失
        ce_loss = F.cross_entropy(difficult_inputs, difficult_targets, reduction='none')
        pt = torch.exp(-ce_loss)

        # 应用超高gamma值 - 确保权重在正确设备上
        alpha_device = self.alpha.to(inputs.device)
        focal_loss = alpha_device[difficult_targets] * (1 - pt) ** self.gamma * ce_loss

        return focal_loss.mean()

class DiceLoss(nn.Module):
    """Dice损失"""

    def __init__(self, num_classes, smooth=1e-6):
        super(DiceLoss, self).__init__()
        self.num_classes = num_classes
        self.smooth = smooth

    def forward(self, inputs, targets):
        # 处理ignore_index
        if targets.dim() == 3:  # [B, H, W]
            valid_mask = targets != 255
            if valid_mask.sum() == 0:
                return torch.tensor(0.0, device=inputs.device, requires_grad=True)

        inputs = F.softmax(inputs, dim=1)

        # 处理ignore_index，将其设为0
        targets_clean = targets.clone()
        targets_clean[targets == 255] = 0

        # 转换为one-hot编码
        targets_one_hot = F.one_hot(targets_clean, num_classes=self.num_classes).permute(0, 3, 1, 2).float()

        # 如果有ignore_index，将对应位置的one-hot设为0
        if targets.dim() == 3:
            ignore_mask = (targets == 255).unsqueeze(1).expand_as(targets_one_hot)
            targets_one_hot[ignore_mask] = 0

        # 计算Dice系数
        intersection = (inputs * targets_one_hot).sum(dim=(2, 3))
        union = inputs.sum(dim=(2, 3)) + targets_one_hot.sum(dim=(2, 3))

        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
        dice_loss = 1.0 - dice.mean()

        return dice_loss

class TverskyLoss(nn.Module):
    """Tversky损失 - 可以调整精确率和召回率的权重"""

    def __init__(self, alpha=0.3, beta=0.7, smooth=1e-6):
        super(TverskyLoss, self).__init__()
        self.alpha = alpha  # 假阳性权重
        self.beta = beta    # 假阴性权重
        self.smooth = smooth

    def forward(self, inputs, targets):
        # 处理ignore_index
        if targets.dim() == 3:  # [B, H, W]
            valid_mask = targets != 255
            if valid_mask.sum() == 0:
                return torch.tensor(0.0, device=inputs.device, requires_grad=True)

        inputs = F.softmax(inputs, dim=1)

        # 处理ignore_index，将其设为0
        targets_clean = targets.clone()
        targets_clean[targets == 255] = 0

        # 转换为one-hot编码
        targets_one_hot = F.one_hot(targets_clean, num_classes=inputs.size(1)).permute(0, 3, 1, 2).float()

        # 如果有ignore_index，将对应位置的one-hot设为0
        if targets.dim() == 3:
            ignore_mask = (targets == 255).unsqueeze(1).expand_as(targets_one_hot)
            targets_one_hot[ignore_mask] = 0

        # 计算Tversky指数
        tp = (inputs * targets_one_hot).sum(dim=(2, 3))
        fp = (inputs * (1 - targets_one_hot)).sum(dim=(2, 3))
        fn = ((1 - inputs) * targets_one_hot).sum(dim=(2, 3))

        tversky = (tp + self.smooth) / (tp + self.alpha * fp + self.beta * fn + self.smooth)
        tversky_loss = 1.0 - tversky.mean()

        return tversky_loss

class LovaszSoftmaxLoss(nn.Module):
    """Lovász-Softmax损失"""

    def __init__(self, reduction='mean'):
        super(LovaszSoftmaxLoss, self).__init__()
        self.reduction = reduction

    def forward(self, inputs, targets):
        # 处理ignore_index
        if targets.dim() == 3:  # [B, H, W]
            valid_mask = targets != 255
            if valid_mask.sum() == 0:
                return torch.tensor(0.0, device=inputs.device, requires_grad=True)

            # 只处理有效像素
            inputs_flat = inputs.permute(0, 2, 3, 1).contiguous().view(-1, inputs.size(1))  # [B*H*W, C]
            targets_flat = targets.view(-1)  # [B*H*W]
            valid_mask_flat = valid_mask.view(-1)  # [B*H*W]

            inputs_valid = inputs_flat[valid_mask_flat]  # [N_valid, C]
            targets_valid = targets_flat[valid_mask_flat]  # [N_valid]

            probas = F.softmax(inputs_valid, dim=1)
            num_classes = probas.size(1)

            losses = []
            for c in range(num_classes):
                fg = (targets_valid == c).float()  # 前景
                if fg.sum() == 0:
                    continue

                errors = (fg - probas[:, c]).abs()
                errors_sorted, perm = torch.sort(errors, descending=True)
                fg_sorted = fg[perm]

                grad = self.lovasz_grad(fg_sorted)
                loss = torch.dot(errors_sorted, grad)
                losses.append(loss)

            if not losses:
                return torch.tensor(0.0, device=inputs.device, requires_grad=True)

            loss = torch.stack(losses).mean()
            return loss
        else:
            # 原始处理逻辑
            probas = F.softmax(inputs, dim=1)
            num_classes = probas.size(1)

            losses = []
            for c in range(num_classes):
                fg = (targets == c).float()  # 前景
                if fg.sum() == 0:
                    continue

                errors = (fg - probas[:, c]).abs()
                errors_sorted, perm = torch.sort(errors, descending=True)
                fg_sorted = fg.flatten()[perm]

                grad = self.lovasz_grad(fg_sorted)
                loss = torch.dot(errors_sorted, grad)
                losses.append(loss)

            if not losses:
                return torch.tensor(0.0, device=inputs.device, requires_grad=True)

            loss = torch.stack(losses).mean()
            return loss

    def lovasz_grad(self, gt_sorted):
        """计算Lovász梯度"""
        gts = gt_sorted.sum()
        intersection = gts - gt_sorted.cumsum(0)
        union = gts + (1 - gt_sorted).cumsum(0)
        jaccard = 1.0 - intersection / union

        if len(jaccard) > 1:
            jaccard[1:] = jaccard[1:] - jaccard[:-1]

        return jaccard

class AdaptiveLossWeighting(nn.Module):
    """自适应损失权重调整"""

    def __init__(self, num_classes, difficult_classes, update_frequency=10):
        super(AdaptiveLossWeighting, self).__init__()
        self.num_classes = num_classes
        self.difficult_classes = difficult_classes
        self.update_frequency = update_frequency

        # 初始权重
        self.loss_weights = nn.Parameter(torch.ones(6))  # 6个损失函数
        self.class_performance_history = []
        self.epoch_count = 0

    def update_weights(self, class_ious):
        """根据类别性能更新损失权重"""
        self.epoch_count += 1

        if self.epoch_count % self.update_frequency != 0:
            return

        # 计算困难类别的平均IoU
        difficult_ious = [class_ious[i] for i in self.difficult_classes if i < len(class_ious)]
        avg_difficult_iou = np.mean(difficult_ious) if difficult_ious else 0.0

        # 如果困难类别表现不佳，增加相关损失的权重
        if avg_difficult_iou < 0.01:  # 几乎没有学习
            with torch.no_grad():
                self.loss_weights[4] *= 1.2  # 增加困难类别Focal损失权重
                self.loss_weights[1] *= 1.1  # 增加普通Focal损失权重
        elif avg_difficult_iou > 0.1:  # 学习良好
            with torch.no_grad():
                self.loss_weights[4] *= 0.9  # 减少困难类别Focal损失权重

        # 确保权重在合理范围内
        with torch.no_grad():
            self.loss_weights.clamp_(0.1, 10.0)

class ClassBalancedSampler:
    """类别平衡采样器"""

    def __init__(self, dataset, difficult_classes, oversample_ratio=15.0):
        self.dataset = dataset
        self.difficult_classes = difficult_classes
        self.oversample_ratio = oversample_ratio

    def get_sample_weights(self):
        """计算样本权重"""
        weights = []

        for idx in range(len(self.dataset)):
            # 获取样本的类别信息
            sample_classes = self.get_sample_classes(idx)

            # 基础权重
            weight = 1.0

            # 如果包含困难类别，大幅增加权重
            for class_id in sample_classes:
                if class_id in self.difficult_classes:
                    weight *= self.oversample_ratio
                    break

            weights.append(weight)

        return weights

    def get_sample_classes(self, idx):
        """获取样本包含的类别（需要根据实际数据集实现）"""
        # 这里需要根据实际数据集实现
        # 暂时返回随机类别
        return [np.random.choice(29)]

class ProgressiveWeightScheduler:
    """渐进式权重调度器"""

    def __init__(self, initial_weights, target_weights, total_epochs):
        self.initial_weights = initial_weights
        self.target_weights = target_weights
        self.total_epochs = total_epochs

    def get_weights(self, epoch):
        """获取当前轮次的权重"""
        progress = min(epoch / self.total_epochs, 1.0)

        # 线性插值
        current_weights = {}
        for key in self.initial_weights:
            initial = self.initial_weights[key]
            target = self.target_weights[key]
            current_weights[key] = initial + (target - initial) * progress

        return current_weights

def create_extreme_balance_loss(num_classes=29, difficult_classes=[8, 10, 18, 23, 26, 27, 28]):
    """创建极端类别平衡损失函数"""
    return ExtremeBalanceLoss(num_classes=num_classes, difficult_classes=difficult_classes)
