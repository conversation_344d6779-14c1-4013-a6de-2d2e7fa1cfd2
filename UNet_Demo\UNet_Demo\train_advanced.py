#!/usr/bin/env python3
"""
高级U-net训练脚本
集成了多种优化技术和最佳实践
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.deeplabv3plus import unet
from utils.dataloader import SegmentationDataset
from utils.losses import CombinedLoss
from utils.callbacks import LossHistory, EvalCallback
from utils.utils_fit import fit_one_epoch
from utils.scheduler import build_scheduler as build_scheduler_new
from utils.class_weights import compute_class_weights as calculate_class_weights
from utils.utils import show_config

def setup_logging(log_dir):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def build_model(cfg):
    """构建模型"""
    model_cfg = cfg['model']

    # 创建模型
    model = unet(
        num_classes=cfg['data']['num_classes'],
        backbone=model_cfg['backbone'],
        pretrained=model_cfg['pretrained'],
        dropout_rate=model_cfg.get('dropout_rate', 0.2),
        use_attention=model_cfg.get('use_attention', True),
        attention_type=model_cfg.get('attention_type', 'cbam')
    )

    return model

def build_optimizer(model, cfg):
    """构建优化器"""
    opt_cfg = cfg['optimizer']
    train_cfg = cfg['train']

    # 分层学习率
    if train_cfg.get('progressive_unfreezing', False):
        # 为不同层设置不同学习率
        params = []

        # 编码器参数
        if hasattr(model, 'encoder'):
            params.append({
                'params': model.encoder.parameters(),
                'lr': train_cfg.get('encoder_lr', train_cfg['init_lr'] * 0.1)
            })

        # 解码器参数
        if hasattr(model, 'decoder'):
            params.append({
                'params': model.decoder.parameters(),
                'lr': train_cfg.get('decoder_lr', train_cfg['init_lr'] * 0.5)
            })

        # 分割头参数
        if hasattr(model, 'segmentation_head'):
            params.append({
                'params': model.segmentation_head.parameters(),
                'lr': train_cfg.get('head_lr', train_cfg['init_lr'])
            })

        # 如果没有找到特定层，使用所有参数
        if not params:
            params = model.parameters()
    else:
        params = model.parameters()

    # 选择优化器
    opt_type = opt_cfg.get('type', 'adamw').lower()

    if opt_type == 'adamw':
        optimizer = optim.AdamW(
            params,
            lr=train_cfg['init_lr'],
            betas=(opt_cfg.get('beta1', 0.9), opt_cfg.get('beta2', 0.999)),
            weight_decay=train_cfg.get('weight_decay', 1e-4)
        )
    elif opt_type == 'adam':
        optimizer = optim.Adam(
            params,
            lr=train_cfg['init_lr'],
            betas=(opt_cfg.get('beta1', 0.9), opt_cfg.get('beta2', 0.999)),
            weight_decay=train_cfg.get('weight_decay', 1e-4)
        )
    elif opt_type == 'sgd':
        optimizer = optim.SGD(
            params,
            lr=train_cfg['init_lr'],
            momentum=opt_cfg.get('momentum', 0.9),
            weight_decay=train_cfg.get('weight_decay', 1e-4),
            nesterov=True
        )
    else:
        raise ValueError(f"不支持的优化器类型: {opt_type}")

    return optimizer

def build_loss_function(cfg, class_weights=None):
    """构建损失函数"""
    loss_cfg = cfg['loss']

    # 转换类别权重为tensor
    if class_weights is not None:
        class_weights = torch.tensor(class_weights, dtype=torch.float32)

    loss_fn = CombinedLoss(loss_cfg, class_weights)
    return loss_fn

def build_dataloaders(cfg):
    """构建数据加载器"""
    data_cfg = cfg['data']
    train_cfg = cfg['train']

    # 训练数据集
    train_dataset = SegmentationDataset(
        file_list=data_cfg['train_list'],
        root_dir=data_cfg['dataset_path'],
        img_size=tuple(data_cfg['input_size']),
        num_classes=data_cfg['num_classes'],
        train=True,
        cfg=cfg
    )

    # 验证数据集
    val_dataset = SegmentationDataset(
        file_list=data_cfg['val_list'],
        root_dir=data_cfg['dataset_path'],
        img_size=tuple(data_cfg['input_size']),
        num_classes=data_cfg['num_classes'],
        train=False,
        cfg=cfg
    )

    # 数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=train_cfg['freeze_batch_size'],
        shuffle=True,
        num_workers=train_cfg['num_workers'],
        pin_memory=True,
        drop_last=True,
        collate_fn=None
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=train_cfg['freeze_batch_size'],
        shuffle=False,
        num_workers=train_cfg['num_workers'],
        pin_memory=True,
        drop_last=False,
        collate_fn=None
    )

    return train_loader, val_loader

def load_annotation_lines(annotation_path):
    """加载标注文件"""
    with open(annotation_path, 'r') as f:
        lines = f.readlines()
    return [line.strip() for line in lines]

def setup_device(cfg):
    """设置设备"""
    if cfg['train']['use_cuda'] and torch.cuda.is_available():
        device = torch.device('cuda')
        # 设置GPU内存分配
        torch.cuda.set_per_process_memory_fraction(0.85)
        # 启用cudnn基准测试
        if cfg['train'].get('benchmark_cudnn', True):
            torch.backends.cudnn.benchmark = True
    else:
        device = torch.device('cpu')

    return device

def apply_progressive_unfreezing(model, epoch, cfg):
    """应用渐进式解冻"""
    if not cfg['train'].get('progressive_unfreezing', False):
        return

    unfreeze_schedule = cfg['train'].get('unfreeze_schedule', {})

    for unfreeze_epoch, layers_to_unfreeze in unfreeze_schedule.items():
        if epoch >= int(unfreeze_epoch):
            for layer_name in layers_to_unfreeze:
                if hasattr(model, layer_name):
                    layer = getattr(model, layer_name)
                    for param in layer.parameters():
                        param.requires_grad = True
                    logging.info(f"解冻层: {layer_name}")

def main():
    parser = argparse.ArgumentParser(description='高级U-net训练')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--resume', type=str, default=None, help='恢复训练的权重路径')
    parser.add_argument('--gpu', type=int, default=0, help='GPU ID')
    args = parser.parse_args()

    # 加载配置
    cfg = load_config(args.config)

    # 设置设备
    device = setup_device(cfg)
    if device.type == 'cuda':
        torch.cuda.set_device(args.gpu)

    # 创建保存目录
    save_dir = os.path.join(cfg['train']['save_dir'], f"{cfg['model']['backbone']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    os.makedirs(save_dir, exist_ok=True)

    # 设置日志
    logger = setup_logging(save_dir)
    logger.info(f"开始训练，保存目录: {save_dir}")
    logger.info(f"使用设备: {device}")

    # 显示配置
    show_config(cfg)

    # 构建模型
    model = build_model(cfg)
    model = model.to(device)

    # 计算类别权重
    if cfg['loss'].get('use_class_weights', False):
        logger.info("计算类别权重...")
        class_weights = calculate_class_weights(
            cfg['data']['dataset_path'],
            cfg['data']['num_classes'],
            method=cfg['loss'].get('class_weight_method', 'inverse_frequency')
        )
        logger.info(f"类别权重: {class_weights}")
    else:
        class_weights = None

    # 构建损失函数
    loss_fn = build_loss_function(cfg, class_weights)
    loss_fn = loss_fn.to(device)

    # 构建优化器
    optimizer = build_optimizer(model, cfg)

    # 构建学习率调度器
    scheduler = build_scheduler_new(optimizer, cfg, None, cfg['train']['total_epochs'])

    # 构建数据加载器
    train_loader, val_loader = build_dataloaders(cfg)

    # 设置回调
    loss_history = LossHistory(save_dir, model, cfg['data']['input_size'])
    eval_callback = EvalCallback(val_loader, cfg['data']['num_classes'],
                                ignore_index=cfg['data']['num_classes'], device=device)

    # 混合精度训练
    scaler = torch.cuda.amp.GradScaler() if cfg['train'].get('mixed_precision', False) else None

    logger.info("开始训练...")

    # 训练循环
    for epoch in range(cfg['train']['total_epochs']):
        logger.info(f"Epoch {epoch+1}/{cfg['train']['total_epochs']}")

        # 应用渐进式解冻
        apply_progressive_unfreezing(model, epoch, cfg)

        # 训练一个epoch
        fit_one_epoch(
            model=model,
            loss_fn=loss_fn,
            optimizer=optimizer,
            scheduler=scheduler,
            train_loader=train_loader,
            val_loader=val_loader,
            epoch=epoch,
            device=device,
            loss_history=loss_history,
            eval_callback=eval_callback,
            cfg=cfg,
            scaler=scaler,
            use_amp=cfg['train'].get('mixed_precision', False),
            accumulation_steps=cfg['train'].get('gradient_accumulation', 1)
        )

        # 保存检查点
        if (epoch + 1) % cfg['train'].get('save_period', 10) == 0:
            checkpoint_path = os.path.join(save_dir, f'epoch_{epoch+1:03d}.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
                'loss': loss_history.losses[-1] if loss_history.losses else 0,
                'config': cfg
            }, checkpoint_path)
            logger.info(f"保存检查点: {checkpoint_path}")

    # 保存最终模型
    final_model_path = os.path.join(save_dir, 'final_model.pth')
    torch.save(model.state_dict(), final_model_path)
    logger.info(f"训练完成，最终模型保存至: {final_model_path}")

if __name__ == '__main__':
    main()
