# 训练优化指南

针对 i7-14700KF + RTX 4070 Ti Super 16GB 配置的深度学习训练优化指南

## 目录

1. [硬件优势分析](#硬件优势分析)
2. [软件优化策略](#软件优化策略)
3. [批次大小优化](#批次大小优化)
4. [内存管理](#内存管理)
5. [数据加载优化](#数据加载优化)
6. [模型架构优化](#模型架构优化)
7. [训练策略优化](#训练策略优化)
8. [监控工具使用](#监控工具使用)
9. [常见问题解决](#常见问题解决)

## 硬件优势分析

### CPU: i7-14700KF
- **核心/线程**: 20核心(8P+12E)/28线程
- **优势**: 强大的多线程性能，适合数据预处理和增强
- **建议**: 充分利用多线程进行数据加载和预处理

### GPU: RTX 4070 Ti Super 16GB
- **CUDA核心**: 8448
- **Tensor核心**: 第4代
- **显存**: 16GB GDDR6X
- **优势**: 
  - 强大的FP16/BF16混合精度性能
  - 足够的显存支持较大批次大小
  - 高效的Tensor核心加速矩阵运算
- **建议**: 启用混合精度训练，使用channels_last内存格式

## 软件优化策略

### PyTorch优化

1. **启用混合精度训练**
   ```python
   # 已在config.yaml中启用
   mixed_precision: true
   ```

2. **使用channels_last内存格式**
   ```python
   # 已在config.yaml中启用
   use_channels_last: true
   ```

3. **启用cudnn基准测试**
   ```python
   # 已在config.yaml中启用
   benchmark_cudnn: true
   ```

4. **设置环境变量**
   ```bash
   # 已在train_optimized.bat中设置
   set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
   set CUDA_LAUNCH_BLOCKING=0
   ```

### 数据加载优化

1. **优化DataLoader配置**
   ```python
   # 已优化DataLoader配置
   persistent_workers=True
   pin_memory=True
   pin_memory_device='cuda'
   prefetch_factor=2
   ```

2. **适当的num_workers**
   ```python
   # 已在config.yaml中设置
   num_workers: 4  # 根据内存情况可调整
   ```

## 批次大小优化

### 当前配置
```yaml
# 批量大小设置
freeze_batch_size: 12      # 冻结阶段批量大小
unfreeze_batch_size: 6     # 解冻阶段批量大小
```

### 优化建议

- **冻结阶段**: 可以尝试12-16之间的批次大小
- **解冻阶段**: 可以尝试6-8之间的批次大小
- **梯度累积**: 使用梯度累积增加等效批次大小
  ```yaml
  gradient_accumulation: 4  # 已在config.yaml中设置
  ```

### 动态调整

使用`gpu_memory_monitor.py`脚本监控GPU内存使用情况，根据建议动态调整批次大小：

```bash
python gpu_memory_monitor.py --optimize --interval 2
```

## 内存管理

### GPU内存优化

1. **控制内存分配比例**
   ```python
   # 已在train.py中设置
   torch.cuda.set_per_process_memory_fraction(0.85)
   ```

2. **定期清理缓存**
   ```python
   # 在训练循环中定期执行
   torch.cuda.empty_cache()
   ```

3. **减少不必要的中间变量**
   - 使用`del`删除不再需要的大型张量
   - 使用`with torch.no_grad()`进行验证

### CPU内存优化

1. **控制数据加载线程数**
   ```python
   # 已在config.yaml中设置
   num_workers: 4
   ```

2. **减少预加载数据量**
   - 使用较小的`prefetch_factor`
   - 使用流式数据加载而非全部加载到内存

## 数据加载优化

### 数据增强优化

1. **GPU上执行数据增强**
   - 考虑使用`kornia`库在GPU上执行数据增强
   - 减少CPU-GPU数据传输开销

2. **减少增强复杂度**
   - 对于复杂的增强操作，考虑预计算并缓存结果
   - 使用多进程预处理数据

### 数据格式优化

1. **使用内存映射文件**
   - 对于大型数据集，考虑使用内存映射文件格式
   - 减少内存使用并加速访问

2. **使用更高效的图像格式**
   - 考虑使用WebP或JPEG 2000等更高效的图像格式
   - 减少磁盘I/O和解码时间

## 模型架构优化

### 注意力机制优化

1. **CBAM注意力机制**
   - 已启用，有效提升性能
   - 考虑在关键层使用，而非所有层

2. **轻量级注意力变体**
   - 对于内存受限情况，考虑使用轻量级注意力变体
   - 例如ECA(Efficient Channel Attention)

### 骨干网络选择

1. **ResNet50**
   - 当前使用，平衡性能和效率
   - 适合当前硬件配置

2. **备选方案**
   - EfficientNet-B4: 更高效但可能更慢
   - ResNet101: 更强大但需要更多内存

## 训练策略优化

### 学习率优化

1. **余弦退火+预热**
   - 已启用，有效提升收敛速度
   - 当前配置:
     ```yaml
     type: cosine_warmup
     max_lr: 0.02
     min_lr: 5e-5
     warmup_epochs: 5
     cycles: 4
     ```

2. **分层学习率**
   - 已启用，针对不同层使用不同学习率
   - 当前配置:
     ```yaml
     encoder_lr: 5e-4
     decoder_lr: 1e-3
     head_lr: 2e-3
     ```

### 渐进式解冻

1. **当前策略**
   ```yaml
   progressive_unfreezing: true
   unfreeze_schedule:
     0: ["segmentation_head", "decoder"]
     3: ["encoder.layer4"]
     6: ["encoder.layer3"]
     9: ["encoder.layer2"]
     12: ["encoder.layer1"]
   ```

2. **优化建议**
   - 对于快速训练，可以缩短解冻间隔
   - 例如: 0/2/4/6/8轮解冻

## 监控工具使用

### GPU内存监控

使用提供的`gpu_memory_monitor.py`脚本监控GPU内存使用情况：

```bash
python gpu_memory_monitor.py --optimize --interval 2
```

该脚本将:
- 实时显示GPU内存使用情况
- 提供批次大小优化建议
- 监控PyTorch进程内存使用

### TensorBoard监控

使用TensorBoard监控训练进度：

```bash
tensorboard --logdir=logs/current
```

关注以下指标:
- 损失曲线趋势
- 学习率变化
- 各类别IoU变化
- GPU利用率

## 常见问题解决

### 内存不足错误

如果遇到"CUDA out of memory"错误:

1. **减小批次大小**
   - 修改`config.yaml`中的`freeze_batch_size`和`unfreeze_batch_size`

2. **增加梯度累积步数**
   - 修改`config.yaml`中的`gradient_accumulation`

3. **减少模型复杂度**
   - 考虑使用更轻量级的骨干网络
   - 减少注意力模块的使用

### 训练速度慢

如果训练速度不理想:

1. **检查GPU利用率**
   - 使用`gpu_memory_monitor.py`监控GPU利用率
   - 理想利用率应在80-95%之间

2. **优化数据加载**
   - 增加`num_workers`
   - 检查数据增强是否过于复杂

3. **检查CPU瓶颈**
   - 监控CPU利用率
   - 考虑预处理和缓存数据

### 训练不稳定

如果训练不稳定或损失波动大:

1. **调整学习率**
   - 降低`max_lr`
   - 延长预热阶段

2. **增加梯度裁剪**
   - 修改`gradient_clip`值

3. **检查类别权重**
   - 确保类别权重设置合理
   - 考虑使用更平滑的权重分布

## 结论

通过以上优化策略，您可以充分利用i7-14700KF + RTX 4070 Ti Super 16GB硬件配置的性能潜力。根据实际训练情况，灵活调整参数以获得最佳性能。

使用提供的`train_optimized.bat`脚本启动优化训练:

```bash
train_optimized.bat
```

定期使用`gpu_memory_monitor.py`监控GPU使用情况，根据建议调整配置。
