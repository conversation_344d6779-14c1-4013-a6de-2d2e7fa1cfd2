import time
import torch
from torch.utils.data import DataLoader
from torch.amp import autocast, GradScaler
from .utils_metrics import f_score
from tqdm import tqdm


def fit_one_epoch(model, loss_fn, optimizer, scheduler,
                  train_loader, val_loader,
                  epoch, device,
                  loss_history, eval_callback,
                  cfg, scaler=None, use_amp=False,
                  accumulation_steps=1):
    """
    完成一个 epoch 的训练与验证，并根据配置调用学习率调度器。

    参数:
    - model: 模型
    - loss_fn: 损失函数
    - optimizer: 优化器
    - scheduler: 学习率调度器
    - train_loader: 训练数据加载器
    - val_loader: 验证数据加载器
    - epoch: 当前轮次
    - device: 设备
    - loss_history: 损失记录器
    - eval_callback: 评估回调
    - cfg: 配置
    - scaler: 混合精度训练的梯度缩放器
    - use_amp: 是否使用混合精度训练

    返回:
    - 包含训练指标的字典
    """
    total_loss = 0.0
    total_fscore = 0.0

    epoch_step     = len(train_loader)
    epoch_step_val = len(val_loader)

    ignore_index = cfg['loss'].get('ignore_index', None)
    if ignore_index is None:
        ignore_index = cfg['data']['num_classes']

    # 训练
    model.train()
    train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{cfg['train']['total_epochs']} [Train]",
                     ncols=100, leave=True,
                     bar_format='{l_bar}{bar}{r_bar}')  # 使用单一连续进度条

    # 梯度累积设置
    optimizer.zero_grad()  # 确保开始时梯度为零

    # 获取当前学习率
    current_lr = optimizer.param_groups[0]['lr']

    # 记录训练开始时间
    start_time = time.time()

    for iteration, (imgs, masks) in enumerate(train_pbar):
        # 检查是否使用channels_last内存格式
        use_channels_last = cfg['train'].get('use_channels_last', False)

        # 将数据移动到设备，使用non_blocking=True加速数据传输
        if use_channels_last and device.type == 'cuda':
            # 使用channels_last内存格式
            imgs = imgs.to(device, non_blocking=True).to(memory_format=torch.channels_last)
        else:
            imgs = imgs.to(device, non_blocking=True)

        masks = masks.to(device, non_blocking=True)

        # 混合精度训练
        if use_amp:
            with autocast(device_type='cuda' if device.type == 'cuda' else 'cpu'):
                outputs = model(imgs)
                loss = loss_fn(outputs, masks)
                # 缩放损失以适应梯度累积
                loss = loss / accumulation_steps

            # 梯度缩放和累积
            scaler.scale(loss).backward()

            # 每 accumulation_steps 次迭代更新一次参数
            if (iteration + 1) % accumulation_steps == 0 or (iteration + 1) == len(train_loader):
                # 梯度裁剪，防止梯度爆炸
                if cfg['train'].get('gradient_clip', 0) > 0:
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(model.parameters(), cfg['train'].get('gradient_clip', 1.0))

                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad()
        else:
            outputs = model(imgs)
            loss = loss_fn(outputs, masks)
            # 缩放损失以适应梯度累积
            loss = loss / accumulation_steps

            # 梯度累积
            loss.backward()

            # 每 accumulation_steps 次迭代更新一次参数
            if (iteration + 1) % accumulation_steps == 0 or (iteration + 1) == len(train_loader):
                # 梯度裁剪，防止梯度爆炸
                if cfg['train'].get('gradient_clip', 0) > 0:
                    torch.nn.utils.clip_grad_norm_(model.parameters(), cfg['train'].get('gradient_clip', 1.0))

                optimizer.step()
                optimizer.zero_grad()

        # 学习率调度
        if cfg['scheduler']['type'] in ['cosine', 'onecycle']:
            scheduler.step()
            # 更新当前学习率
            current_lr = optimizer.param_groups[0]['lr']

        # 计算指标
        with torch.no_grad():
            fscore = f_score(outputs, masks, beta=1.0, ignore_index=ignore_index)

        total_loss += loss.item()
        # 检查fscore是否为张量
        if isinstance(fscore, torch.Tensor):
            total_fscore += fscore.item()
        else:
            total_fscore += fscore

        # 更新进度条
        train_pbar.set_postfix({
            'loss': f"{loss.item():.4f}",
            'avg_loss': f"{total_loss / (iteration + 1):.4f}",
            'f_score': f"{fscore:.4f}" if isinstance(fscore, float) else f"{fscore.item():.4f}",
            'lr': f"{current_lr:.2e}"  # 显示当前学习率
        })

    mean_train_loss  = total_loss / max(epoch_step, 1)
    mean_train_fscore = total_fscore / max(epoch_step, 1)

    # 验证
    val_loss = 0.0
    val_fscore = 0.0
    model.eval()
    val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{cfg['train']['total_epochs']} [Val]",
                   ncols=100, leave=True,
                   bar_format='{l_bar}{bar}{r_bar}')  # 使用单一连续进度条

    with torch.no_grad():
        for iteration, (imgs, masks) in enumerate(val_pbar):
            # 检查是否使用channels_last内存格式
            use_channels_last = cfg['train'].get('use_channels_last', False)

            # 将数据移动到设备，使用non_blocking=True加速数据传输
            if use_channels_last and device.type == 'cuda':
                # 使用channels_last内存格式
                imgs = imgs.to(device, non_blocking=True).to(memory_format=torch.channels_last)
            else:
                imgs = imgs.to(device, non_blocking=True)

            masks = masks.to(device, non_blocking=True)

            # 混合精度推理
            if use_amp:
                with autocast(device_type='cuda' if device.type == 'cuda' else 'cpu'):
                    outputs = model(imgs)
                    loss = loss_fn(outputs, masks)
            else:
                outputs = model(imgs)
                loss = loss_fn(outputs, masks)

            fscore = f_score(outputs, masks, beta=1.0, ignore_index=ignore_index)
            val_loss += loss.item()
            # 检查fscore是否为张量
            if isinstance(fscore, torch.Tensor):
                val_fscore += fscore.item()
            else:
                val_fscore += fscore

            # 更新进度条
            val_pbar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'avg_loss': f"{val_loss / (iteration + 1):.4f}",
                'f_score': f"{fscore:.4f}" if isinstance(fscore, float) else f"{fscore.item():.4f}"
            })

    mean_val_loss = val_loss / max(epoch_step_val, 1)
    mean_val_fscore = val_fscore / max(epoch_step_val, 1)

    # ReduceLROnPlateau 型调度器在 epoch 末尾更新
    if cfg['scheduler']['type'] == 'plateau':
        scheduler.step(mean_val_loss)

    # 计算 mIoU
    mean_iou, class_ious = eval_callback.on_epoch_end(epoch, model)

    # 获取当前学习率
    current_lr = optimizer.param_groups[0]['lr']

    # 收集训练详细指标
    train_metrics = {
        'batch_size': train_loader.batch_size,
        'samples': len(train_loader.dataset),
        'steps_per_epoch': epoch_step,
        'gradient_accumulation_steps': accumulation_steps,
        'train_fscore': total_fscore / max(epoch_step, 1),
    }

    # 记录与绘图
    loss_history.append_metrics(
        epoch=epoch,
        loss=mean_train_loss,
        val_loss=mean_val_loss,
        fscore=mean_val_fscore,
        miou=mean_iou,
        lr=current_lr,
        class_ious=class_ious,
        train_metrics=train_metrics
    )

    # 计算训练时间
    epoch_time = time.time() - start_time

    # 打印轮次结果
    print(f"Epoch [{epoch+1}/{cfg['train']['total_epochs']}] ",
          f"Train Loss: {mean_train_loss:.4f}| ",
          f"Val Loss: {mean_val_loss:.4f}| ",
          f"Train F: {mean_train_fscore:.4f}| ",
          f"Val F: {mean_val_fscore:.4f}| ",
          f"mIoU: {mean_iou:.4f}| ",
          f"LR: {current_lr:.2e}| ",
          f"Time: {epoch_time:.1f}s")

    # 返回指标
    return {
        'train_loss': mean_train_loss,
        'val_loss': mean_val_loss,
        'train_fscore': mean_train_fscore,
        'val_fscore': mean_val_fscore,
        'miou': mean_iou
    }
