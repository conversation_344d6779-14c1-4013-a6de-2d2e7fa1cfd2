"""
预训练和微调工具模块
提供预训练权重下载、加载和微调相关功能
"""
import os
import torch
import torch.nn as nn
import warnings
import logging
from pathlib import Path
import requests
import hashlib
from tqdm import tqdm
import yaml
import json
from utils.download_utils import download_file as download_file_robust
from utils.local_weights import find_local_weights, copy_to_pretrained_dir, load_local_imagenet_weights

logger = logging.getLogger(__name__)

# 预训练数据集信息
PRETRAIN_DATASETS = {
    "ADE20K": {
        "description": "场景理解数据集，包含150个语义类别",
        "num_classes": 150,
        "weights_url": {
            "resnet50": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/resnet50_a2_0-a2ad5bee.pth",
            "resnet101": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/resnet101_a2_0-1b2fb7f1.pth",
            "efficientnet-b4": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/efficientnet-b4_a2_0-27a41732.pth"
        },
        "md5": {
            "resnet50": "a2ad5bee2f00364f60b5f2e9adc7e6f7",
            "resnet101": "1b2fb7f1d90a7d0f1d0b0a5d3ad2c200",
            "efficientnet-b4": "27a41732d3b8a85d5f0d6c58cd368c33"
        }
    },
    "Cityscapes": {
        "description": "城市街景数据集，包含19个语义类别",
        "num_classes": 19,
        "weights_url": {
            "resnet50": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/resnet50_cs_0-4e6f4ec9.pth",
            "resnet101": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/resnet101_cs_0-cf40f06e.pth",
            "efficientnet-b4": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/efficientnet-b4_cs_0-3180e11e.pth"
        },
        "md5": {
            "resnet50": "4e6f4ec9a165a830d3e7c3ec92fd5a5e",
            "resnet101": "cf40f06e9c7da64fdcaa1e696e8f0e30",
            "efficientnet-b4": "3180e11e9435b9c5c8d9f47e4d2bf4a0"
        }
    },
    "COCO-Stuff": {
        "description": "COCO数据集的语义分割版本，包含171个类别",
        "num_classes": 171,
        "weights_url": {
            "resnet50": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/resnet50_cs_0-4e6f4ec9.pth",
            "resnet101": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/resnet101_cs_0-cf40f06e.pth",
            "efficientnet-b4": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/efficientnet-b4_cs_0-3180e11e.pth"
        },
        "md5": {
            "resnet50": "4e6f4ec9a165a830d3e7c3ec92fd5a5e",
            "resnet101": "cf40f06e9c7da64fdcaa1e696e8f0e30",
            "efficientnet-b4": "3180e11e9435b9c5c8d9f47e4d2bf4a0"
        }
    },
    "Pascal-VOC": {
        "description": "Pascal VOC数据集，包含21个类别",
        "num_classes": 21,
        "weights_url": {
            "resnet50": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/resnet50_voc_0-4e6f4ec9.pth",
            "resnet101": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/resnet101_voc_0-cf40f06e.pth",
            "efficientnet-b4": "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/efficientnet-b4_voc_0-3180e11e.pth"
        },
        "md5": {
            "resnet50": "4e6f4ec9a165a830d3e7c3ec92fd5a5e",
            "resnet101": "cf40f06e9c7da64fdcaa1e696e8f0e30",
            "efficientnet-b4": "3180e11e9435b9c5c8d9f47e4d2bf4a0"
        }
    },
    "Medical": {
        "description": "医学图像分割数据集集合",
        "num_classes": 10,
        "weights_url": {
            "resnet50": "https://github.com/medical-segmentation/weights/releases/download/v1.0/resnet50_medical.pth",
            "resnet101": "https://github.com/medical-segmentation/weights/releases/download/v1.0/resnet101_medical.pth",
            "efficientnet-b4": "https://github.com/medical-segmentation/weights/releases/download/v1.0/efficientnet-b4_medical.pth"
        },
        "md5": {
            "resnet50": "medical_resnet50_md5_placeholder",
            "resnet101": "medical_resnet101_md5_placeholder",
            "efficientnet-b4": "medical_efficientnet_md5_placeholder"
        }
    }
}

def download_file(url, save_path, expected_md5=None):
    """
    下载文件并验证MD5

    参数:
    - url: 下载URL
    - save_path: 保存路径
    - expected_md5: 预期的MD5值，用于验证

    返回:
    - 保存的文件路径
    """
    save_dir = os.path.dirname(save_path)
    os.makedirs(save_dir, exist_ok=True)

    # 检查文件是否已存在
    if os.path.exists(save_path):
        if expected_md5:
            # 验证已存在文件的MD5
            with open(save_path, 'rb') as f:
                file_md5 = hashlib.md5(f.read()).hexdigest()
            if file_md5 == expected_md5:
                logger.info(f"文件已存在且MD5验证通过: {save_path}")
                return save_path
            else:
                logger.warning(f"文件已存在但MD5验证失败，重新下载: {save_path}")
        else:
            logger.info(f"文件已存在: {save_path}")
            return save_path

    # 使用健壮的下载函数
    logger.info(f"使用健壮的下载函数下载文件: {url} -> {save_path}")
    success = download_file_robust(url, save_path, max_retries=5, timeout=60)

    if not success:
        raise RuntimeError(f"下载文件失败: {url}")

    # 验证MD5
    if expected_md5 and os.path.exists(save_path):
        with open(save_path, 'rb') as f:
            file_md5 = hashlib.md5(f.read()).hexdigest()
        if file_md5 != expected_md5:
            logger.warning(f"MD5验证失败: {save_path}, 预期: {expected_md5}, 实际: {file_md5}")

    return save_path

def get_pretrained_weights(dataset, backbone, weights_dir="pretrained_weights"):
    """
    获取预训练权重

    参数:
    - dataset: 预训练数据集名称
    - backbone: 骨干网络名称
    - weights_dir: 权重保存目录

    返回:
    - 权重文件路径
    """
    # 转换为大写以便匹配
    dataset = dataset.upper()

    # 首先检查目标路径是否已存在权重文件
    target_path = os.path.join(weights_dir, dataset, f"{backbone}.pth")
    if os.path.exists(target_path):
        logger.info(f"找到已存在的预训练权重: {target_path}")
        return target_path

    # 尝试在本地查找权重文件
    logger.info("尝试在本地查找预训练权重...")
    local_weights_path = find_local_weights(backbone=backbone)
    if local_weights_path:
        # 复制到目标目录
        copied_path = copy_to_pretrained_dir(local_weights_path, dataset, backbone)
        if copied_path:
            logger.info(f"使用本地预训练权重: {copied_path}")
            return copied_path

    # 如果是ImageNet数据集，尝试从torchvision加载
    if dataset == "IMAGENET":
        logger.info("尝试从torchvision加载ImageNet预训练权重...")
        imagenet_weights_path = load_local_imagenet_weights(backbone)
        if imagenet_weights_path:
            logger.info(f"使用torchvision预训练权重: {imagenet_weights_path}")
            return imagenet_weights_path

    # 检查数据集是否支持
    if dataset not in PRETRAIN_DATASETS:
        logger.warning(f"不支持的预训练数据集: {dataset}，可选: {list(PRETRAIN_DATASETS.keys())}")
        logger.info(f"尝试使用ADE20K数据集...")
        dataset = "ADE20K"

    dataset_info = PRETRAIN_DATASETS[dataset]
    if backbone not in dataset_info["weights_url"]:
        logger.warning(f"数据集 {dataset} 不支持骨干网络 {backbone}，可选: {list(dataset_info['weights_url'].keys())}")

        # 尝试找到支持的骨干网络
        supported_backbones = list(dataset_info['weights_url'].keys())
        if supported_backbones:
            closest_backbone = supported_backbones[0]
            logger.info(f"尝试使用支持的骨干网络: {closest_backbone}")
            backbone = closest_backbone
        else:
            raise ValueError(f"数据集 {dataset} 没有支持的骨干网络")

    url = dataset_info["weights_url"][backbone]
    md5 = dataset_info["md5"].get(backbone)

    # 构建保存路径
    save_path = os.path.join(weights_dir, dataset, f"{backbone}.pth")

    try:
        # 下载权重
        logger.info(f"开始下载 {dataset} 数据集的 {backbone} 预训练权重...")
        return download_file(url, save_path, md5)
    except Exception as e:
        logger.error(f"下载预训练权重失败: {e}")

        # 尝试备用URL
        backup_urls = {
            "ADE20K": {
                "resnet50": [
                    # 原始URL
                    "https://github.com/qubvel/segmentation_models.pytorch/releases/download/v0.0.2/resnet50_a2_0-a2ad5bee.pth",
                    # HuggingFace镜像
                    "https://huggingface.co/qubvel/segmentation_models.pytorch/resolve/main/resnet50_a2_0-a2ad5bee.pth",
                    # Google Cloud Storage镜像
                    "https://storage.googleapis.com/segmentation_models.pytorch/resnet50_a2_0-a2ad5bee.pth",
                    # 百度网盘镜像 (示例URL，需要替换为实际可用的URL)
                    "https://pan.baidu.com/s/1kWPY9wF",
                    # 阿里云OSS镜像 (示例URL，需要替换为实际可用的URL)
                    "https://aliyun-oss-example.oss-cn-hangzhou.aliyuncs.com/resnet50_a2_0-a2ad5bee.pth",
                    # 腾讯云COS镜像 (示例URL，需要替换为实际可用的URL)
                    "https://cos-example-1234567890.cos.ap-beijing.myqcloud.com/resnet50_a2_0-a2ad5bee.pth",
                    # 使用PyTorch Hub
                    "https://download.pytorch.org/models/resnet50-19c8e357.pth"
                ]
            }
        }

        if dataset in backup_urls and backbone in backup_urls[dataset]:
            for backup_url in backup_urls[dataset][backbone]:
                if backup_url != url:  # 避免重复尝试相同的URL
                    logger.info(f"尝试备用URL: {backup_url}")
                    try:
                        return download_file(backup_url, save_path, md5)
                    except Exception as e2:
                        logger.warning(f"备用URL下载失败: {e2}")

        # 所有尝试都失败，尝试使用ImageNet预训练权重
        logger.warning("所有下载尝试都失败，尝试使用ImageNet预训练权重...")
        imagenet_weights_path = load_local_imagenet_weights(backbone)
        if imagenet_weights_path:
            logger.info(f"使用ImageNet预训练权重: {imagenet_weights_path}")
            return imagenet_weights_path

        # 真的所有尝试都失败了
        logger.error("无法获取预训练权重")
        return None

def load_pretrained_weights(model, weights_path, num_classes, strict=False):
    """
    加载预训练权重到模型

    参数:
    - model: 模型实例
    - weights_path: 权重文件路径
    - num_classes: 目标类别数
    - strict: 是否严格加载

    返回:
    - 加载了权重的模型
    """
    if weights_path is None or not os.path.exists(weights_path):
        logger.warning(f"预训练权重文件不存在: {weights_path}")
        return model

    logger.info(f"加载预训练权重: {weights_path}")

    try:
        # 加载权重
        checkpoint = torch.load(weights_path, map_location='cpu')

        # 处理不同格式的checkpoint
        if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
            logger.info("从'state_dict'键加载权重")
        elif isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
            logger.info("从'model_state_dict'键加载权重")
        else:
            state_dict = checkpoint
            logger.info("直接加载权重")

        # 检查分割头是否需要调整
        if 'segmentation_head.0.weight' in state_dict:
            pretrained_classes = state_dict['segmentation_head.0.weight'].size(0)
            if pretrained_classes != num_classes:
                logger.info(f"预训练模型有 {pretrained_classes} 个类别，目标模型有 {num_classes} 个类别")
                logger.info("调整分割头...")

                # 删除与分割头相关的权重
                keys_to_remove = [k for k in state_dict.keys() if 'segmentation_head' in k]
                for k in keys_to_remove:
                    del state_dict[k]
                    logger.debug(f"删除权重: {k}")

        # 检查模型结构与权重是否匹配
        model_keys = set(model.state_dict().keys())
        weight_keys = set(state_dict.keys())

        # 计算匹配率
        matching_keys = model_keys.intersection(weight_keys)
        match_percentage = len(matching_keys) / len(model_keys) * 100

        logger.info(f"权重匹配率: {match_percentage:.2f}% ({len(matching_keys)}/{len(model_keys)})")

        if match_percentage < 50:
            logger.warning("权重匹配率低于50%，可能不是正确的预训练权重")

            # 尝试调整权重键名
            adjusted_state_dict = {}
            for k, v in state_dict.items():
                # 尝试常见的键名调整
                if k.startswith('encoder.'):
                    adjusted_k = k
                elif k.startswith('model.encoder.'):
                    adjusted_k = k.replace('model.', '')
                elif not k.startswith('encoder.') and not k.startswith('decoder.') and not k.startswith('segmentation_head.'):
                    adjusted_k = 'encoder.' + k
                else:
                    adjusted_k = k

                adjusted_state_dict[adjusted_k] = v

            # 检查调整后的匹配率
            adjusted_keys = set(adjusted_state_dict.keys())
            adjusted_matching_keys = model_keys.intersection(adjusted_keys)
            adjusted_match_percentage = len(adjusted_matching_keys) / len(model_keys) * 100

            logger.info(f"调整后权重匹配率: {adjusted_match_percentage:.2f}% ({len(adjusted_matching_keys)}/{len(model_keys)})")

            if adjusted_match_percentage > match_percentage:
                logger.info("使用调整后的权重")
                state_dict = adjusted_state_dict

        # 加载权重
        missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)

        if missing_keys:
            logger.warning(f"缺失的键: {len(missing_keys)}")
            for k in missing_keys[:10]:  # 只显示前10个
                logger.debug(f"缺失: {k}")
            if len(missing_keys) > 10:
                logger.debug(f"... 以及 {len(missing_keys) - 10} 个其他键")

        if unexpected_keys:
            logger.warning(f"意外的键: {len(unexpected_keys)}")
            for k in unexpected_keys[:10]:  # 只显示前10个
                logger.debug(f"意外: {k}")
            if len(unexpected_keys) > 10:
                logger.debug(f"... 以及 {len(unexpected_keys) - 10} 个其他键")

        if not missing_keys and not unexpected_keys:
            logger.info("预训练权重完美加载!")
        else:
            logger.info("预训练权重部分加载成功")

    except Exception as e:
        logger.error(f"加载预训练权重失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

    return model

def apply_finetune_strategy(model, method='gradual'):
    """
    应用微调策略

    参数:
    - model: 模型实例
    - method: 微调方法
        - 'all': 所有层一起训练
        - 'gradual': 逐步解冻训练
        - 'freeze_encoder': 冻结编码器

    返回:
    - 应用了微调策略的模型
    """
    if method == 'all':
        # 所有层都可训练
        for param in model.parameters():
            param.requires_grad = True
        print("微调策略: 所有层一起训练")

    elif method == 'freeze_encoder':
        # 冻结编码器
        for param in model.encoder.parameters():
            param.requires_grad = False
        print("微调策略: 冻结编码器，只训练解码器和分割头")

    elif method == 'gradual':
        # 初始冻结编码器，在训练过程中逐步解冻
        for param in model.encoder.parameters():
            param.requires_grad = False
        print("微调策略: 逐步解冻训练，初始冻结编码器")

    else:
        warnings.warn(f"未知的微调方法: {method}，使用默认策略")

    return model
