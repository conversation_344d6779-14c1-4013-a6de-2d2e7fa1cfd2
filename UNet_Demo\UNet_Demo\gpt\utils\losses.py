import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

def CE_Loss(inputs, targets, cls_weights=None, num_classes=29):
    if isinstance(cls_weights, np.ndarray):
        cls_weights = torch.tensor(cls_weights, dtype=torch.float32).to(inputs.device)
    return nn.CrossEntropyLoss(weight=cls_weights, ignore_index=255)(inputs, targets)

def Dice_loss(inputs, targets, smooth=1):
    inputs = F.softmax(inputs, dim=1)
    targets = F.one_hot(targets, num_classes=inputs.shape[1]).permute(0, 3, 1, 2).float()
    dims = (0, 2, 3)
    intersection = torch.sum(inputs * targets, dims)
    union = torch.sum(inputs + targets, dims)
    dice = (2. * intersection + smooth) / (union + smooth)
    return 1 - dice.mean()

def Focal_Loss(inputs, targets, cls_weights=None, num_classes=29, gamma=2.0):
    if isinstance(cls_weights, np.ndarray):
        cls_weights = torch.tensor(cls_weights, dtype=torch.float32).to(inputs.device)
    logpt = -F.cross_entropy(inputs, targets, weight=cls_weights, ignore_index=255, reduction='none')
    pt = torch.exp(logpt)
    loss = -((1 - pt) ** gamma) * logpt
    return loss.mean()

def combined_loss_fn(inputs, targets, cls_weights=None, num_classes=29, use_focal=True, use_dice=True):
    loss = CE_Loss(inputs, targets, cls_weights=cls_weights, num_classes=num_classes)
    if use_focal:
        loss += Focal_Loss(inputs, targets, cls_weights=cls_weights, num_classes=num_classes)
    if use_dice:
        loss += Dice_loss(inputs, targets)
    return loss
