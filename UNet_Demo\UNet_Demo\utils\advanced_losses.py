"""
高级损失函数实现
用于提升语义分割性能的先进损失函数
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, List

class BoundaryLoss(nn.Module):
    """边界损失函数 - 专注于边界区域的分割精度"""
    
    def __init__(self, theta0: float = 3, theta: float = 5):
        super(Bo<PERSON>ry<PERSON><PERSON>, self).__init__()
        self.theta0 = theta0
        self.theta = theta
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: 预测结果 [B, C, H, W]
            target: 真实标签 [B, H, W]
        """
        # 计算距离变换
        with torch.no_grad():
            # 简化的边界检测
            target_one_hot = F.one_hot(target.long(), num_classes=pred.size(1)).permute(0, 3, 1, 2).float()
            
            # 计算边界
            boundary = self._compute_boundary(target_one_hot)
            
            # 距离变换 (简化版)
            dist_map = self._compute_distance_map(boundary)
        
        # 应用softmax
        pred_soft = F.softmax(pred, dim=1)
        
        # 计算边界损失
        loss = torch.sum(pred_soft * dist_map) / torch.sum(dist_map + 1e-8)
        
        return loss
    
    def _compute_boundary(self, target_one_hot: torch.Tensor) -> torch.Tensor:
        """计算边界"""
        # 使用Sobel算子检测边界
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32, device=target_one_hot.device)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32, device=target_one_hot.device)
        
        sobel_x = sobel_x.view(1, 1, 3, 3).repeat(target_one_hot.size(1), 1, 1, 1)
        sobel_y = sobel_y.view(1, 1, 3, 3).repeat(target_one_hot.size(1), 1, 1, 1)
        
        edge_x = F.conv2d(target_one_hot, sobel_x, padding=1, groups=target_one_hot.size(1))
        edge_y = F.conv2d(target_one_hot, sobel_y, padding=1, groups=target_one_hot.size(1))
        
        boundary = torch.sqrt(edge_x**2 + edge_y**2)
        boundary = torch.sum(boundary, dim=1, keepdim=True)
        
        return boundary
    
    def _compute_distance_map(self, boundary: torch.Tensor) -> torch.Tensor:
        """计算距离图 (简化版)"""
        # 简化的距离变换
        dist_map = torch.exp(-boundary / self.theta0) * self.theta
        return dist_map

class TverskyLoss(nn.Module):
    """Tversky损失函数 - 处理类别不平衡"""
    
    def __init__(self, alpha: float = 0.3, beta: float = 0.7, smooth: float = 1e-6):
        super(TverskyLoss, self).__init__()
        self.alpha = alpha  # 假阳性权重
        self.beta = beta    # 假阴性权重
        self.smooth = smooth
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: 预测结果 [B, C, H, W]
            target: 真实标签 [B, H, W]
        """
        # 转换为one-hot
        target_one_hot = F.one_hot(target.long(), num_classes=pred.size(1)).permute(0, 3, 1, 2).float()
        
        # 应用softmax
        pred_soft = F.softmax(pred, dim=1)
        
        # 计算Tversky系数
        tp = torch.sum(pred_soft * target_one_hot, dim=(2, 3))
        fp = torch.sum(pred_soft * (1 - target_one_hot), dim=(2, 3))
        fn = torch.sum((1 - pred_soft) * target_one_hot, dim=(2, 3))
        
        tversky = (tp + self.smooth) / (tp + self.alpha * fp + self.beta * fn + self.smooth)
        
        # 返回损失 (1 - Tversky)
        loss = 1 - torch.mean(tversky)
        
        return loss

class LovaszSoftmaxLoss(nn.Module):
    """Lovász-Softmax损失函数 - 直接优化IoU"""
    
    def __init__(self, ignore_index: int = 255):
        super(LovaszSoftmaxLoss, self).__init__()
        self.ignore_index = ignore_index
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Args:
            pred: 预测结果 [B, C, H, W]
            target: 真实标签 [B, H, W]
        """
        # 应用softmax
        pred_soft = F.softmax(pred, dim=1)
        
        # 计算每个类别的Lovász损失
        losses = []
        for c in range(pred.size(1)):
            fg = (target == c).float()  # 前景
            if self.ignore_index is not None:
                fg = fg * (target != self.ignore_index).float()
            
            if torch.sum(fg) == 0:
                continue
                
            errors = torch.abs(fg - pred_soft[:, c])
            errors_sorted, perm = torch.sort(errors.view(-1), descending=True)
            fg_sorted = fg.view(-1)[perm]
            
            losses.append(self._lovasz_grad(fg_sorted) @ errors_sorted)
        
        return torch.mean(torch.stack(losses)) if losses else torch.tensor(0.0, device=pred.device)
    
    def _lovasz_grad(self, gt_sorted: torch.Tensor) -> torch.Tensor:
        """计算Lovász梯度"""
        p = len(gt_sorted)
        gts = gt_sorted.sum()
        intersection = gts - gt_sorted.cumsum(0)
        union = gts + (1 - gt_sorted).cumsum(0)
        jaccard = 1 - intersection / union
        
        if p > 1:
            jaccard[1:p] = jaccard[1:p] - jaccard[0:-1]
        
        return jaccard

class CompoundLoss(nn.Module):
    """复合损失函数 - 组合多种损失"""
    
    def __init__(self, 
                 num_classes: int,
                 weights: dict = None,
                 class_weights: Optional[torch.Tensor] = None,
                 ignore_index: int = 255):
        super(CompoundLoss, self).__init__()
        
        # 默认权重
        default_weights = {
            'ce': 1.0,
            'dice': 1.0,
            'focal': 1.0,
            'tversky': 0.5,
            'boundary': 0.3,
            'lovasz': 0.8
        }
        self.weights = weights if weights is not None else default_weights
        
        # 损失函数
        self.ce_loss = nn.CrossEntropyLoss(weight=class_weights, ignore_index=ignore_index)
        self.dice_loss = DiceLoss(num_classes=num_classes)
        self.focal_loss = FocalLoss(alpha=0.25, gamma=2.0, num_classes=num_classes)
        self.tversky_loss = TverskyLoss(alpha=0.3, beta=0.7)
        self.boundary_loss = BoundaryLoss()
        self.lovasz_loss = LovaszSoftmaxLoss(ignore_index=ignore_index)
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> dict:
        """
        Args:
            pred: 预测结果 [B, C, H, W]
            target: 真实标签 [B, H, W]
        
        Returns:
            dict: 包含各个损失分量和总损失
        """
        losses = {}
        
        # 计算各个损失
        if self.weights.get('ce', 0) > 0:
            losses['ce'] = self.ce_loss(pred, target.long())
        
        if self.weights.get('dice', 0) > 0:
            losses['dice'] = self.dice_loss(pred, target)
        
        if self.weights.get('focal', 0) > 0:
            losses['focal'] = self.focal_loss(pred, target)
        
        if self.weights.get('tversky', 0) > 0:
            losses['tversky'] = self.tversky_loss(pred, target)
        
        if self.weights.get('boundary', 0) > 0:
            losses['boundary'] = self.boundary_loss(pred, target)
        
        if self.weights.get('lovasz', 0) > 0:
            losses['lovasz'] = self.lovasz_loss(pred, target)
        
        # 计算总损失
        total_loss = sum(self.weights.get(name, 0) * loss for name, loss in losses.items())
        losses['total'] = total_loss
        
        return losses

class DiceLoss(nn.Module):
    """Dice损失函数"""
    
    def __init__(self, num_classes: int, smooth: float = 1e-6):
        super(DiceLoss, self).__init__()
        self.num_classes = num_classes
        self.smooth = smooth
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        # 转换为one-hot
        target_one_hot = F.one_hot(target.long(), num_classes=self.num_classes).permute(0, 3, 1, 2).float()
        
        # 应用softmax
        pred_soft = F.softmax(pred, dim=1)
        
        # 计算Dice系数
        intersection = torch.sum(pred_soft * target_one_hot, dim=(2, 3))
        union = torch.sum(pred_soft, dim=(2, 3)) + torch.sum(target_one_hot, dim=(2, 3))
        
        dice = (2 * intersection + self.smooth) / (union + self.smooth)
        
        # 返回损失
        return 1 - torch.mean(dice)

class FocalLoss(nn.Module):
    """Focal损失函数"""
    
    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, num_classes: int = 29):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.num_classes = num_classes
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        # 计算交叉熵
        ce_loss = F.cross_entropy(pred, target.long(), reduction='none')
        
        # 计算概率
        pt = torch.exp(-ce_loss)
        
        # 计算focal权重
        focal_weight = self.alpha * (1 - pt) ** self.gamma
        
        # 计算focal损失
        focal_loss = focal_weight * ce_loss
        
        return torch.mean(focal_loss)

# 创建优化的损失函数配置
def create_advanced_loss(num_classes: int, 
                        class_weights: Optional[torch.Tensor] = None,
                        loss_config: str = "aggressive") -> CompoundLoss:
    """
    创建高级损失函数
    
    Args:
        num_classes: 类别数量
        class_weights: 类别权重
        loss_config: 损失配置 ("conservative", "balanced", "aggressive")
    
    Returns:
        CompoundLoss实例
    """
    if loss_config == "conservative":
        weights = {
            'ce': 1.0,
            'dice': 0.8,
            'focal': 0.5,
            'tversky': 0.0,
            'boundary': 0.0,
            'lovasz': 0.3
        }
    elif loss_config == "balanced":
        weights = {
            'ce': 0.8,
            'dice': 1.0,
            'focal': 0.8,
            'tversky': 0.3,
            'boundary': 0.2,
            'lovasz': 0.5
        }
    elif loss_config == "aggressive":
        weights = {
            'ce': 0.6,
            'dice': 1.2,
            'focal': 1.0,
            'tversky': 0.5,
            'boundary': 0.4,
            'lovasz': 0.8
        }
    else:
        raise ValueError(f"Unknown loss_config: {loss_config}")
    
    return CompoundLoss(
        num_classes=num_classes,
        weights=weights,
        class_weights=class_weights
    )
