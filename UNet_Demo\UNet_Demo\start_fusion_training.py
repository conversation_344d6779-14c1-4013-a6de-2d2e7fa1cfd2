#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
融合优化训练启动脚本
一键启动平衡的优化方案
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_fusion_banner():
    """打印融合优化横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎯 融合优化训练启动器                      ║
    ║                                                              ║
    ║  当前最佳: mIoU = 0.3355                                     ║
    ║  目标性能: mIoU > 0.4 (学术论文质量)                         ║
    ║  预期提升: +19% 以上                                         ║
    ║                                                              ║
    ║  🎯 融合策略 (平衡取舍):                                     ║
    ║  ✅ 模型升级: ResNet50 → EfficientNet-B4                    ║
    ║  ✅ 训练时间: 119 → 400 epochs                              ║
    ║  ✅ 数据增强: 适度强化 (CutMix + MixUp)                     ║
    ║  ✅ 极端平衡: 保持成功策略                                   ║
    ║                                                              ║
    ║  ⚖️  取舍原则:                                               ║
    ║  • 性能提升 vs 训练效率                                      ║
    ║  • 模型复杂度 vs 稳定性                                      ║
    ║  • 创新技术 vs 成熟方案                                      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_environment():
    """检查环境"""
    print("🔍 检查训练环境...")
    
    # 检查GPU
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"✅ GPU: {gpu_name} ({gpu_memory:.1f}GB)")
            
            # 检查GPU内存是否足够
            if gpu_memory < 6:
                print("⚠️  警告: GPU内存可能不足，建议减小batch_size")
        else:
            print("❌ 未检测到GPU，无法进行高效训练")
            return False
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    
    # 检查配置文件
    config_files = [
        'config_fusion_optimized.yaml',
        'train_fusion_optimized.py'
    ]
    
    for file in config_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} 不存在")
            return False
    
    # 检查数据
    data_paths = [
        'VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt',
        'VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt'
    ]
    
    for path in data_paths:
        if os.path.exists(path):
            print(f"✅ {path}")
        else:
            print(f"❌ {path} 不存在")
            return False
    
    return True

def show_training_options():
    """显示训练选项"""
    print("\n🎯 选择训练模式:")
    print("1. 🚀 融合优化训练 (推荐)")
    print("2. 📊 快速验证 (10轮测试)")
    print("3. 🔄 继续之前的训练")
    print("4. 📈 性能分析")
    
    while True:
        try:
            choice = int(input("\n请选择 (1-4): "))
            if 1 <= choice <= 4:
                return choice
            else:
                print("请输入1-4之间的数字")
        except ValueError:
            print("请输入有效的数字")

def run_fusion_training():
    """运行融合优化训练"""
    print("\n🚀 启动融合优化训练...")
    
    # 显示训练配置
    print("📋 训练配置:")
    print("  • 模型: EfficientNet-B4")
    print("  • 训练轮数: 400")
    print("  • 批量大小: 8 (冻结) / 6 (解冻)")
    print("  • 数据增强: CutMix + MixUp")
    print("  • 损失函数: 极端类别平衡")
    print("  • 预期时间: 8-12小时")
    
    # 确认开始
    confirm = input("\n确认开始训练? (y/n): ").lower()
    if confirm != 'y':
        print("训练已取消")
        return
    
    # 启动训练
    cmd = ["python", "train_fusion_optimized.py"]
    print(f"🏃 执行命令: {' '.join(cmd)}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return subprocess.run(cmd)

def run_quick_validation():
    """运行快速验证"""
    print("\n📊 启动快速验证...")
    print("将运行10轮训练来验证配置是否正确")
    
    # 创建快速验证配置
    quick_config = """
# 快速验证配置
train:
  total_epochs: 10
  early_stopping: 5
  save_frequency: 5
"""
    
    with open('config_quick_validation.yaml', 'w') as f:
        # 读取基础配置
        with open('config_fusion_optimized.yaml', 'r') as base_f:
            base_config = base_f.read()
        
        # 修改训练轮数
        modified_config = base_config.replace('total_epochs: 400', 'total_epochs: 10')
        modified_config = modified_config.replace('early_stopping: 60', 'early_stopping: 5')
        f.write(modified_config)
    
    # 运行快速验证
    cmd = ["python", "train_fusion_optimized.py", "--config", "config_quick_validation.yaml"]
    print(f"🏃 执行命令: {' '.join(cmd)}")
    
    return subprocess.run(cmd)

def continue_training():
    """继续之前的训练"""
    print("\n🔄 继续之前的训练...")
    
    # 查找最新的检查点
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        print("❌ 未找到之前的训练记录")
        return
    
    subdirs = [d for d in os.listdir(logs_dir) if os.path.isdir(os.path.join(logs_dir, d))]
    if not subdirs:
        print("❌ 未找到之前的训练记录")
        return
    
    latest_dir = max(subdirs, key=lambda x: os.path.getctime(os.path.join(logs_dir, x)))
    latest_log_dir = os.path.join(logs_dir, latest_dir)
    
    print(f"📁 找到最新训练: {latest_log_dir}")
    
    # 查找检查点文件
    checkpoint_files = [f for f in os.listdir(latest_log_dir) if f.endswith('.pth')]
    if not checkpoint_files:
        print("❌ 未找到检查点文件")
        return
    
    latest_checkpoint = max(checkpoint_files, key=lambda x: os.path.getctime(os.path.join(latest_log_dir, x)))
    checkpoint_path = os.path.join(latest_log_dir, latest_checkpoint)
    
    print(f"📄 最新检查点: {latest_checkpoint}")
    
    # 继续训练
    cmd = ["python", "train_fusion_optimized.py", "--resume", checkpoint_path]
    print(f"🏃 执行命令: {' '.join(cmd)}")
    
    return subprocess.run(cmd)

def analyze_performance():
    """分析性能"""
    print("\n📈 分析训练性能...")
    
    # 查找最新的训练日志
    logs_dir = "logs"
    if not os.path.exists(logs_dir):
        print("❌ 未找到训练日志")
        return
    
    subdirs = [d for d in os.listdir(logs_dir) if os.path.isdir(os.path.join(logs_dir, d))]
    if not subdirs:
        print("❌ 未找到训练日志")
        return
    
    latest_dir = max(subdirs, key=lambda x: os.path.getctime(os.path.join(logs_dir, x)))
    latest_log_dir = os.path.join(logs_dir, latest_dir)
    
    print(f"📁 分析目录: {latest_log_dir}")
    
    # 读取mIoU历史
    miou_file = os.path.join(latest_log_dir, "epoch_miou.txt")
    if os.path.exists(miou_file):
        with open(miou_file, 'r') as f:
            lines = f.readlines()
            if lines:
                mious = [float(line.strip()) for line in lines if line.strip()]
                if mious:
                    best_miou = max(mious)
                    final_miou = mious[-1]
                    
                    print(f"📊 训练轮数: {len(mious)}")
                    print(f"📈 最佳mIoU: {best_miou:.4f}")
                    print(f"📊 最终mIoU: {final_miou:.4f}")
                    print(f"🎯 距离目标0.4: {max(0, 0.4 - best_miou):.4f}")
                    
                    # 计算提升
                    baseline = 0.3355
                    improvement = ((best_miou - baseline) / baseline) * 100
                    print(f"📈 相比基线提升: {improvement:.1f}%")
                    
                    if best_miou >= 0.4:
                        print("🏆 恭喜！已达到学术论文质量标准！")
                    else:
                        needed_improvement = ((0.4 - best_miou) / best_miou) * 100
                        print(f"📈 还需提升: {needed_improvement:.1f}%")
    
    # 启动TensorBoard
    print(f"\n📊 启动TensorBoard...")
    tensorboard_cmd = ["tensorboard", "--logdir", latest_log_dir, "--port", "6008"]
    subprocess.Popen(tensorboard_cmd)
    print(f"🌐 TensorBoard已启动: http://localhost:6008")

def main():
    """主函数"""
    print_fusion_banner()
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请检查配置")
        return
    
    print("\n✅ 环境检查通过")
    
    # 显示选项
    choice = show_training_options()
    
    # 记录开始时间
    start_time = time.time()
    
    # 执行选择的操作
    if choice == 1:
        result = run_fusion_training()
    elif choice == 2:
        result = run_quick_validation()
    elif choice == 3:
        result = continue_training()
    elif choice == 4:
        analyze_performance()
        return
    
    # 计算训练时间
    end_time = time.time()
    training_time = end_time - start_time
    hours = int(training_time // 3600)
    minutes = int((training_time % 3600) // 60)
    
    print(f"\n⏱️  总用时: {hours}小时{minutes}分钟")
    
    if hasattr(result, 'returncode'):
        if result.returncode == 0:
            print("🎉 训练成功完成！")
            print("\n📊 建议查看TensorBoard了解详细训练过程:")
            print("tensorboard --logdir=logs")
        else:
            print("❌ 训练过程中出现错误")
            print("请检查日志文件获取详细信息")

if __name__ == "__main__":
    main()
