#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的增强训练脚本
快速验证配置并开始训练
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import os
import time
from datetime import datetime
import segmentation_models_pytorch as smp

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🚀 使用设备: {device}")

class SimpleConfig:
    """简化配置"""
    BACKBONE = 'resnet34'  # 使用更小的模型
    EPOCHS = 50  # 减少轮数先测试
    BATCH_SIZE = 4  # 进一步减小批次大小
    LEARNING_RATE = 1e-4
    NUM_CLASSES = 29
    IMAGE_SIZE = 256  # 减小图像尺寸

def load_simple_dataset():
    """加载简化数据集"""
    from enhanced_dataset import create_enhanced_datasets

    print("📊 加载数据集...")
    train_dataset, val_dataset = create_enhanced_datasets()

    # 创建简单的数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=SimpleConfig.BATCH_SIZE,
        shuffle=True,
        num_workers=0,
        pin_memory=False,  # 关闭pin_memory避免问题
        drop_last=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=SimpleConfig.BATCH_SIZE,
        shuffle=False,
        num_workers=0,
        pin_memory=False
    )

    print(f"✅ 训练集: {len(train_loader)} 批次")
    print(f"✅ 验证集: {len(val_loader)} 批次")

    return train_loader, val_loader

def create_simple_model():
    """创建简化模型"""
    print(f"🧠 创建模型: UNet++ + {SimpleConfig.BACKBONE}")

    model = smp.UnetPlusPlus(
        encoder_name=SimpleConfig.BACKBONE,
        encoder_weights='imagenet',
        in_channels=3,
        classes=SimpleConfig.NUM_CLASSES,
        activation=None,
    )

    return model.to(device)

def simple_train_epoch(model, train_loader, criterion, optimizer, epoch):
    """简化的训练epoch"""
    model.train()
    total_loss = 0
    num_batches = 0

    print(f"\n🔄 Epoch {epoch+1}/{SimpleConfig.EPOCHS}")

    for batch_idx, (images, masks) in enumerate(train_loader):
        try:
            images, masks = images.to(device), masks.to(device)

            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, masks)
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            num_batches += 1

            if batch_idx % 2 == 0:  # 每2个批次打印一次
                print(f"  Batch {batch_idx+1}/{len(train_loader)}, Loss: {loss.item():.4f}")

        except Exception as e:
            print(f"❌ 批次 {batch_idx} 出错: {e}")
            continue

    avg_loss = total_loss / max(num_batches, 1)
    print(f"✅ Epoch {epoch+1} 完成, 平均损失: {avg_loss:.4f}")

    return avg_loss

def simple_validate(model, val_loader, criterion, epoch):
    """简化的验证"""
    model.eval()
    total_loss = 0
    total_iou = 0
    num_batches = 0

    with torch.no_grad():
        for batch_idx, (images, masks) in enumerate(val_loader):
            try:
                images, masks = images.to(device), masks.to(device)

                outputs = model(images)
                loss = criterion(outputs, masks)

                # 简单的IoU计算
                predictions = torch.argmax(outputs, dim=1)
                intersection = (predictions == masks).sum().float()
                union = predictions.numel()
                iou = intersection / union

                total_loss += loss.item()
                total_iou += iou.item()
                num_batches += 1

            except Exception as e:
                print(f"❌ 验证批次 {batch_idx} 出错: {e}")
                continue

    avg_loss = total_loss / max(num_batches, 1)
    avg_iou = total_iou / max(num_batches, 1)

    print(f"📊 验证结果 - 损失: {avg_loss:.4f}, IoU: {avg_iou:.4f}")

    return avg_loss, avg_iou

def main():
    """主函数"""
    print("🚀 启动简化增强训练")
    print("=" * 50)
    print(f"📋 配置:")
    print(f"  模型: UNet++ + {SimpleConfig.BACKBONE}")
    print(f"  轮数: {SimpleConfig.EPOCHS}")
    print(f"  批次: {SimpleConfig.BATCH_SIZE}")
    print(f"  学习率: {SimpleConfig.LEARNING_RATE}")
    print(f"  图像尺寸: {SimpleConfig.IMAGE_SIZE}x{SimpleConfig.IMAGE_SIZE}")

    # 清理显存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print(f"🧹 显存已清理")

    try:
        # 加载数据
        train_loader, val_loader = load_simple_dataset()

        # 创建模型
        model = create_simple_model()

        # 创建损失函数和优化器
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=SimpleConfig.LEARNING_RATE)

        # 训练循环
        best_iou = 0
        start_time = time.time()

        print(f"\n🎯 开始训练...")

        for epoch in range(SimpleConfig.EPOCHS):
            # 训练
            train_loss = simple_train_epoch(model, train_loader, criterion, optimizer, epoch)

            # 验证
            val_loss, val_iou = simple_validate(model, val_loader, criterion, epoch)

            # 保存最佳模型
            if val_iou > best_iou:
                best_iou = val_iou
                torch.save(model.state_dict(), f'best_simple_model_iou_{val_iou:.4f}.pth')
                print(f"🎉 新的最佳IoU: {best_iou:.4f}")

            # 每10轮保存一次
            if (epoch + 1) % 10 == 0:
                torch.save(model.state_dict(), f'simple_model_epoch_{epoch+1}.pth')
                elapsed = time.time() - start_time
                print(f"⏱️ 已训练 {epoch+1} 轮, 耗时: {elapsed/60:.1f}分钟")

        total_time = time.time() - start_time
        print(f"\n🎊 训练完成!")
        print(f"📊 最佳IoU: {best_iou:.4f}")
        print(f"⏱️ 总耗时: {total_time/60:.1f}分钟")

        if best_iou > 0.4:
            print("🎯 太好了! IoU > 0.4, 配置验证成功!")
        elif best_iou > 0.2:
            print("👍 不错! IoU > 0.2, 有改进空间")
        else:
            print("💪 需要进一步调试配置")

    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
