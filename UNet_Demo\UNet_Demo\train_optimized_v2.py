#!/usr/bin/env python3
"""
优化训练脚本 v2.0 - 基于深度分析的改进方案
实施类别不平衡解决方案、优化学习率策略、增强数据增强
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.unet import Unet
from utils.dataloader import SegmentationDataset
from utils.utils_fit import fit_one_epoch
from utils.utils_metrics import f_score
from utils.callbacks import LossHistory, EvalCallback
from utils.class_weights import get_class_weights
from utils.losses import CombinedLoss
from utils.scheduler import get_optimized_scheduler, create_layerwise_optimizer

def setup_logging(log_dir):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_model(config):
    """创建模型"""
    model_config = config['model']

    model = Unet(
        num_classes=model_config['num_classes'],
        backbone=model_config['backbone'],
        pretrained=True
    )

    return model

def create_optimizers(model, config):
    """创建优化器 - 分层学习率"""
    training_config = config['training']

    # 分层参数组
    encoder_params = []
    decoder_params = []
    head_params = []

    for name, param in model.named_parameters():
        if 'backbone' in name or 'encoder' in name:
            encoder_params.append(param)
        elif 'decoder' in name:
            decoder_params.append(param)
        else:
            head_params.append(param)

    # 创建参数组
    param_groups = [
        {'params': encoder_params, 'lr': training_config['encoder_lr']},
        {'params': decoder_params, 'lr': training_config['decoder_lr']},
        {'params': head_params, 'lr': training_config['head_lr']}
    ]

    optimizer = optim.AdamW(
        param_groups,
        weight_decay=training_config['weight_decay']
    )

    return optimizer

def create_scheduler(optimizer, config):
    """创建学习率调度器"""
    scheduler_config = config['training']['scheduler']

    if scheduler_config['type'] == 'cosine_annealing':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=scheduler_config['T_max'],
            eta_min=scheduler_config['eta_min']
        )

        # 添加warmup
        if scheduler_config.get('warmup_epochs', 0) > 0:
            from torch.optim.lr_scheduler import LambdaLR

            def warmup_lambda(epoch):
                if epoch < scheduler_config['warmup_epochs']:
                    return scheduler_config['warmup_factor'] + \
                           (1 - scheduler_config['warmup_factor']) * epoch / scheduler_config['warmup_epochs']
                return 1.0

            warmup_scheduler = LambdaLR(optimizer, warmup_lambda)
            return warmup_scheduler, scheduler

        return scheduler, None

    elif scheduler_config['type'] == 'reduce_on_plateau':
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True
        )
        return scheduler, None

    else:
        raise ValueError(f"不支持的调度器类型: {scheduler_config['type']}")

def create_datasets(config):
    """创建数据集"""
    # 使用默认数据集路径和配置
    dataset_path = "VOCdevkit"

    # 读取训练和验证文件列表
    with open("VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt", "r") as f:
        train_lines = f.readlines()

    with open("VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt", "r") as f:
        val_lines = f.readlines()

    # 训练数据集
    train_dataset = SegmentationDataset(
        file_list=train_lines,
        root_dir=dataset_path,
        img_size=(512, 512),
        num_classes=29,
        train=True
    )

    # 验证数据集
    val_dataset = SegmentationDataset(
        file_list=val_lines,
        root_dir=dataset_path,
        img_size=(512, 512),
        num_classes=29,
        train=False
    )

    return train_dataset, val_dataset

def create_dataloaders(train_dataset, val_dataset, config):
    """创建数据加载器"""
    training_config = config['training']

    train_loader = DataLoader(
        train_dataset,
        batch_size=training_config['batch_size'],
        shuffle=True,
        num_workers=training_config['num_workers'],
        pin_memory=True,
        drop_last=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=training_config['batch_size'],
        shuffle=False,
        num_workers=training_config['num_workers'],
        pin_memory=True,
        drop_last=False
    )

    return train_loader, val_loader

def main():
    """主训练函数"""
    # 加载配置
    config_path = "config_optimized_v2.yaml"
    config = load_config(config_path)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 创建日志目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"logs/optimized_v2_{timestamp}"
    logger = setup_logging(log_dir)

    logger.info("开始优化训练 v2.0")
    logger.info(f"配置文件: {config_path}")

    # 创建模型
    model = create_model(config)
    model = model.to(device)

    # 创建数据集和数据加载器
    train_dataset, val_dataset = create_datasets(config)
    train_loader, val_loader = create_dataloaders(train_dataset, val_dataset, config)

    logger.info(f"训练样本数: {len(train_dataset)}")
    logger.info(f"验证样本数: {len(val_dataset)}")

    # 计算类别权重
    class_weights = None
    if config['class_weights']['use_class_weights']:
        from utils.class_weights import compute_class_weights
        class_weights = compute_class_weights(
            dataset_path=".",
            num_classes=29,
            method=config['class_weights']['method']
        ).to(device)
        logger.info(f"类别权重: {class_weights}")

    # 创建损失函数
    criterion = CombinedLoss(config['loss'], class_weights)

    # 创建优化器
    optimizer = create_optimizers(model, config)

    # 创建学习率调度器
    scheduler = get_optimized_scheduler(optimizer, config['training'])

    # 创建回调函数
    loss_history = LossHistory(log_dir)
    eval_callback = EvalCallback(
        model, val_loader, config['data']['num_classes'],
        device, log_dir, eval_period=1
    )

    # 训练循环
    total_epochs = config['training']['total_epochs']
    best_miou = 0.0

    logger.info("开始训练...")

    for epoch in range(total_epochs):
        logger.info(f"Epoch {epoch+1}/{total_epochs}")

        # 训练一个epoch
        train_loss = fit_one_epoch(
            model, criterion, optimizer, train_loader,
            device, epoch, total_epochs, logger
        )

        # 验证
        val_loss, val_miou, val_fscore = eval_callback.on_epoch_end(epoch)

        # 更新学习率
        if isinstance(scheduler, optim.lr_scheduler.ReduceLROnPlateau):
            scheduler.step(val_loss)
        else:
            scheduler.step()

        # 记录损失
        loss_history.append_loss(epoch, train_loss, val_loss)

        # 保存最佳模型
        if val_miou > best_miou:
            best_miou = val_miou
            torch.save(model.state_dict(), os.path.join(log_dir, 'best_model.pth'))
            logger.info(f"保存最佳模型，mIoU: {best_miou:.4f}")

        # 记录当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        logger.info(f"当前学习率: {current_lr:.6f}")

        # 早停检查
        if hasattr(eval_callback, 'early_stopping_counter'):
            if eval_callback.early_stopping_counter >= config['early_stopping']['patience']:
                logger.info("触发早停")
                break

    logger.info("训练完成!")
    logger.info(f"最佳mIoU: {best_miou:.4f}")

if __name__ == "__main__":
    main()
