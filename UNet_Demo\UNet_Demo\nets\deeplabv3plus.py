import segmentation_models_pytorch as smp
from torch import nn
import torch
import warnings

class CBAM(nn.Module):
    def __init__(self, in_planes, ratio=8, kernel_size=7):
        super(CBAM, self).__init__()
        self.channel_attention = ChannelAttention(in_planes, ratio)
        self.spatial_attention = SpatialAttention(kernel_size)

    def forward(self, x):
        x = self.channel_attention(x) * x
        x = self.spatial_attention(x) * x
        return x

class ChannelAttention(nn.Module):
    def __init__(self, in_planes, ratio=8):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc1 = nn.Conv2d(in_planes, in_planes // ratio, 1, bias=False)
        self.relu1 = nn.ReLU()
        self.fc2 = nn.Conv2d(in_planes // ratio, in_planes, 1, bias=False)

        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc2(self.relu1(self.fc1(self.avg_pool(x))))
        max_out = self.fc2(self.relu1(self.fc1(self.max_pool(x))))
        out = avg_out + max_out
        return self.sigmoid(out)

class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()

        assert kernel_size in (3, 7), 'kernel size must be 3 or 7'
        padding = 3 if kernel_size == 7 else 1

        self.conv1 = nn.Conv2d(2, 1, kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv1(x)
        return self.sigmoid(x)


def DeepLabV3Plus(num_classes=29, backbone='resnet101', pretrained=True, dropout_rate=0.2, use_attention=True, attention_type='cbam'):
    """
    构建带注意力机制的 DeepLabV3+ 模型，使用指定的主干网络。

    参数:
    - num_classes: 类别数量
    - backbone: 骨干网络名称
    - pretrained: 是否使用预训练权重
    - dropout_rate: Dropout比率，用于防止过拟合
    - use_attention: 是否使用注意力机制
    - attention_type: 注意力机制类型，可选 'cbam', 'se', 'self'

    支持的骨干网络:
    - resnet: resnet18, resnet34, resnet50, resnet101, resnet152
    - efficientnet: efficientnet-b0 到 efficientnet-b7
    - resnext: resnext50_32x4d, resnext101_32x8d
    - swin: swin_tiny_patch4_window7_224, swin_small_patch4_window7_224, swin_base_patch4_window7_224
    """
    # 检查是否为Swin Transformer骨干网络
    is_swin = backbone.startswith('swin')

    # 对于Swin Transformer，需要特殊处理
    if is_swin:
        try:
            import timm
            # 确保timm已安装
            if not hasattr(timm.models, 'swin_transformer'):
                warnings.warn(f"Swin Transformer模型需要timm库支持，将回退到resnet101")
                backbone = 'resnet101'
                is_swin = False
        except ImportError:
            warnings.warn(f"未找到timm库，无法使用Swin Transformer，将回退到resnet101")
            backbone = 'resnet101'
            is_swin = False

    # 创建基础模型
    base_model = smp.DeepLabV3Plus(
        encoder_name=backbone,
        encoder_weights='imagenet' if pretrained else None,
        in_channels=3,
        classes=num_classes
    )

    class DeepLabV3Plus_CBAM(nn.Module):
        def __init__(self, base, dropout_rate=0.2, use_attention=True, attention_type='cbam'):
            super().__init__()
            self.encoder = base.encoder
            self.decoder = base.decoder
            self.segmentation_head = base.segmentation_head
            self.use_attention = use_attention
            self.attention_type = attention_type

            # 获取编码器输出通道数
            out_channels = base.encoder.out_channels

            # 导入注意力模块
            from utils.attention import get_attention_module

            # 增强版注意力机制：对多层特征应用注意力机制
            if use_attention:
                # 使用工厂函数获取指定类型的注意力模块
                self.attention_high = get_attention_module(attention_type, out_channels[-1])  # 最高层特征
                self.attention_mid = get_attention_module(attention_type, out_channels[-2])   # 中间层特征

            # 获取解码器输出通道数
            decoder_channels = 256  # DeepLabV3+解码器的默认输出通道数

            # 增加解码器容量，添加Dropout
            self.extra_conv = nn.Sequential(
                nn.Conv2d(decoder_channels, decoder_channels, kernel_size=3, padding=1),
                nn.BatchNorm2d(decoder_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(decoder_channels, decoder_channels, kernel_size=3, padding=1),
                nn.BatchNorm2d(decoder_channels),
                nn.ReLU(inplace=True),
                nn.Dropout2d(p=dropout_rate) if dropout_rate > 0 else nn.Identity()
            )

            # 添加额外的Dropout层
            self.dropout = nn.Dropout2d(p=dropout_rate) if dropout_rate > 0 else nn.Identity()

        def forward(self, x):
            features = self.encoder(x)

            # 应用注意力机制到多个层次的特征
            if self.use_attention:
                features[-1] = self.attention_high(features[-1])  # 最高层特征
                features[-2] = self.attention_mid(features[-2])   # 中间层特征

            # 解码
            decoder_output = self.decoder(*features)

            # 额外的卷积层增加容量
            decoder_output = self.extra_conv(decoder_output)

            # 应用额外的Dropout
            decoder_output = self.dropout(decoder_output)

            # 分割头
            masks = self.segmentation_head(decoder_output)
            return masks

    return DeepLabV3Plus_CBAM(base_model, dropout_rate=dropout_rate, use_attention=use_attention, attention_type=attention_type)


# 兼容 Unet 推理类的 generate() 调用
class UnetWrapper(nn.Module):
    def __init__(self, num_classes=29, backbone='resnet101', pretrained=True, dropout_rate=0.2, use_attention=True, attention_type='cbam'):
        super(UnetWrapper, self).__init__()
        self.model = DeepLabV3Plus(
            num_classes=num_classes,
            backbone=backbone,
            pretrained=pretrained,
            dropout_rate=dropout_rate,
            use_attention=use_attention,
            attention_type=attention_type
        )
        self.encoder = self.model.encoder  # ⭐ 显式暴露 encoder 属性（供 train.py 使用）

    def forward(self, x):
        return self.model(x)


# 用于 predict.py 中 Unet 类替代原 unet()
def unet(num_classes=29, backbone='resnet101', pretrained=True, dropout_rate=0.2, use_attention=True, attention_type='cbam'):
    return UnetWrapper(
        num_classes=num_classes,
        backbone=backbone,
        pretrained=pretrained,
        dropout_rate=dropout_rate,
        use_attention=use_attention,
        attention_type=attention_type
    )
