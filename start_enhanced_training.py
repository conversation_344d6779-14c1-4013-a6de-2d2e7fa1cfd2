#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动增强训练脚本
一键启动全面改进的训练
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    print("🚀" + "=" * 58 + "🚀")
    print("🎯          增强训练 V2.0 - 全面改进启动器          🎯")
    print("🚀" + "=" * 58 + "🚀")
    print()
    print("📋 本次改进包括:")
    print("  1️⃣ 模型架构升级: ResNet50 → EfficientNet-B7 + UNet++")
    print("  2️⃣ 智能类别平衡: 动态权重 + Focal Loss + Effective Number")
    print("  3️⃣ 过拟合解决: 强化数据增强 + Dropout + 标签平滑")
    print("  4️⃣ 训练策略优化: 余弦退火 + 预热 + 梯度裁剪")
    print("  5️⃣ 损失函数组合: CE + Focal + Dice + Lovász")
    print()
    print("🎯 目标: mIoU > 0.6 (相比当前0.35提升71%)")
    print("⏱️ 预计训练时间: 8-12小时")
    print()

def check_environment():
    """检查环境"""
    print("🔍 检查训练环境...")

    # 检查CUDA
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"  ✅ GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            print("  ⚠️ 未检测到CUDA，将使用CPU训练（速度较慢）")
    except ImportError:
        print("  ❌ PyTorch未安装")
        return False

    # 检查必要的包
    required_packages = {
        'segmentation_models_pytorch': 'segmentation_models_pytorch',
        'albumentations': 'albumentations',
        'timm': 'timm',
        'numpy': 'numpy',
        'opencv-python': 'cv2'
    }

    missing_packages = []
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"  ✅ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"  ❌ {package_name}")

    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False

    # 检查数据集
    data_paths = [
        "UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/JPEGImages",
        "UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/SegmentationClass",
        "UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt",
        "UNet_Demo/UNet_Demo/VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt"
    ]

    for path in data_paths:
        if os.path.exists(path):
            print(f"  ✅ {path}")
        else:
            print(f"  ❌ {path}")
            return False

    print("✅ 环境检查通过！")
    return True

def show_training_options():
    """显示训练选项"""
    print("\n🎛️ 训练配置选项:")
    print("  1. 🚀 完整训练 (300轮, EfficientNet-B7)")
    print("  2. ⚡ 快速测试 (50轮, 验证配置)")
    print("  3. 🔧 自定义配置")
    print("  4. 📊 分析当前结果")
    print("  5. 🔄 继续之前的训练")

    while True:
        try:
            choice = int(input("\n请选择 (1-5): "))
            if 1 <= choice <= 5:
                return choice
            else:
                print("❌ 请输入1-5之间的数字")
        except ValueError:
            print("❌ 请输入有效数字")

def run_full_training():
    """运行完整训练"""
    print("\n🚀 启动完整训练...")
    print("📋 配置:")
    print("  - 模型: UNet++ + EfficientNet-B7")
    print("  - 轮数: 300")
    print("  - 批次: 16")
    print("  - 学习率: 1e-4 (余弦退火)")
    print("  - 数据增强: 强化版本")

    confirm = input("\n确认开始训练? (y/n): ").lower()
    if confirm != 'y':
        print("❌ 训练已取消")
        return False

    # 启动训练
    cmd = ["python", "train_enhanced_v2.py"]

    print(f"\n🎯 开始训练...")
    print(f"📝 命令: {' '.join(cmd)}")

    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 universal_newlines=True, bufsize=1)

        # 实时输出
        for line in process.stdout:
            print(line.rstrip())

        process.wait()

        if process.returncode == 0:
            print("\n🎉 训练完成！")
            return True
        else:
            print(f"\n❌ 训练失败，退出码: {process.returncode}")
            return False

    except Exception as e:
        print(f"\n❌ 启动训练失败: {e}")
        return False

def run_quick_test():
    """运行快速测试"""
    print("\n⚡ 启动快速测试...")

    # 修改配置为快速测试
    test_config = """
# 快速测试配置
EPOCHS = 50
BATCH_SIZE = 8
BACKBONE = 'efficientnet-b4'  # 使用较小的模型
PATIENCE = 15
"""

    print("📋 快速测试配置:")
    print("  - 模型: UNet++ + EfficientNet-B4")
    print("  - 轮数: 50")
    print("  - 批次: 8")
    print("  - 目标: 验证配置正确性")

    confirm = input("\n确认开始测试? (y/n): ").lower()
    if confirm != 'y':
        print("❌ 测试已取消")
        return False

    # 创建测试配置文件
    with open("test_config.py", "w") as f:
        f.write(test_config)

    print("🎯 开始快速测试...")
    # 这里可以添加测试逻辑
    return True

def analyze_current_results():
    """分析当前结果"""
    print("\n📊 分析当前训练结果...")

    try:
        # 运行分析脚本
        subprocess.run(["python", "quick_analysis.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ 分析失败")
        return False

def continue_training():
    """继续之前的训练"""
    print("\n🔄 查找可继续的训练...")

    # 查找最新的检查点
    log_dirs = []
    if os.path.exists("logs"):
        for item in os.listdir("logs"):
            if item.startswith("enhanced_v2_"):
                log_dirs.append(item)

    if not log_dirs:
        print("❌ 未找到可继续的训练")
        return False

    log_dirs.sort(reverse=True)
    latest_dir = log_dirs[0]

    print(f"📁 找到最新训练: {latest_dir}")

    # 检查检查点文件
    checkpoint_dir = f"logs/{latest_dir}"
    checkpoints = []

    for file in os.listdir(checkpoint_dir):
        if file.startswith("checkpoint_") and file.endswith(".pth"):
            checkpoints.append(file)

    if not checkpoints:
        print("❌ 未找到检查点文件")
        return False

    checkpoints.sort()
    latest_checkpoint = checkpoints[-1]

    print(f"📄 最新检查点: {latest_checkpoint}")

    confirm = input("\n确认继续训练? (y/n): ").lower()
    if confirm != 'y':
        print("❌ 继续训练已取消")
        return False

    # 这里可以添加继续训练的逻辑
    print("🔄 继续训练功能开发中...")
    return True

def main():
    """主函数"""
    print_banner()

    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return

    # 显示选项
    choice = show_training_options()

    # 记录开始时间
    start_time = time.time()

    # 执行选择的操作
    success = False
    if choice == 1:
        success = run_full_training()
    elif choice == 2:
        success = run_quick_test()
    elif choice == 3:
        print("🔧 自定义配置功能开发中...")
    elif choice == 4:
        success = analyze_current_results()
    elif choice == 5:
        success = continue_training()

    # 计算耗时
    elapsed_time = time.time() - start_time
    hours = int(elapsed_time // 3600)
    minutes = int((elapsed_time % 3600) // 60)

    print(f"\n⏱️ 总耗时: {hours}小时{minutes}分钟")

    if success:
        print("🎉 操作完成！")
    else:
        print("❌ 操作未完成")

if __name__ == "__main__":
    main()
