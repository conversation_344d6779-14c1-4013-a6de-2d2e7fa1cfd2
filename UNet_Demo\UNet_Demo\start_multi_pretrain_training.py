"""
启动多预训练数据集权重混合训练
基于现有的训练框架，集成多预训练权重混合功能
"""
import os
import sys
import yaml
import torch
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from unet import unet
from utils.multi_pretrain_mixer import MultiPretrainMixer, apply_multi_pretrain_to_model
from train import main as train_main

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_mixed_pretrain_model():
    """创建混合预训练权重的模型"""
    logger.info("=== 开始多预训练数据集权重混合 ===")
    
    # 加载配置
    with open('config_multi_pretrain.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    # 创建基础模型
    model = unet(
        num_classes=cfg['data']['num_classes'],
        backbone=cfg['model']['backbone'],
        pretrained=False,  # 不使用单一预训练权重
        dropout_rate=cfg['model'].get('dropout_rate', 0.2),
        use_attention=cfg['model'].get('use_attention', True),
        attention_type=cfg['model'].get('attention_type', 'cbam')
    )
    
    # 创建权重混合器
    target_classes = cfg['data']['class_names']
    mixer = MultiPretrainMixer(
        target_classes=target_classes,
        target_num_classes=cfg['data']['num_classes']
    )
    
    # 要混合的数据集
    datasets = cfg['multi_pretrain']['datasets']
    backbone = cfg['model']['backbone']
    mixing_strategy = cfg['multi_pretrain']['mixing_strategy']
    
    logger.info(f"混合数据集: {datasets}")
    logger.info(f"骨干网络: {backbone}")
    logger.info(f"混合策略: {mixing_strategy}")
    
    # 检查是否已有混合权重
    mixed_weights_path = cfg['multi_pretrain']['mixed_weights_path']
    if os.path.exists(mixed_weights_path):
        logger.info(f"发现已有混合权重: {mixed_weights_path}")
        logger.info("加载已有混合权重...")
        
        mixed_weights, metadata = mixer.load_mixed_weights(mixed_weights_path)
        missing_keys, unexpected_keys = model.load_state_dict(mixed_weights, strict=False)
        
        logger.info(f"权重加载完成，缺失键: {len(missing_keys)}, 意外键: {len(unexpected_keys)}")
        logger.info(f"混合权重元数据: {metadata}")
        
    else:
        logger.info("创建新的混合权重...")
        
        # 加载多个预训练权重
        weights_dict = mixer.load_multiple_pretrained_weights(
            datasets=datasets,
            backbone=backbone,
            weights_dir="pretrained_weights"
        )
        
        if not weights_dict:
            logger.warning("未能加载任何预训练权重，将使用随机初始化")
            return model
        
        logger.info(f"成功加载 {len(weights_dict)} 个预训练权重")
        
        # 显示相似度分数
        logger.info("标签相似度分数:")
        for dataset, score in mixer.similarity_scores.items():
            logger.info(f"  {dataset}: {score:.3f}")
        
        # 混合权重
        mixed_weights = mixer.mix_weights_adaptive(weights_dict, mixing_strategy)
        
        # 调整分割头
        mixer._adjust_segmentation_head(mixed_weights, model)
        
        # 加载混合权重到模型
        missing_keys, unexpected_keys = model.load_state_dict(mixed_weights, strict=False)
        logger.info(f"混合权重加载完成，缺失键: {len(missing_keys)}, 意外键: {len(unexpected_keys)}")
        
        # 保存混合权重
        if cfg['multi_pretrain'].get('save_mixed_weights', False):
            metadata = {
                'datasets': datasets,
                'mixing_strategy': mixing_strategy,
                'backbone': backbone,
                'target_classes': target_classes,
                'similarity_scores': mixer.similarity_scores
            }
            
            os.makedirs(os.path.dirname(mixed_weights_path), exist_ok=True)
            mixer.save_mixed_weights(mixed_weights, mixed_weights_path, metadata)
            logger.info(f"混合权重已保存到: {mixed_weights_path}")
    
    logger.info("=== 多预训练权重混合完成 ===")
    return model

def modify_train_config():
    """修改训练配置以使用混合权重"""
    # 读取原始配置
    with open('config_multi_pretrain.yaml', 'r', encoding='utf-8') as f:
        multi_cfg = yaml.safe_load(f)
    
    # 创建适配现有训练脚本的配置
    train_cfg = {
        'model_path': '',  # 使用混合权重，不需要预训练模型路径
        'classes_path': 'model_data/voc_classes.txt',  # 需要创建类别文件
        'input_shape': multi_cfg['data']['input_shape'],
        'backbone': multi_cfg['model']['backbone'],
        'pretrained': False,  # 不使用标准预训练权重
        'model_name': 'unet',
        'dataset_path': multi_cfg['data']['dataset_path'],
        'num_classes': multi_cfg['data']['num_classes'],
        
        # 训练参数
        'Init_Epoch': 0,
        'Freeze_Epoch': 50,
        'Freeze_batch_size': 8,
        'UnFreeze_Epoch': 100,
        'Unfreeze_batch_size': 4,
        'Freeze_Train': True,
        'Init_lr': 1e-4,
        'Min_lr': 1e-7,
        'optimizer_type': 'adam',
        'momentum': 0.9,
        'weight_decay': 0,
        'lr_decay_type': 'cos',
        'save_period': 5,
        'save_dir': f'logs/multi_pretrain_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'eval_flag': True,
        'eval_period': 5,
        'num_workers': 4,
        'cuda': True,
        'fp16': False,
        'dice_loss': True,
        'focal_loss': False,
        'cls_weights': False,
        'label_smoothing': 0,
    }
    
    return train_cfg

def create_classes_file():
    """创建类别文件"""
    with open('config_multi_pretrain.yaml', 'r', encoding='utf-8') as f:
        cfg = yaml.safe_load(f)
    
    classes = cfg['data']['class_names']
    
    # 创建类别文件
    os.makedirs('model_data', exist_ok=True)
    with open('model_data/voc_classes.txt', 'w', encoding='utf-8') as f:
        for cls in classes:
            f.write(f"{cls}\n")
    
    logger.info(f"类别文件已创建: model_data/voc_classes.txt")
    logger.info(f"类别数量: {len(classes)}")

def main():
    """主函数"""
    logger.info("=== 启动多预训练数据集权重混合训练 ===")
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        logger.info(f"CUDA可用，设备数量: {torch.cuda.device_count()}")
        logger.info(f"当前设备: {torch.cuda.get_device_name()}")
    else:
        logger.info("CUDA不可用，将使用CPU训练")
    
    # 创建类别文件
    create_classes_file()
    
    # 创建混合预训练权重的模型
    mixed_model = create_mixed_pretrain_model()
    
    # 保存混合模型以供训练脚本使用
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = f'logs/multi_pretrain_{timestamp}'
    os.makedirs(save_dir, exist_ok=True)
    
    model_save_path = os.path.join(save_dir, 'mixed_pretrain_model.pth')
    torch.save({
        'model_state_dict': mixed_model.state_dict(),
        'model_config': {
            'num_classes': 29,
            'backbone': 'resnet50',
            'pretrained': False,
            'dropout_rate': 0.2,
            'use_attention': True,
            'attention_type': 'cbam'
        }
    }, model_save_path)
    
    logger.info(f"混合预训练模型已保存到: {model_save_path}")
    
    # 修改训练配置
    train_cfg = modify_train_config()
    train_cfg['save_dir'] = save_dir
    train_cfg['model_path'] = model_save_path  # 使用混合权重模型
    
    logger.info("=== 开始训练 ===")
    logger.info(f"保存目录: {save_dir}")
    logger.info(f"数据集路径: {train_cfg['dataset_path']}")
    logger.info(f"类别数量: {train_cfg['num_classes']}")
    logger.info(f"输入尺寸: {train_cfg['input_shape']}")
    logger.info(f"骨干网络: {train_cfg['backbone']}")
    
    # 设置环境变量以传递配置给训练脚本
    os.environ['MULTI_PRETRAIN_CONFIG'] = str(train_cfg)
    
    # 启动训练（使用现有的训练脚本）
    try:
        # 这里可以调用现有的训练脚本
        logger.info("请手动运行以下命令开始训练:")
        logger.info(f"python train.py --model_path {model_save_path} --save_dir {save_dir}")
        
        # 或者直接在这里启动训练
        print("\n" + "="*60)
        print("多预训练权重混合模型已准备完成！")
        print(f"模型保存路径: {model_save_path}")
        print(f"训练日志目录: {save_dir}")
        print("="*60)
        
        # 提示用户下一步操作
        print("\n下一步操作:")
        print("1. 检查混合权重效果")
        print("2. 开始训练")
        print("3. 监控训练过程")
        
    except Exception as e:
        logger.error(f"训练启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
