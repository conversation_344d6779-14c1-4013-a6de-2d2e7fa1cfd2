#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问卷星自动上传工具 - Edge版本
使用Microsoft Edge浏览器
"""

import os
import time
import glob
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
from datetime import datetime

def setup_logging():
    """设置日志"""
    log_filename = f"wenjuanxing_edge_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def setup_edge_driver():
    """设置Edge浏览器驱动"""
    try:
        from selenium.webdriver.edge.options import Options
        
        edge_options = Options()
        edge_options.add_argument('--no-sandbox')
        edge_options.add_argument('--disable-dev-shm-usage')
        edge_options.add_argument('--disable-blink-features=AutomationControlled')
        edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        edge_options.add_experimental_option('useAutomationExtension', False)
        
        # 尝试多种方式创建driver
        driver = None
        
        # 方法1: 使用webdriver-manager
        try:
            from webdriver_manager.microsoft import EdgeChromiumDriverManager
            from selenium.webdriver.edge.service import Service
            
            service = Service(EdgeChromiumDriverManager().install())
            driver = webdriver.Edge(service=service, options=edge_options)
            print("✅ 使用webdriver-manager的EdgeDriver")
        except Exception as e:
            print(f"方法1失败: {e}")
        
        # 方法2: 直接使用系统PATH中的msedgedriver
        if not driver:
            try:
                driver = webdriver.Edge(options=edge_options)
                print("✅ 使用系统PATH中的EdgeDriver")
            except Exception as e:
                print(f"方法2失败: {e}")
        
        # 方法3: 尝试使用当前目录的msedgedriver
        if not driver:
            try:
                from selenium.webdriver.edge.service import Service
                service = Service('./msedgedriver.exe')
                driver = webdriver.Edge(service=service, options=edge_options)
                print("✅ 使用当前目录的EdgeDriver")
            except Exception as e:
                print(f"方法3失败: {e}")
        
        if not driver:
            print("❌ 无法创建Edge浏览器驱动")
            print("请确保已安装Microsoft Edge浏览器")
            return None, None
        
        # 隐藏自动化标识
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        driver.maximize_window()
        wait = WebDriverWait(driver, 20)
        
        return driver, wait
        
    except Exception as e:
        print(f"设置Edge浏览器驱动失败: {e}")
        return None, None

def load_images(image_folder, count=20):
    """加载图片"""
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif']
    images = []
    
    for ext in image_extensions:
        pattern = os.path.join(image_folder, ext)
        images.extend(glob.glob(pattern))
        pattern_upper = os.path.join(image_folder, ext.upper())
        images.extend(glob.glob(pattern_upper))
    
    images.sort()
    return images[:count]

def test_page_access(driver, wait, logger, url):
    """测试页面访问"""
    try:
        logger.info("正在打开问卷编辑页面...")
        driver.get(url)
        time.sleep(5)
        
        # 检查页面标题
        title = driver.title
        logger.info(f"页面标题: {title}")
        
        # 检查是否需要登录
        current_url = driver.current_url
        if "login" in current_url.lower() or "登录" in title:
            logger.info("检测到需要登录")
            print("\n🔐 需要登录")
            print("请在浏览器中手动完成登录...")
            input("登录完成后，按回车键继续...")
            
            # 重新导航到编辑页面
            driver.get(url)
            time.sleep(3)
        
        # 截图保存当前页面
        screenshot_path = f"page_access_edge_{datetime.now().strftime('%H%M%S')}.png"
        driver.save_screenshot(screenshot_path)
        logger.info(f"页面截图已保存: {screenshot_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"页面访问失败: {e}")
        return False

def find_elements_on_page(driver, wait, logger):
    """查找页面元素"""
    try:
        logger.info("开始查找页面元素...")
        
        # 查找可能的按钮和元素
        possible_selectors = [
            "//span[contains(text(), '添加题目')]",
            "//button[contains(text(), '添加题目')]",
            "//div[contains(text(), '添加题目')]",
            "//span[contains(text(), '添加')]",
            "//button[contains(text(), '添加')]",
            "//*[contains(@class, 'add')]",
            "//*[contains(@id, 'add')]",
            "//span[contains(text(), '新增')]",
            "//button[contains(text(), '新增')]"
        ]
        
        found_elements = []
        for selector in possible_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    for elem in elements:
                        try:
                            text = elem.text
                            if text:
                                found_elements.append(f"选择器: {selector} | 文本: {text}")
                        except:
                            found_elements.append(f"选择器: {selector} | 文本: [无法获取]")
            except:
                continue
        
        if found_elements:
            logger.info("找到的页面元素:")
            for elem in found_elements[:10]:  # 只显示前10个
                logger.info(f"  {elem}")
        else:
            logger.warning("未找到任何相关元素")
        
        # 查找所有按钮
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        logger.info(f"页面共有 {len(all_buttons)} 个按钮")
        
        # 查找所有span元素
        all_spans = driver.find_elements(By.TAG_NAME, "span")
        logger.info(f"页面共有 {len(all_spans)} 个span元素")
        
        return len(found_elements) > 0
        
    except Exception as e:
        logger.error(f"查找页面元素失败: {e}")
        return False

def interactive_element_finder(driver, wait, logger):
    """交互式元素查找"""
    try:
        logger.info("进入交互式元素查找模式...")
        
        while True:
            print("\n🔍 交互式元素查找 (Edge版本)")
            print("1. 查找包含特定文本的元素")
            print("2. 查找特定标签的所有元素")
            print("3. 点击指定元素")
            print("4. 截图保存当前页面")
            print("5. 刷新页面")
            print("6. 退出")
            
            choice = input("请选择操作 (1-6): ").strip()
            
            if choice == "1":
                text = input("请输入要查找的文本: ").strip()
                if text:
                    elements = driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                    logger.info(f"找到 {len(elements)} 个包含 '{text}' 的元素")
                    for i, elem in enumerate(elements[:5]):
                        try:
                            logger.info(f"  元素 {i+1}: {elem.tag_name} - {elem.text}")
                        except:
                            logger.info(f"  元素 {i+1}: {elem.tag_name} - [无法获取文本]")
            
            elif choice == "2":
                tag = input("请输入标签名 (如: button, span, div): ").strip()
                if tag:
                    elements = driver.find_elements(By.TAG_NAME, tag)
                    logger.info(f"找到 {len(elements)} 个 {tag} 元素")
                    for i, elem in enumerate(elements[:10]):
                        try:
                            text = elem.text[:50] if elem.text else "[无文本]"
                            logger.info(f"  {tag} {i+1}: {text}")
                        except:
                            logger.info(f"  {tag} {i+1}: [无法获取文本]")
            
            elif choice == "3":
                text = input("请输入要点击的元素文本: ").strip()
                if text:
                    try:
                        element = driver.find_element(By.XPATH, f"//*[contains(text(), '{text}')]")
                        element.click()
                        logger.info(f"已点击包含 '{text}' 的元素")
                        time.sleep(2)
                    except Exception as e:
                        logger.error(f"点击失败: {e}")
            
            elif choice == "4":
                screenshot_path = f"interactive_edge_screenshot_{datetime.now().strftime('%H%M%S')}.png"
                driver.save_screenshot(screenshot_path)
                logger.info(f"截图已保存: {screenshot_path}")
            
            elif choice == "5":
                driver.refresh()
                time.sleep(3)
                logger.info("页面已刷新")
            
            elif choice == "6":
                break
            
            else:
                print("无效选择，请重试")
        
        return True
        
    except Exception as e:
        logger.error(f"交互式查找失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 问卷星自动上传工具 - Edge版本")
    print("=" * 50)
    
    # 配置
    image_folder = r"D:\1a_taohuacun"
    questionnaire_url = "https://www.wjx.cn/wjxdesignnew/designnew.aspx?version=7&openType=redesign&curid=316618829&nqt=&sguid=8d03ce0d-b368-47b9-8646-e129a13932f2"
    
    # 设置日志
    logger = setup_logging()
    
    # 检查图片文件夹
    if not os.path.exists(image_folder):
        print(f"❌ 图片文件夹不存在: {image_folder}")
        return
    
    # 加载图片
    images = load_images(image_folder, 20)
    if len(images) < 10:
        print(f"❌ 图片数量不足，需要至少10张，当前只有{len(images)}张")
        return
    
    print(f"📁 图片文件夹: {image_folder}")
    print(f"🖼️ 找到图片数量: {len(images)}")
    print(f"🌐 问卷地址: {questionnaire_url}")
    print(f"🌐 使用浏览器: Microsoft Edge")
    
    # 确认执行
    confirm = input("\n确认开始测试? (y/n): ").lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    # 设置浏览器
    driver, wait = setup_edge_driver()
    if not driver:
        return
    
    try:
        # 测试页面访问
        if not test_page_access(driver, wait, logger, questionnaire_url):
            logger.error("页面访问失败")
            return
        
        # 查找页面元素
        if not find_elements_on_page(driver, wait, logger):
            logger.warning("未找到预期元素，进入交互式模式")
        
        # 交互式元素查找
        interactive_element_finder(driver, wait, logger)
        
        logger.info("🎉 测试完成！")
        print("\n🎉 测试完成！")
        print("请查看日志文件和截图了解页面情况")
        
        input("\n按回车键关闭浏览器...")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        print(f"\n❌ 测试失败: {e}")
        
    finally:
        if driver:
            driver.quit()
            logger.info("浏览器已关闭")

if __name__ == "__main__":
    main()
