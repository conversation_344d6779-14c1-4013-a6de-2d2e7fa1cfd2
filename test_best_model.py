#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import torch
import numpy as np
import os
import sys
from datetime import datetime

# 添加UNet_Demo路径
sys.path.append('UNet_Demo/UNet_Demo')

try:
    from train import unet
    from utils.utils_metrics import compute_mIoU_tensor
    from train_extreme_simple import SimpleUnetDataset, create_extreme_weights
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

def load_best_model():
    """加载最佳模型"""
    print("🔍 查找最佳模型...")
    
    # 查找最高mIoU的模型
    model_files = [f for f in os.listdir('.') if f.startswith('best_extreme_simple_miou_')]
    if not model_files:
        print("❌ 未找到训练好的模型")
        return None, None
    
    # 提取mIoU值并排序
    model_scores = []
    for file in model_files:
        try:
            miou_str = file.split('_')[-1].replace('.pth', '')
            miou = float(miou_str)
            model_scores.append((miou, file))
        except:
            continue
    
    model_scores.sort(reverse=True)
    best_miou, best_file = model_scores[0]
    
    print(f"📊 找到最佳模型: {best_file}")
    print(f"🎯 最佳mIoU: {best_miou:.4f}")
    
    # 创建模型
    model = unet(
        num_classes=29,
        backbone='resnet50',
        pretrained=True,
        dropout_rate=0.3,
        use_attention=True,
        attention_type='cbam'
    )
    
    # 加载权重
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    checkpoint = torch.load(best_file, map_location=device)
    
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
        epoch = checkpoint.get('epoch', 'unknown')
        train_loss = checkpoint.get('train_loss', 'unknown')
        val_loss = checkpoint.get('val_loss', 'unknown')
        
        print(f"📈 模型信息:")
        print(f"  训练轮数: {epoch}")
        print(f"  训练损失: {train_loss}")
        print(f"  验证损失: {val_loss}")
    else:
        model.load_state_dict(checkpoint)
    
    model = model.to(device)
    model.eval()
    
    return model, best_miou

def test_model_performance(model):
    """测试模型性能"""
    print("\n🧪 测试模型性能...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 准备测试数据
    VOCdevkit_path = "UNet_Demo/UNet_Demo/VOCdevkit"
    
    try:
        with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/val.txt"), "r") as f:
            val_lines = f.readlines()
        
        print(f"📊 验证集样本数: {len(val_lines)}")
        
        # 创建验证数据集
        val_dataset = SimpleUnetDataset(
            val_lines, 
            input_shape=(512, 512), 
            num_classes=29, 
            train=False, 
            dataset_path=VOCdevkit_path
        )
        
        val_loader = torch.utils.data.DataLoader(
            val_dataset, batch_size=4, shuffle=False, num_workers=0
        )
        
        # 测试性能
        total_miou = 0
        total_samples = 0
        class_ious = np.zeros(29)
        class_counts = np.zeros(29)
        
        print("🔄 开始评估...")
        
        with torch.no_grad():
            for batch_idx, (images, targets) in enumerate(val_loader):
                images = images.to(device)
                targets = targets.to(device)
                
                outputs = model(images)
                pred = torch.argmax(outputs, dim=1)
                
                # 计算mIoU
                miou, per_class_ious = compute_mIoU_tensor(
                    pred, targets, num_classes=29, return_per_class=True
                )
                
                total_miou += miou
                total_samples += 1
                
                # 统计每个类别的IoU
                for cls in range(29):
                    if per_class_ious[cls] > 0:
                        class_ious[cls] += per_class_ious[cls]
                        class_counts[cls] += 1
                
                if batch_idx % 2 == 0:
                    print(f"  批次 {batch_idx+1}/{len(val_loader)}, mIoU: {miou:.4f}")
        
        avg_miou = total_miou / total_samples
        
        print(f"\n📊 最终测试结果:")
        print(f"🎯 平均mIoU: {avg_miou:.4f}")
        
        # 计算每个类别的平均IoU
        print(f"\n📋 各类别IoU详情:")
        for cls in range(29):
            if class_counts[cls] > 0:
                avg_class_iou = class_ious[cls] / class_counts[cls]
                print(f"  类别 {cls:2d}: {avg_class_iou:.4f} (样本数: {int(class_counts[cls])})")
            else:
                print(f"  类别 {cls:2d}: 0.0000 (无样本)")
        
        return avg_miou
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return None

def analyze_model_weights():
    """分析模型权重分布"""
    print("\n🔍 分析极端权重策略效果...")
    
    weights = create_extreme_weights()
    
    print("📊 权重分布统计:")
    print(f"  最高权重: {weights.max().item():.2f}x (类别 {weights.argmax().item()})")
    print(f"  最低权重: {weights.min().item():.4f}x (类别 {weights.argmin().item()})")
    print(f"  权重范围: {weights.max().item() / weights.min().item():.0f}倍差距")
    
    # 统计不同权重范围的类别数量
    extreme_high = (weights >= 50).sum().item()
    high = ((weights >= 5) & (weights < 50)).sum().item()
    normal = ((weights >= 0.5) & (weights < 5)).sum().item()
    low = ((weights >= 0.1) & (weights < 0.5)).sum().item()
    extreme_low = (weights < 0.1).sum().item()
    
    print(f"\n📈 权重分布:")
    print(f"  极高权重 (≥50x): {extreme_high} 个类别")
    print(f"  高权重 (5-50x): {high} 个类别")
    print(f"  正常权重 (0.5-5x): {normal} 个类别")
    print(f"  低权重 (0.1-0.5x): {low} 个类别")
    print(f"  极低权重 (<0.1x): {extreme_low} 个类别")

def main():
    """主函数"""
    print("🔬 最佳模型性能测试")
    print("=" * 50)
    
    # 加载最佳模型
    model, best_miou = load_best_model()
    if model is None:
        return
    
    # 分析权重策略
    analyze_model_weights()
    
    # 测试模型性能
    test_miou = test_model_performance(model)
    
    if test_miou is not None:
        print(f"\n🎊 测试完成!")
        print(f"📊 训练时最佳mIoU: {best_miou:.4f}")
        print(f"🧪 独立测试mIoU: {test_miou:.4f}")
        print(f"📈 性能一致性: {(test_miou/best_miou)*100:.1f}%")
        
        if test_miou >= 0.25:
            print("🎯 模型性能优秀！")
        elif test_miou >= 0.20:
            print("✅ 模型性能良好")
        elif test_miou >= 0.15:
            print("⚠️ 模型性能一般")
        else:
            print("❌ 模型性能需要改进")
        
        # 与目标对比
        target_miou = 0.45
        gap = target_miou - test_miou
        progress = (test_miou / target_miou) * 100
        
        print(f"\n🎯 目标达成情况:")
        print(f"  目标mIoU: {target_miou:.4f}")
        print(f"  当前mIoU: {test_miou:.4f}")
        print(f"  完成度: {progress:.1f}%")
        print(f"  还需提升: {gap:.4f}")
        
        if progress >= 90:
            print("🏆 接近目标，继续优化！")
        elif progress >= 70:
            print("🎯 进展良好，加油！")
        elif progress >= 50:
            print("💪 已过半程，继续努力！")
        else:
            print("🚀 需要更多优化策略")

if __name__ == "__main__":
    main()
