#!/usr/bin/env python3
"""
一键训练脚本
自动化整个训练流程，包括数据准备、模型训练、评估和可视化
"""

import os
import sys
import yaml
import argparse
import subprocess
import shutil
from datetime import datetime
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    log_dir = "logs/one_click_train"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'one_click_train_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

def check_dependencies():
    """检查依赖"""
    logger = logging.getLogger(__name__)
    
    required_packages = [
        'torch', 'torchvision', 'numpy', 'PIL', 'opencv-python',
        'matplotlib', 'seaborn', 'tqdm', 'tensorboard', 'yaml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少依赖包: {missing_packages}")
        logger.info("请运行: pip install " + " ".join(missing_packages))
        return False
    
    logger.info("所有依赖包检查通过")
    return True

def check_data_structure(data_dir):
    """检查数据结构"""
    logger = logging.getLogger(__name__)
    
    required_dirs = [
        'VOCdevkit/VOC2025/JPEGImages',
        'VOCdevkit/VOC2025/SegmentationClass',
        'VOCdevkit/VOC2025/ImageSets/Segmentation'
    ]
    
    for dir_path in required_dirs:
        full_path = os.path.join(data_dir, dir_path)
        if not os.path.exists(full_path):
            logger.error(f"数据目录不存在: {full_path}")
            return False
    
    # 检查训练和验证列表文件
    train_list = os.path.join(data_dir, 'VOCdevkit/VOC2025/ImageSets/Segmentation/train.txt')
    val_list = os.path.join(data_dir, 'VOCdevkit/VOC2025/ImageSets/Segmentation/val.txt')
    
    if not os.path.exists(train_list):
        logger.error(f"训练列表文件不存在: {train_list}")
        return False
    
    if not os.path.exists(val_list):
        logger.error(f"验证列表文件不存在: {val_list}")
        return False
    
    logger.info("数据结构检查通过")
    return True

def prepare_config(base_config, output_path, custom_settings=None):
    """准备配置文件"""
    logger = logging.getLogger(__name__)
    
    # 加载基础配置
    with open(base_config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 应用自定义设置
    if custom_settings:
        for key, value in custom_settings.items():
            keys = key.split('.')
            current = config
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            current[keys[-1]] = value
    
    # 保存配置
    with open(output_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    logger.info(f"配置文件准备完成: {output_path}")
    return config

def run_training(config_path, resume_path=None):
    """运行训练"""
    logger = logging.getLogger(__name__)
    
    cmd = ['python', 'train_advanced.py', '--config', config_path]
    
    if resume_path:
        cmd.extend(['--resume', resume_path])
    
    logger.info(f"开始训练: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("训练完成")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"训练失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False, e.stderr

def run_evaluation(model_path, config_path, test_image_dir, test_label_dir, output_dir):
    """运行评估"""
    logger = logging.getLogger(__name__)
    
    cmd = [
        'python', 'advanced_evaluate.py',
        '--model', model_path,
        '--config', config_path,
        '--image_dir', test_image_dir,
        '--label_dir', test_label_dir,
        '--output_dir', output_dir
    ]
    
    logger.info(f"开始评估: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("评估完成")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"评估失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False, e.stderr

def start_tensorboard(log_dir):
    """启动TensorBoard"""
    logger = logging.getLogger(__name__)
    
    cmd = ['tensorboard', '--logdir', log_dir, '--port', '6006']
    
    try:
        process = subprocess.Popen(cmd)
        logger.info(f"TensorBoard已启动，访问地址: http://localhost:6006")
        return process
    except Exception as e:
        logger.error(f"启动TensorBoard失败: {e}")
        return None

def create_summary_report(training_dir, evaluation_dir, output_path):
    """创建总结报告"""
    logger = logging.getLogger(__name__)
    
    report = []
    report.append("# U-net训练总结报告")
    report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 训练信息
    report.append("## 训练信息")
    report.append(f"训练目录: {training_dir}")
    
    # 查找最佳模型
    model_files = []
    if os.path.exists(training_dir):
        for file in os.listdir(training_dir):
            if file.endswith('.pth'):
                model_files.append(file)
    
    if model_files:
        report.append(f"生成的模型文件: {', '.join(model_files)}")
    
    # 评估结果
    if evaluation_dir and os.path.exists(evaluation_dir):
        report.append("")
        report.append("## 评估结果")
        
        metrics_file = os.path.join(evaluation_dir, 'overall_metrics.txt')
        if os.path.exists(metrics_file):
            with open(metrics_file, 'r') as f:
                report.append("```")
                report.append(f.read())
                report.append("```")
    
    # 文件位置
    report.append("")
    report.append("## 文件位置")
    report.append(f"- 训练日志: {training_dir}")
    report.append(f"- 评估结果: {evaluation_dir}")
    report.append(f"- TensorBoard日志: {training_dir}")
    
    # 下一步建议
    report.append("")
    report.append("## 下一步建议")
    report.append("1. 查看TensorBoard了解训练过程")
    report.append("2. 分析评估结果，特别是混淆矩阵和每类IoU")
    report.append("3. 如果性能不满意，考虑调整超参数或使用模型集成")
    report.append("4. 使用最佳模型进行实际预测")
    
    # 保存报告
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    logger.info(f"总结报告已生成: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='一键训练U-net模型')
    parser.add_argument('--config', type=str, default='config.yaml', help='基础配置文件')
    parser.add_argument('--data_dir', type=str, default='.', help='数据根目录')
    parser.add_argument('--output_dir', type=str, default='one_click_results', help='输出目录')
    parser.add_argument('--skip_training', action='store_true', help='跳过训练（仅评估）')
    parser.add_argument('--skip_evaluation', action='store_true', help='跳过评估')
    parser.add_argument('--model_path', type=str, default=None, help='预训练模型路径（用于跳过训练时）')
    parser.add_argument('--tensorboard', action='store_true', help='启动TensorBoard')
    
    # 自定义设置
    parser.add_argument('--backbone', type=str, default=None, help='骨干网络')
    parser.add_argument('--epochs', type=int, default=None, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=None, help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=None, help='学习率')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    logger.info("开始一键训练流程")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查数据结构
    if not check_data_structure(args.data_dir):
        return
    
    # 创建输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = os.path.join(args.output_dir, f'run_{timestamp}')
    os.makedirs(output_dir, exist_ok=True)
    
    # 准备自定义设置
    custom_settings = {}
    if args.backbone:
        custom_settings['model.backbone'] = args.backbone
    if args.epochs:
        custom_settings['train.total_epochs'] = args.epochs
    if args.batch_size:
        custom_settings['train.freeze_batch_size'] = args.batch_size
        custom_settings['train.unfreeze_batch_size'] = max(2, args.batch_size // 2)
    if args.learning_rate:
        custom_settings['train.init_lr'] = args.learning_rate
    
    # 准备配置文件
    config_path = os.path.join(output_dir, 'config.yaml')
    config = prepare_config(args.config, config_path, custom_settings)
    
    training_dir = None
    model_path = args.model_path
    
    # 训练阶段
    if not args.skip_training:
        logger.info("=" * 50)
        logger.info("开始训练阶段")
        logger.info("=" * 50)
        
        success, output = run_training(config_path)
        if success:
            # 查找生成的模型
            save_dir = config['train']['save_dir']
            training_dir = save_dir
            
            # 查找最佳模型
            if os.path.exists(save_dir):
                model_files = [f for f in os.listdir(save_dir) if f.endswith('.pth')]
                if 'best_model.pth' in model_files:
                    model_path = os.path.join(save_dir, 'best_model.pth')
                elif 'final_model.pth' in model_files:
                    model_path = os.path.join(save_dir, 'final_model.pth')
                elif model_files:
                    model_path = os.path.join(save_dir, model_files[-1])
        else:
            logger.error("训练失败，停止流程")
            return
    
    # 评估阶段
    evaluation_dir = None
    if not args.skip_evaluation and model_path:
        logger.info("=" * 50)
        logger.info("开始评估阶段")
        logger.info("=" * 50)
        
        # 使用验证集进行评估
        test_image_dir = os.path.join(args.data_dir, 'VOCdevkit/VOC2025/JPEGImages')
        test_label_dir = os.path.join(args.data_dir, 'VOCdevkit/VOC2025/SegmentationClass')
        evaluation_dir = os.path.join(output_dir, 'evaluation')
        
        success, output = run_evaluation(
            model_path, config_path, test_image_dir, test_label_dir, evaluation_dir
        )
        
        if not success:
            logger.warning("评估失败，但继续流程")
    
    # 启动TensorBoard
    if args.tensorboard and training_dir:
        start_tensorboard(training_dir)
    
    # 生成总结报告
    report_path = os.path.join(output_dir, 'summary_report.md')
    create_summary_report(training_dir, evaluation_dir, report_path)
    
    logger.info("=" * 50)
    logger.info("一键训练流程完成")
    logger.info("=" * 50)
    logger.info(f"结果保存在: {output_dir}")
    logger.info(f"总结报告: {report_path}")
    
    if model_path:
        logger.info(f"最佳模型: {model_path}")
    
    if args.tensorboard:
        logger.info("TensorBoard: http://localhost:6006")

if __name__ == '__main__':
    main()
