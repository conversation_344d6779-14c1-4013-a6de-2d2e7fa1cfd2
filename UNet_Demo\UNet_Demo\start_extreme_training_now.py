#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
立即启动极端类别平衡训练
使用现有的训练框架，但应用极端类别权重
"""

import os
import sys
import numpy as np
import torch
import torch.backends.cudnn as cudnn
import torch.optim as optim
from torch.utils.data import DataLoader

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nets.unet import Unet
from utils.callbacks import LossHistory, EvalCallback
from utils.dataloader import SegmentationDataset
from utils.utils_fit import fit_one_epoch
from utils.utils_metrics import f_score
from utils.losses import CombinedLoss, compute_class_weights

def main():
    """主函数"""
    print("🚀 启动极端类别平衡训练")
    print("=" * 50)

    # 训练参数
    Cuda = True
    distributed = False
    sync_bn = False
    fp16 = True

    # 数据参数
    num_classes = 29
    backbone = "resnet50"
    pretrained = True
    model_path = ""
    input_shape = [512, 512]

    # 训练参数
    Init_Epoch = 0
    Freeze_Epoch = 50
    Freeze_batch_size = 4
    UnFreeze_Epoch = 200
    Unfreeze_batch_size = 4
    Freeze_Train = True

    # 优化器参数
    Init_lr = 1e-4
    Min_lr = Init_lr * 0.01
    optimizer_type = "adamw"
    momentum = 0.937
    weight_decay = 5e-4

    # 学习率调度
    lr_decay_type = "cos"
    save_period = 10
    save_dir = 'logs'
    eval_flag = True
    eval_period = 5

    # 数据路径
    VOCdevkit_path = 'VOCdevkit'

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and Cuda else 'cpu')
    local_rank = 0

    print(f"使用设备: {device}")

    # 创建模型
    model = Unet(num_classes=num_classes, backbone=backbone, pretrained=pretrained)

    if not pretrained:
        weights_init(model)
    if model_path != '':
        print('Load weights {}.'.format(model_path))
        model_dict = model.state_dict()
        pretrained_dict = torch.load(model_path, map_location=device)
        pretrained_dict = {k: v for k, v in pretrained_dict.items() if np.shape(model_dict[k]) == np.shape(v)}
        model_dict.update(pretrained_dict)
        model.load_state_dict(model_dict)

    # 计算极端类别权重
    print("计算极端类别权重...")

    # 创建极端权重 - 手动设置
    class_weights = torch.ones(num_classes)

    # 困难类别 - 极高权重
    difficult_classes = {8: 100.0, 10: 90.0, 18: 80.0, 23: 70.0, 26: 60.0, 27: 50.0, 28: 40.0}
    for class_id, weight in difficult_classes.items():
        class_weights[class_id] = weight

    # 优秀类别 - 极低权重
    excellent_classes = [0, 3, 13, 2, 21]
    for class_id in excellent_classes:
        class_weights[class_id] = 0.01

    print("极端类别权重:")
    for i, weight in enumerate(class_weights):
        if weight != 1.0:
            print(f"  类别 {i}: {weight:.2f}")

    # 损失函数配置
    loss_cfg = {
        'use_ce': True,
        'use_dice': True,
        'use_focal': True,
        'use_lovasz': True,
        'weight_ce': 0.1,      # 降低CE权重
        'weight_dice': 2.5,    # 增加Dice权重
        'weight_focal': 3.0,   # 大幅增加Focal权重
        'weight_lovasz': 1.8,  # Lovász权重
        'focal_gamma': 6.0,    # 极高gamma值
        'focal_alpha': None,   # 使用类别权重
        'ignore_index': 255,
        'label_smoothing': 0.15
    }

    # 创建损失函数
    loss = CombinedLoss(loss_cfg, class_weights)

    # 记录Loss
    time_str = datetime.datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
    log_dir = os.path.join(save_dir, "extreme_balance_" + str(time_str))
    loss_history = LossHistory(log_dir, model, input_shape=input_shape)

    # torch 1.2不支持amp，建议使用torch 1.7.1及以上正确使用fp16
    if fp16:
        from torch.cuda.amp import GradScaler as GradScaler
        scaler = GradScaler()
    else:
        scaler = None

    model.train()
    model_train = model

    # 分布式训练参数
    ngpus_per_node = 1

    if sync_bn and ngpus_per_node > 1 and distributed:
        model_train = torch.nn.SyncBatchNorm.convert_sync_batchnorm(model_train)
    elif sync_bn:
        print("Sync_bn is not support in one gpu or not distributed.")

    if Cuda:
        if distributed:
            model_train = model_train.cuda(local_rank)
            model_train = torch.nn.parallel.DistributedDataParallel(model_train, device_ids=[local_rank], find_unused_parameters=True)
        else:
            model_train = torch.nn.DataParallel(model)
            cudnn.benchmark = True
            model_train = model_train.cuda()

    # 读取数据集对应的txt
    with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/train.txt"), "r") as f:
        train_lines = f.readlines()
    with open(os.path.join(VOCdevkit_path, "VOC2025/ImageSets/Segmentation/val.txt"), "r") as f:
        val_lines = f.readlines()
    num_train = len(train_lines)
    num_val = len(val_lines)

    print(f"训练样本数: {num_train}, 验证样本数: {num_val}")

    if True:
        UnFreeze_flag = False
        # 冻结一定部分训练
        if Freeze_Train:
            for param in model.backbone.parameters():
                param.requires_grad = False

        # 如果不冻结训练的话，直接设置batch_size为Unfreeze_batch_size
        batch_size = Freeze_batch_size if Freeze_Train else Unfreeze_batch_size

        # 判断当前batch_size，自适应调整学习率
        nbs = 16
        lr_limit_max = 1e-4 if optimizer_type == 'adam' else 5e-2
        lr_limit_min = 3e-5 if optimizer_type == 'adam' else 5e-4
        Init_lr_fit = min(max(batch_size / nbs * Init_lr, lr_limit_min), lr_limit_max)
        Min_lr_fit = min(max(batch_size / nbs * Min_lr, lr_limit_min * 1e-2), lr_limit_max * 1e-2)

        # 根据optimizer_type选择优化器
        pg0, pg1, pg2 = [], [], []
        for k, v in model.named_modules():
            if hasattr(v, "bias") and isinstance(v.bias, nn.Parameter):
                pg2.append(v.bias)
            if isinstance(v, nn.BatchNorm2d) or "bn" in k:
                pg0.append(v.weight)
            elif hasattr(v, "weight") and isinstance(v.weight, nn.Parameter):
                pg1.append(v.weight)

        optimizer = {
            'adam': optim.Adam(pg0, Init_lr_fit, betas=(momentum, 0.999)),
            'adamw': optim.AdamW(pg0, Init_lr_fit, betas=(momentum, 0.999), weight_decay=weight_decay),
            'sgd': optim.SGD(pg0, Init_lr_fit, momentum=momentum, nesterov=True)
        }[optimizer_type]
        optimizer.add_param_group({"params": pg1, "weight_decay": weight_decay})
        optimizer.add_param_group({"params": pg2})

        # 获得学习率下降的公式
        lr_scheduler_func = get_lr_scheduler(lr_decay_type, Init_lr_fit, Min_lr_fit, UnFreeze_Epoch)

        # 判断每一个世代的长度
        epoch_step = num_train // batch_size
        epoch_step_val = num_val // batch_size

        if epoch_step == 0 or epoch_step_val == 0:
            raise ValueError("数据集过小，无法继续进行训练，请扩充数据集。")

        # 构建数据集加载器
        train_dataset = SegmentationDataset([line.strip() for line in train_lines], VOCdevkit_path, input_shape, num_classes, True)
        val_dataset = SegmentationDataset([line.strip() for line in val_lines], VOCdevkit_path, input_shape, num_classes, False)

        if distributed:
            train_sampler = torch.utils.data.distributed.DistributedSampler(train_dataset, shuffle=True,)
            val_sampler = torch.utils.data.distributed.DistributedSampler(val_dataset, shuffle=False,)
            batch_size = batch_size // ngpus_per_node
            shuffle = False
        else:
            train_sampler = None
            val_sampler = None
            shuffle = True

        gen = DataLoader(train_dataset, shuffle=shuffle, batch_size=batch_size, num_workers=4, pin_memory=True,
                        drop_last=True, sampler=train_sampler)
        gen_val = DataLoader(val_dataset, shuffle=shuffle, batch_size=batch_size, num_workers=4, pin_memory=True,
                            drop_last=True, sampler=val_sampler)

        # 记录eval的map曲线
        if eval_flag:
            eval_callback = EvalCallback(model, input_shape, num_classes, val_lines, VOCdevkit_path, log_dir, Cuda, \
                                        eval_flag=eval_flag, period=eval_period)
        else:
            eval_callback = None

        # 开始模型训练
        for epoch in range(Init_Epoch, UnFreeze_Epoch):
            # 如果模型有冻结学习部分
            # 则解冻，并设置参数
            if epoch >= Freeze_Epoch and not UnFreeze_flag and Freeze_Train:
                batch_size = Unfreeze_batch_size

                # 判断当前batch_size，自适应调整学习率
                nbs = 16
                lr_limit_max = 1e-4 if optimizer_type == 'adam' else 5e-2
                lr_limit_min = 3e-5 if optimizer_type == 'adam' else 5e-4
                Init_lr_fit = min(max(batch_size / nbs * Init_lr, lr_limit_min), lr_limit_max)
                Min_lr_fit = min(max(batch_size / nbs * Min_lr, lr_limit_min * 1e-2), lr_limit_max * 1e-2)
                # 获得学习率下降的公式
                lr_scheduler_func = get_lr_scheduler(lr_decay_type, Init_lr_fit, Min_lr_fit, UnFreeze_Epoch)

                for param in model.backbone.parameters():
                    param.requires_grad = True

                epoch_step = num_train // batch_size
                epoch_step_val = num_val // batch_size

                if epoch_step == 0 or epoch_step_val == 0:
                    raise ValueError("数据集过小，无法继续进行训练，请扩充数据集。")

                if distributed:
                    batch_size = batch_size // ngpus_per_node

                gen = DataLoader(train_dataset, shuffle=shuffle, batch_size=batch_size, num_workers=4, pin_memory=True,
                                drop_last=True, sampler=train_sampler)
                gen_val = DataLoader(val_dataset, shuffle=shuffle, batch_size=batch_size, num_workers=4, pin_memory=True,
                                    drop_last=True, sampler=val_sampler)

                UnFreeze_flag = True

            set_optimizer_lr(optimizer, lr_scheduler_func, epoch)

            fit_one_epoch(model_train, model, loss_history, eval_callback, optimizer, epoch, epoch_step, epoch_step_val, gen, gen_val, UnFreeze_Epoch, Cuda, fp16, scaler, save_period, save_dir, local_rank)

            if distributed:
                dist.barrier()

        loss_history.writer.close()

def weights_init(net, init_type='normal', init_gain=0.02):
    """权重初始化"""
    def init_func(m):
        classname = m.__class__.__name__
        if hasattr(m, 'weight') and classname.find('Conv') != -1:
            if init_type == 'normal':
                torch.nn.init.normal_(m.weight.data, 0.0, init_gain)
            elif init_type == 'xavier':
                torch.nn.init.xavier_normal_(m.weight.data, gain=init_gain)
            elif init_type == 'kaiming':
                torch.nn.init.kaiming_normal_(m.weight.data, a=0, mode='fan_in')
            elif init_type == 'orthogonal':
                torch.nn.init.orthogonal_(m.weight.data, gain=init_gain)
            else:
                raise NotImplementedError('initialization method [%s] is not implemented' % init_type)
        elif classname.find('BatchNorm2d') != -1:
            torch.nn.init.normal_(m.weight.data, 1.0, 0.02)
            torch.nn.init.constant_(m.bias.data, 0.0)
    net.apply(init_func)

def get_lr_scheduler(lr_decay_type, lr, min_lr, total_iters, warmup_iters_ratio=0.05, warmup_lr_ratio=0.1, no_aug_iter_ratio=0.05, step_num=10):
    """学习率调度器"""
    def yolox_warm_cos_lr(lr, min_lr, total_iters, warmup_total_iters, warmup_lr_start, no_aug_iter, iters):
        if iters <= warmup_total_iters:
            lr = (lr - warmup_lr_start) * pow(iters / float(warmup_total_iters), 2) + warmup_lr_start
        elif iters >= total_iters - no_aug_iter:
            lr = min_lr
        else:
            lr = min_lr + 0.5 * (lr - min_lr) * (1.0 + math.cos(math.pi* (iters - warmup_total_iters) / (total_iters - warmup_total_iters - no_aug_iter)))
        return lr

    def step_lr(lr, decay_rate, step_size, iters):
        if step_size < 1:
            raise ValueError("step_size must above 1.")
        n = iters // step_size
        out_lr = lr * decay_rate ** n
        return out_lr

    if lr_decay_type == "cos":
        warmup_total_iters = min(max(warmup_iters_ratio * total_iters, 1), 3)
        warmup_lr_start = max(warmup_lr_ratio * lr, 1e-6)
        no_aug_iter = min(max(no_aug_iter_ratio * total_iters, 1), 15)
        func = partial(yolox_warm_cos_lr ,lr, min_lr, total_iters, warmup_total_iters, warmup_lr_start, no_aug_iter)
    else:
        decay_rate = (min_lr / lr) ** (1 / (step_num - 1))
        step_size = total_iters / step_num
        func = partial(step_lr, lr, decay_rate, step_size)

    return func

def set_optimizer_lr(optimizer, lr_scheduler_func, epoch):
    """设置优化器学习率"""
    lr = lr_scheduler_func(epoch)
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr

if __name__ == "__main__":
    import datetime
    import math
    from functools import partial
    import torch.nn as nn
    main()
