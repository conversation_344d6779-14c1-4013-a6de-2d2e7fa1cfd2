#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
困难类别特殊处理策略
专门处理完全未学习的类别 8,10,18,23,26,27,28
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from collections import defaultdict
import logging

class DifficultClassHandler:
    """困难类别处理器"""
    
    def __init__(self, difficult_classes=[8, 10, 18, 23, 26, 27, 28], num_classes=29):
        self.difficult_classes = difficult_classes
        self.num_classes = num_classes
        self.logger = logging.getLogger(__name__)
        
        # 困难类别学习历史
        self.learning_history = defaultdict(list)
        self.zero_iou_epochs = defaultdict(int)
        
    def create_extreme_weights(self, base_weight=1.0):
        """创建极端类别权重"""
        weights = torch.ones(self.num_classes) * base_weight
        
        # 困难类别权重映射
        difficult_weights = {
            8: 100.0,   # 最高权重
            10: 90.0,
            18: 80.0,
            23: 70.0,
            26: 60.0,
            27: 50.0,
            28: 40.0
        }
        
        for class_id, weight in difficult_weights.items():
            weights[class_id] = weight
            
        # 优秀表现类别降低权重
        excellent_classes = [0, 3, 13, 2, 21]  # 背景、天空、道路等
        for class_id in excellent_classes:
            weights[class_id] = 0.01
            
        return weights
        
    def create_difficult_class_focal_loss(self, gamma=8.0, alpha=None):
        """创建困难类别专用Focal损失"""
        if alpha is None:
            alpha = self.create_extreme_weights()
            
        class DifficultClassFocalLoss(nn.Module):
            def __init__(self, alpha, gamma, difficult_classes):
                super().__init__()
                self.alpha = alpha
                self.gamma = gamma
                self.difficult_classes = difficult_classes
                
            def forward(self, inputs, targets):
                ce_loss = F.cross_entropy(inputs, targets, reduction='none')
                pt = torch.exp(-ce_loss)
                
                # 为困难类别应用更高的gamma
                gamma_tensor = torch.ones_like(targets, dtype=torch.float) * self.gamma
                for class_id in self.difficult_classes:
                    mask = (targets == class_id)
                    gamma_tensor[mask] = self.gamma * 2.0  # 困难类别双倍gamma
                    
                focal_loss = self.alpha[targets] * (1 - pt) ** gamma_tensor * ce_loss
                return focal_loss.mean()
                
        return DifficultClassFocalLoss(alpha, gamma, self.difficult_classes)
        
    def create_class_aware_sampler(self, dataset, oversample_ratio=15.0):
        """创建类别感知采样器"""
        # 分析数据集中的类别分布
        class_counts = self.analyze_class_distribution(dataset)
        
        # 计算采样权重
        sample_weights = []
        for idx in range(len(dataset)):
            weight = 1.0
            
            # 获取样本的类别信息（需要根据实际数据集实现）
            sample_classes = self.get_sample_classes(dataset, idx)
            
            # 如果包含困难类别，大幅增加权重
            for class_id in sample_classes:
                if class_id in self.difficult_classes:
                    weight *= oversample_ratio
                    break
                    
            sample_weights.append(weight)
            
        return torch.utils.data.WeightedRandomSampler(
            weights=sample_weights,
            num_samples=len(sample_weights),
            replacement=True
        )
        
    def analyze_class_distribution(self, dataset):
        """分析类别分布"""
        class_counts = defaultdict(int)
        
        # 这里需要根据实际数据集实现
        # 暂时返回模拟数据
        for class_id in range(self.num_classes):
            if class_id in self.difficult_classes:
                class_counts[class_id] = 10  # 困难类别样本很少
            else:
                class_counts[class_id] = 1000  # 其他类别样本较多
                
        return class_counts
        
    def get_sample_classes(self, dataset, idx):
        """获取样本包含的类别"""
        # 这里需要根据实际数据集实现
        # 暂时返回随机类别
        return [np.random.choice(self.num_classes)]
        
    def create_copy_paste_augmentation(self):
        """创建复制粘贴增强策略"""
        class CopyPasteAugmentation:
            def __init__(self, difficult_classes, paste_prob=0.8):
                self.difficult_classes = difficult_classes
                self.paste_prob = paste_prob
                
            def __call__(self, image, mask):
                if np.random.random() > self.paste_prob:
                    return image, mask
                    
                # 查找困难类别的像素
                difficult_pixels = torch.zeros_like(mask, dtype=torch.bool)
                for class_id in self.difficult_classes:
                    difficult_pixels |= (mask == class_id)
                    
                if difficult_pixels.sum() > 0:
                    # 复制困难类别像素到随机位置
                    # 这里实现具体的复制粘贴逻辑
                    pass
                    
                return image, mask
                
        return CopyPasteAugmentation(self.difficult_classes)
        
    def create_mixup_for_difficult_classes(self, alpha=0.6):
        """为困难类别创建Mixup增强"""
        class DifficultClassMixup:
            def __init__(self, difficult_classes, alpha):
                self.difficult_classes = difficult_classes
                self.alpha = alpha
                
            def __call__(self, batch_images, batch_masks):
                batch_size = batch_images.size(0)
                
                # 找到包含困难类别的样本
                difficult_indices = []
                for i in range(batch_size):
                    mask = batch_masks[i]
                    for class_id in self.difficult_classes:
                        if (mask == class_id).any():
                            difficult_indices.append(i)
                            break
                            
                if len(difficult_indices) < 2:
                    return batch_images, batch_masks
                    
                # 对困难样本进行Mixup
                for i in range(0, len(difficult_indices) - 1, 2):
                    idx1, idx2 = difficult_indices[i], difficult_indices[i + 1]
                    
                    lam = np.random.beta(self.alpha, self.alpha)
                    
                    # 混合图像
                    batch_images[idx1] = lam * batch_images[idx1] + (1 - lam) * batch_images[idx2]
                    
                    # 混合标签（对于分割任务需要特殊处理）
                    # 这里可以实现更复杂的标签混合策略
                    
                return batch_images, batch_masks
                
        return DifficultClassMixup(self.difficult_classes, alpha)
        
    def monitor_learning_progress(self, epoch, class_ious):
        """监控学习进度"""
        self.logger.info(f"\n=== 第 {epoch} 轮困难类别学习监控 ===")
        
        improvements = []
        zero_iou_classes = []
        
        for class_id in self.difficult_classes:
            if class_id < len(class_ious):
                current_iou = class_ious[class_id]
                self.learning_history[class_id].append(current_iou)
                
                # 检查改善情况
                if len(self.learning_history[class_id]) > 1:
                    prev_iou = self.learning_history[class_id][-2]
                    improvement = current_iou - prev_iou
                    improvements.append(improvement)
                    
                    status_emoji = "📈" if improvement > 0 else "📉" if improvement < 0 else "➡️"
                    self.logger.info(
                        f"  类别 {class_id}: IoU={current_iou:.4f} {status_emoji} "
                        f"(变化: {improvement:+.4f})"
                    )
                else:
                    self.logger.info(f"  类别 {class_id}: IoU={current_iou:.4f}")
                    
                # 零IoU监控
                if current_iou == 0.0:
                    self.zero_iou_epochs[class_id] += 1
                    zero_iou_classes.append(class_id)
                    self.logger.warning(
                        f"⚠️  类别 {class_id} 连续 {self.zero_iou_epochs[class_id]} 轮零IoU！"
                    )
                else:
                    if self.zero_iou_epochs[class_id] > 0:
                        self.logger.info(f"🎉 类别 {class_id} 开始学习！IoU={current_iou:.4f}")
                    self.zero_iou_epochs[class_id] = 0
                    
        # 总体评估
        if zero_iou_classes:
            self.logger.warning(f"仍有 {len(zero_iou_classes)} 个类别完全未学习: {zero_iou_classes}")
        else:
            self.logger.info("🎉 所有困难类别都开始学习了！")
            
        if improvements:
            avg_improvement = np.mean(improvements)
            if avg_improvement > 0:
                self.logger.info(f"📊 困难类别平均改善: +{avg_improvement:.4f}")
            else:
                self.logger.warning(f"📊 困难类别平均退化: {avg_improvement:.4f}")
                
    def suggest_dynamic_adjustments(self, epoch, class_ious):
        """建议动态调整策略"""
        suggestions = []
        
        # 检查长期零IoU的类别
        persistent_zero_classes = []
        for class_id in self.difficult_classes:
            if self.zero_iou_epochs[class_id] > 20:  # 连续20轮零IoU
                persistent_zero_classes.append(class_id)
                
        if persistent_zero_classes:
            suggestions.append({
                'type': 'increase_weights',
                'classes': persistent_zero_classes,
                'factor': 2.0,
                'reason': '长期零IoU，建议增加权重'
            })
            
        # 检查学习停滞的类别
        stagnant_classes = []
        for class_id in self.difficult_classes:
            if len(self.learning_history[class_id]) >= 10:
                recent_ious = self.learning_history[class_id][-10:]
                if max(recent_ious) - min(recent_ious) < 0.001:  # 变化很小
                    stagnant_classes.append(class_id)
                    
        if stagnant_classes:
            suggestions.append({
                'type': 'increase_augmentation',
                'classes': stagnant_classes,
                'reason': '学习停滞，建议增加数据增强'
            })
            
        # 检查过度学习的类别
        overlearning_classes = []
        for class_id in self.difficult_classes:
            if class_id < len(class_ious) and class_ious[class_id] > 0.5:
                overlearning_classes.append(class_id)
                
        if overlearning_classes:
            suggestions.append({
                'type': 'reduce_weights',
                'classes': overlearning_classes,
                'factor': 0.5,
                'reason': '学习良好，可以降低权重'
            })
            
        return suggestions
        
    def apply_dynamic_adjustments(self, model, suggestions):
        """应用动态调整"""
        for suggestion in suggestions:
            self.logger.info(f"应用调整: {suggestion['reason']}")
            
            if suggestion['type'] == 'increase_weights':
                # 增加类别权重的逻辑
                pass
            elif suggestion['type'] == 'reduce_weights':
                # 减少类别权重的逻辑
                pass
            elif suggestion['type'] == 'increase_augmentation':
                # 增加数据增强的逻辑
                pass
                
    def create_progressive_unfreezing_schedule(self):
        """创建渐进式解冻计划"""
        return {
            'stage_1': {
                'epochs': [1, 10],
                'freeze': ['backbone'],
                'focus': 'classifier',
                'description': '冻结骨干网络，专注训练分类头'
            },
            'stage_2': {
                'epochs': [11, 30],
                'freeze': ['backbone.layer1', 'backbone.layer2'],
                'focus': 'decoder',
                'description': '部分解冻骨干网络，训练解码器'
            },
            'stage_3': {
                'epochs': [31, 60],
                'freeze': [],
                'focus': 'all',
                'description': '全网络微调'
            },
            'stage_4': {
                'epochs': [61, -1],
                'freeze': [],
                'focus': 'difficult_classes',
                'description': '专注困难类别优化'
            }
        }
        
    def get_current_stage(self, epoch):
        """获取当前训练阶段"""
        schedule = self.create_progressive_unfreezing_schedule()
        
        for stage_name, stage_info in schedule.items():
            start_epoch, end_epoch = stage_info['epochs']
            if end_epoch == -1 or start_epoch <= epoch <= end_epoch:
                return stage_name, stage_info
                
        return 'stage_4', schedule['stage_4']
        
    def apply_progressive_unfreezing(self, model, epoch):
        """应用渐进式解冻"""
        stage_name, stage_info = self.get_current_stage(epoch)
        
        # 首先解冻所有参数
        for param in model.parameters():
            param.requires_grad = True
            
        # 然后冻结指定的模块
        for module_name in stage_info['freeze']:
            if hasattr(model, module_name):
                module = getattr(model, module_name)
                for param in module.parameters():
                    param.requires_grad = False
                    
        self.logger.info(f"第 {epoch} 轮 - {stage_info['description']}")
        
        return stage_info['focus']
