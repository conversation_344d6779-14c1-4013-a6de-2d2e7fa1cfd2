"""
本地预训练权重工具
用于从本地加载预训练权重
"""
import os
import torch
import logging
import shutil
from pathlib import Path

logger = logging.getLogger(__name__)

def find_local_weights(search_dirs=None, backbone="resnet50"):
    """
    在本地搜索预训练权重文件
    
    Args:
        search_dirs: 搜索目录列表，如果为None，则使用默认目录
        backbone: 骨干网络名称
        
    Returns:
        找到的权重文件路径，如果未找到则返回None
    """
    if search_dirs is None:
        # 默认搜索目录
        search_dirs = [
            "pretrained_weights",
            "weights",
            "checkpoints",
            "models",
            os.path.expanduser("~/.torch/models"),
            os.path.expanduser("~/.cache/torch/hub/checkpoints"),
        ]
    
    # 可能的文件名模式
    file_patterns = [
        f"{backbone}.pth",
        f"{backbone}_pretrained.pth",
        f"{backbone}_imagenet.pth",
        f"{backbone}-*.pth",
        f"*{backbone}*.pth",
    ]
    
    # 搜索文件
    for directory in search_dirs:
        if not os.path.exists(directory):
            continue
        
        logger.info(f"搜索目录: {directory}")
        
        # 递归搜索
        for root, _, files in os.walk(directory):
            for file in files:
                # 检查文件是否匹配模式
                if any(Path(file).match(pattern) for pattern in file_patterns):
                    file_path = os.path.join(root, file)
                    logger.info(f"找到预训练权重: {file_path}")
                    return file_path
    
    logger.warning(f"未找到{backbone}的预训练权重")
    return None

def copy_to_pretrained_dir(source_path, dataset="ADE20K", backbone="resnet50"):
    """
    将本地权重文件复制到预训练权重目录
    
    Args:
        source_path: 源文件路径
        dataset: 数据集名称
        backbone: 骨干网络名称
        
    Returns:
        目标文件路径
    """
    if not os.path.exists(source_path):
        logger.error(f"源文件不存在: {source_path}")
        return None
    
    # 目标目录
    target_dir = os.path.join("pretrained_weights", dataset)
    os.makedirs(target_dir, exist_ok=True)
    
    # 目标文件路径
    target_path = os.path.join(target_dir, f"{backbone}.pth")
    
    # 复制文件
    try:
        shutil.copy2(source_path, target_path)
        logger.info(f"已将权重文件复制到: {target_path}")
        return target_path
    except Exception as e:
        logger.error(f"复制文件失败: {e}")
        return None

def load_local_imagenet_weights(backbone="resnet50"):
    """
    加载本地ImageNet预训练权重
    
    Args:
        backbone: 骨干网络名称
        
    Returns:
        权重文件路径
    """
    # 尝试从torchvision加载预训练权重
    try:
        import torchvision.models as models
        
        logger.info(f"尝试从torchvision加载{backbone}预训练权重")
        
        # 获取预训练模型
        if backbone == "resnet50":
            model = models.resnet50(pretrained=True)
        elif backbone == "resnet101":
            model = models.resnet101(pretrained=True)
        elif backbone == "resnet34":
            model = models.resnet34(pretrained=True)
        elif backbone == "resnet18":
            model = models.resnet18(pretrained=True)
        else:
            logger.warning(f"不支持的骨干网络: {backbone}")
            return None
        
        # 保存权重
        target_dir = os.path.join("pretrained_weights", "ImageNet")
        os.makedirs(target_dir, exist_ok=True)
        target_path = os.path.join(target_dir, f"{backbone}.pth")
        
        torch.save(model.state_dict(), target_path)
        logger.info(f"已保存ImageNet预训练权重: {target_path}")
        
        return target_path
    
    except Exception as e:
        logger.error(f"从torchvision加载预训练权重失败: {e}")
        return None
