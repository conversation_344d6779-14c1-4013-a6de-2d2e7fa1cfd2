@echo off
chcp 65001 > nul
echo ===== Optimized Training Script =====
echo For i7-14700KF + RTX 4070 Ti Super 16GB configuration

REM Set PyTorch environment variables
echo Setting PyTorch environment variables...
set PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
set CUDA_LAUNCH_BLOCKING=0
set CUDA_DEVICE_ORDER=PCI_BUS_ID
set CUDA_VISIBLE_DEVICES=0

REM Set NumPy environment variables
set OMP_NUM_THREADS=8
set MKL_NUM_THREADS=8

REM Clean CUDA cache
echo Cleaning CUDA cache...
python -c "import torch; torch.cuda.empty_cache()"

REM Start GPU monitoring
echo Starting GPU memory monitoring...
start cmd /k "python gpu_memory_monitor.py --optimize --interval 2"

REM Start TensorBoard
echo Starting TensorBoard...
start cmd /k "tensorboard --logdir=logs/current"

REM Start training
echo Starting optimized training...
python train.py

echo Training completed!
pause
