"""
GPU内存监控脚本
专门监控训练过程中的GPU内存使用情况，并提供优化建议
"""
import os
import time
import argparse
import subprocess
import platform
import datetime
import threading
import signal
import sys
import json
from tabulate import tabulate

# 检查是否安装了必要的包
try:
    import psutil
    import GPUtil
    import numpy as np
    import matplotlib.pyplot as plt
    from matplotlib.animation import FuncAnimation
except ImportError:
    print("请安装必要的包: pip install psutil GPUtil numpy matplotlib tabulate")
    sys.exit(1)

# 全局变量
running = True
data = {
    'time': [],
    'gpu_util': [],
    'gpu_mem': [],
    'gpu_mem_free': [],
    'gpu_temp': [],
    'cpu_util': [],
    'ram_util': []
}
lock = threading.Lock()

# 批次大小建议
batch_size_suggestions = {
    'freeze': None,
    'unfreeze': None
}

def parse_args():
    parser = argparse.ArgumentParser(description="监控GPU内存使用情况并提供优化建议")
    parser.add_argument('--interval', type=float, default=1.0, help='采样间隔（秒）')
    parser.add_argument('--log', action='store_true', help='是否记录到文件')
    parser.add_argument('--plot', action='store_true', help='是否实时绘图')
    parser.add_argument('--duration', type=int, default=0, help='监控持续时间（秒），0表示一直运行')
    parser.add_argument('--optimize', action='store_true', help='是否提供优化建议')
    return parser.parse_args()

def get_gpu_info():
    """获取GPU信息"""
    try:
        gpus = GPUtil.getGPUs()
        if not gpus:
            return None, None, None, None
        
        # 只获取第一个GPU的信息
        gpu = gpus[0]
        return gpu.load * 100, gpu.memoryUsed, gpu.memoryFree, gpu.temperature
    except Exception as e:
        print(f"获取GPU信息出错: {e}")
        return None, None, None, None

def get_system_info():
    """获取系统信息"""
    try:
        cpu_percent = psutil.cpu_percent()
        ram_percent = psutil.virtual_memory().percent
        return cpu_percent, ram_percent
    except Exception as e:
        print(f"获取系统信息出错: {e}")
        return None, None

def get_pytorch_processes():
    """获取PyTorch进程信息"""
    try:
        pytorch_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'python' in proc.info['name'].lower() and ('train.py' in cmdline or 'torch' in cmdline):
                    mem_info = proc.memory_info()
                    pytorch_processes.append({
                        'pid': proc.pid,
                        'name': proc.info['name'],
                        'cmdline': cmdline[:50] + '...' if len(cmdline) > 50 else cmdline,
                        'memory_mb': mem_info.rss / (1024 * 1024)
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        return pytorch_processes
    except Exception as e:
        print(f"获取PyTorch进程信息出错: {e}")
        return []

def suggest_batch_size(gpu_mem_used, gpu_mem_total):
    """根据GPU内存使用情况建议批次大小"""
    global batch_size_suggestions
    
    # 读取当前配置
    try:
        with open('config.yaml', 'r') as f:
            config_content = f.read()
            
        # 提取当前批次大小
        import re
        freeze_match = re.search(r'freeze_batch_size:\s*(\d+)', config_content)
        unfreeze_match = re.search(r'unfreeze_batch_size:\s*(\d+)', config_content)
        
        current_freeze_bs = int(freeze_match.group(1)) if freeze_match else 12
        current_unfreeze_bs = int(unfreeze_match.group(1)) if unfreeze_match else 6
        
        # 计算内存使用率
        mem_usage_percent = (gpu_mem_used / gpu_mem_total) * 100
        
        # 根据内存使用率调整批次大小建议
        if mem_usage_percent > 90:  # 内存使用率过高
            suggested_freeze_bs = max(4, current_freeze_bs - 4)
            suggested_unfreeze_bs = max(2, current_unfreeze_bs - 2)
        elif mem_usage_percent > 80:  # 内存使用率较高
            suggested_freeze_bs = max(6, current_freeze_bs - 2)
            suggested_unfreeze_bs = max(3, current_unfreeze_bs - 1)
        elif mem_usage_percent < 50:  # 内存使用率较低
            suggested_freeze_bs = min(24, current_freeze_bs + 4)
            suggested_unfreeze_bs = min(12, current_unfreeze_bs + 2)
        elif mem_usage_percent < 70:  # 内存使用率适中
            suggested_freeze_bs = min(20, current_freeze_bs + 2)
            suggested_unfreeze_bs = min(10, current_unfreeze_bs + 1)
        else:  # 内存使用率正常
            suggested_freeze_bs = current_freeze_bs
            suggested_unfreeze_bs = current_unfreeze_bs
        
        # 更新建议
        batch_size_suggestions['freeze'] = suggested_freeze_bs
        batch_size_suggestions['unfreeze'] = suggested_unfreeze_bs
        
        return batch_size_suggestions
    except Exception as e:
        print(f"计算批次大小建议出错: {e}")
        return {'freeze': current_freeze_bs, 'unfreeze': current_unfreeze_bs}

def collect_data():
    """收集数据"""
    global data, running, batch_size_suggestions
    
    args = parse_args()
    interval = args.interval
    log_file = None
    
    if args.log:
        log_dir = "logs/gpu_monitor"
        os.makedirs(log_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = open(f"{log_dir}/gpu_memory_log_{timestamp}.csv", "w")
        log_file.write("时间,GPU利用率(%),GPU内存使用(MB),GPU内存空闲(MB),GPU温度(°C),CPU利用率(%),内存利用率(%)\n")
    
    print("开始监控GPU内存使用情况...")
    print("按Ctrl+C停止监控")
    
    start_time = time.time()
    last_suggestion_time = 0
    
    try:
        while running:
            current_time = time.time() - start_time
            gpu_util, gpu_mem, gpu_mem_free, gpu_temp = get_gpu_info()
            cpu_util, ram_util = get_system_info()
            
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            
            with lock:
                data['time'].append(current_time)
                data['gpu_util'].append(gpu_util if gpu_util is not None else 0)
                data['gpu_mem'].append(gpu_mem if gpu_mem is not None else 0)
                data['gpu_mem_free'].append(gpu_mem_free if gpu_mem_free is not None else 0)
                data['gpu_temp'].append(gpu_temp if gpu_temp is not None else 0)
                data['cpu_util'].append(cpu_util if cpu_util is not None else 0)
                data['ram_util'].append(ram_util if ram_util is not None else 0)
            
            # 获取PyTorch进程信息
            pytorch_processes = get_pytorch_processes()
            
            # 打印当前状态
            table = [
                ["GPU利用率", f"{gpu_util:.1f}%" if gpu_util is not None else "N/A"],
                ["GPU内存使用", f"{gpu_mem:.0f} MB" if gpu_mem is not None else "N/A"],
                ["GPU内存空闲", f"{gpu_mem_free:.0f} MB" if gpu_mem_free is not None else "N/A"],
                ["GPU温度", f"{gpu_temp:.1f}°C" if gpu_temp is not None else "N/A"],
                ["CPU利用率", f"{cpu_util:.1f}%" if cpu_util is not None else "N/A"],
                ["内存利用率", f"{ram_util:.1f}%" if ram_util is not None else "N/A"]
            ]
            
            os.system('cls' if platform.system() == 'Windows' else 'clear')
            print(f"GPU内存监控 - {timestamp} (运行时间: {int(current_time)}秒)")
            print(tabulate(table, headers=["指标", "值"], tablefmt="grid"))
            
            # 打印PyTorch进程信息
            if pytorch_processes:
                print("\nPyTorch进程:")
                proc_table = [[p['pid'], p['name'], p['cmdline'], f"{p['memory_mb']:.1f} MB"] for p in pytorch_processes]
                print(tabulate(proc_table, headers=["PID", "名称", "命令行", "内存使用"], tablefmt="grid"))
            
            # 提供优化建议
            if args.optimize and gpu_mem is not None and gpu_mem_free is not None:
                # 每30秒更新一次建议
                if current_time - last_suggestion_time >= 30:
                    total_mem = gpu_mem + gpu_mem_free
                    suggestions = suggest_batch_size(gpu_mem, total_mem)
                    last_suggestion_time = current_time
                
                if batch_size_suggestions['freeze'] is not None:
                    print("\n批次大小建议:")
                    bs_table = [
                        ["冻结阶段批次大小", batch_size_suggestions['freeze']],
                        ["解冻阶段批次大小", batch_size_suggestions['unfreeze']]
                    ]
                    print(tabulate(bs_table, headers=["参数", "建议值"], tablefmt="grid"))
                    
                    # 内存使用率
                    mem_usage_percent = (gpu_mem / (gpu_mem + gpu_mem_free)) * 100
                    status = "过高" if mem_usage_percent > 85 else "适中" if mem_usage_percent > 70 else "较低"
                    print(f"\n内存使用率: {mem_usage_percent:.1f}% ({status})")
                    
                    # 提供优化建议
                    if mem_usage_percent > 85:
                        print("建议: 降低批次大小或减少模型复杂度")
                    elif mem_usage_percent < 60:
                        print("建议: 可以增加批次大小以提高训练效率")
            
            # 记录到文件
            if log_file:
                log_file.write(f"{timestamp},{gpu_util:.1f},{gpu_mem:.0f},{gpu_mem_free:.0f},{gpu_temp:.1f},{cpu_util:.1f},{ram_util:.1f}\n")
                log_file.flush()
            
            # 检查是否达到持续时间
            if args.duration > 0 and current_time >= args.duration:
                running = False
                break
            
            time.sleep(interval)
    
    except KeyboardInterrupt:
        print("\n停止监控")
    finally:
        if log_file:
            log_file.close()
            print(f"日志已保存到: {log_file.name}")

def update_plot(frame):
    """更新绘图"""
    global data
    
    with lock:
        time_data = data['time']
        gpu_util_data = data['gpu_util']
        gpu_mem_data = data['gpu_mem']
        gpu_mem_free_data = data['gpu_mem_free']
        gpu_temp_data = data['gpu_temp']
        cpu_util_data = data['cpu_util']
        ram_util_data = data['ram_util']
    
    # 清除当前图形
    plt.clf()
    
    # 创建子图
    ax1 = plt.subplot(3, 1, 1)
    ax2 = plt.subplot(3, 1, 2)
    ax3 = plt.subplot(3, 1, 3)
    
    # 绘制GPU利用率
    ax1.plot(time_data, gpu_util_data, 'r-', label='GPU利用率 (%)')
    ax1.plot(time_data, cpu_util_data, 'b-', label='CPU利用率 (%)')
    ax1.set_ylabel('利用率 (%)')
    ax1.set_title('GPU和CPU利用率')
    ax1.grid(True)
    ax1.legend()
    
    # 绘制GPU内存
    ax2.plot(time_data, gpu_mem_data, 'g-', label='GPU内存使用 (MB)')
    ax2.plot(time_data, gpu_mem_free_data, 'y-', label='GPU内存空闲 (MB)')
    ax2.set_ylabel('内存 (MB)')
    ax2.set_title('GPU内存使用情况')
    ax2.grid(True)
    ax2.legend()
    
    # 绘制GPU温度
    ax3.plot(time_data, gpu_temp_data, 'm-', label='GPU温度 (°C)')
    ax3.set_xlabel('时间 (秒)')
    ax3.set_ylabel('温度 (°C)')
    ax3.set_title('GPU温度')
    ax3.grid(True)
    ax3.legend()
    
    plt.tight_layout()
    
    return ax1, ax2, ax3

def plot_data():
    """绘制数据"""
    plt.figure(figsize=(10, 8))
    ani = FuncAnimation(plt.gcf(), update_plot, interval=1000)
    plt.tight_layout()
    plt.show()

def signal_handler(sig, frame):
    """处理信号"""
    global running
    running = False
    print("\n停止监控")
    sys.exit(0)

def main():
    args = parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    # 创建数据收集线程
    data_thread = threading.Thread(target=collect_data)
    data_thread.daemon = True
    data_thread.start()
    
    # 如果需要绘图，启动绘图线程
    if args.plot:
        plot_data()
    else:
        # 等待数据收集线程结束
        data_thread.join()
    
    # 保存最终图表
    if len(data['time']) > 0:
        plt.figure(figsize=(12, 9))
        update_plot(0)
        
        # 创建保存目录
        save_dir = "logs/gpu_monitor"
        os.makedirs(save_dir, exist_ok=True)
        
        # 保存图表
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        plt.savefig(f"{save_dir}/gpu_memory_plot_{timestamp}.png")
        print(f"图表已保存到: {save_dir}/gpu_memory_plot_{timestamp}.png")
        
        # 保存优化建议
        if args.optimize and batch_size_suggestions['freeze'] is not None:
            with open(f"{save_dir}/batch_size_suggestions_{timestamp}.json", "w") as f:
                json.dump(batch_size_suggestions, f, indent=4)
            print(f"批次大小建议已保存到: {save_dir}/batch_size_suggestions_{timestamp}.json")

if __name__ == "__main__":
    main()
