# 多预训练数据集权重混合配置文件
# 用于提升对目标数据集的标签匹配度和迁移效果

# 基础模型配置
model:
  backbone: "resnet50"  # 骨干网络: resnet50, resnet101, efficientnet-b4
  pretrained: false     # 不使用单一预训练权重，而是使用混合权重
  dropout_rate: 0.2
  use_attention: true
  attention_type: "cbam"

# 数据配置
data:
  num_classes: 29
  input_shape: [512, 512]
  dataset_path: "VOCdevkit/VOC2025"

  # 目标数据集的类别名称（用于计算标签相似度）
  class_names: [
    "background", "building", "road", "vegetation", "vehicle", "water",
    "sky", "person", "ground", "wall", "fence", "bridge", "sign",
    "pole", "sidewalk", "terrain", "tree", "car", "truck", "bus",
    "motorcycle", "bicycle", "traffic_light", "traffic_sign", "rail",
    "guardrail", "tunnel", "polegroup", "caravan"
  ]

# 多预训练数据集混合配置
multi_pretrain:
  enabled: true

  # 要混合的预训练数据集列表
  datasets:
    - "ADE20K"      # 场景理解数据集，150类
    - "Cityscapes"  # 城市街景数据集，19类
    - "COCO-Stuff"  # COCO语义分割，171类
    - "Pascal-VOC"  # Pascal VOC，21类

  # 权重混合策略
  mixing_strategy: "similarity_weighted"  # 可选: similarity_weighted, equal_weighted, entropy_weighted, attention_weighted

  # 权重目录和保存路径
  weights_dir: "pretrained_weights"
  save_mixed_weights: true
  mixed_weights_path: "pretrained_weights/mixed_similarity_weighted_resnet50.pth"

  # 高级配置
  advanced:
    # 相似度计算权重
    similarity_weights:
      exact_match: 1.0      # 完全匹配的权重
      partial_match: 0.8    # 部分匹配的权重
      semantic_match: 0.6   # 语义匹配的权重

    # 混合策略参数
    mixing_params:
      # 相似度加权参数
      similarity_weighted:
        min_weight: 0.1     # 最小权重，避免某个数据集权重为0
        normalize: true     # 是否归一化权重

      # 熵加权参数
      entropy_weighted:
        temperature: 1.0    # 温度参数，控制权重分布的平滑度

      # 注意力加权参数
      attention_weighted:
        variance_weight: 0.3    # 方差权重
        mean_weight: 0.3        # 均值权重
        similarity_weight: 0.4  # 相似度权重

# 训练配置
train:
  total_epochs: 100
  batch_size: 8

  # 分层学习率（针对混合权重的微调）
  encoder_lr: 1e-5      # 编码器学习率（较小，因为已经是混合的预训练权重）
  decoder_lr: 5e-5      # 解码器学习率
  head_lr: 1e-4         # 分割头学习率（较大，需要适应新的类别）

  # 渐进式解冻策略
  unfreeze_schedule:
    0: ["segmentation_head"]     # 第0轮开始只训练分割头
    10: ["decoder"]              # 第10轮开始解冻解码器
    30: ["encoder.layer4"]       # 第30轮开始解冻编码器最后一层
    50: ["encoder.layer3"]       # 第50轮开始解冻更多层
    70: ["encoder.layer2"]       # 依此类推
    90: ["encoder.layer1"]

  # 优化器配置
  optimizer: "AdamW"
  weight_decay: 0.01

  # 学习率调度器
  scheduler:
    type: "cosine_warmup"
    warmup_epochs: 10
    cycles: 3
    min_lr: 1e-7

# 损失函数配置
loss:
  # 主损失函数
  main_loss: "focal_dice"

  # 类别权重计算方法
  class_weights:
    method: "effective_samples"  # 有效样本数平衡，适合混合预训练
    smoothing: 1e-6

  # 损失权重
  weights:
    focal_weight: 0.7
    dice_weight: 0.3

# 数据增强配置
augmentation:
  enabled: true

  # 基础增强
  basic:
    horizontal_flip: 0.5
    vertical_flip: 0.2
    rotation: 15
    brightness: 0.2
    contrast: 0.2

  # 高级增强（适合多预训练数据集）
  advanced:
    mixup:
      enabled: true
      alpha: 0.2

    cutmix:
      enabled: true
      alpha: 1.0

    # 域适应增强
    domain_adaptation:
      enabled: true
      style_transfer_prob: 0.1  # 风格迁移概率

# 验证和测试配置
validation:
  val_split: 0.2
  metrics: ["mIoU", "pixel_accuracy", "mean_accuracy"]

  # 早停配置
  early_stopping:
    patience: 20
    min_delta: 0.001
    monitor: "val_mIoU"

# 日志和保存配置
logging:
  log_dir: "logs"
  save_best_only: true
  save_frequency: 10

  # TensorBoard配置
  tensorboard:
    enabled: true
    log_images: true
    log_weights_histogram: true

# 推理配置
inference:
  # 测试时增强
  tta:
    enabled: true
    transforms: ["horizontal_flip", "vertical_flip", "multi_scale"]

  # 后处理
  post_processing:
    crf:
      enabled: false  # 条件随机场后处理

    morphology:
      enabled: true
      kernel_size: 3

# 实验配置
experiment:
  name: "multi_pretrain_mixing"
  description: "使用多预训练数据集权重混合提升标签匹配度"
  tags: ["multi_pretrain", "weight_mixing", "transfer_learning"]

  # 对比实验
  ablation_studies:
    - name: "single_pretrain_ade20k"
      config_override:
        multi_pretrain:
          enabled: false
        model:
          pretrained_weights: "pretrained_weights/ADE20K/resnet50.pth"

    - name: "equal_weighted_mixing"
      config_override:
        multi_pretrain:
          mixing_strategy: "equal_weighted"

    - name: "entropy_weighted_mixing"
      config_override:
        multi_pretrain:
          mixing_strategy: "entropy_weighted"
