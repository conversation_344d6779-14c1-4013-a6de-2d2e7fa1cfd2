"""
多预训练数据集权重混合模块
用于混合多个预训练数据集的权重，提升对目标数据集的标签匹配度和迁移效果
"""
import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple, Union
from copy import deepcopy
import json
from pathlib import Path

from utils.pretrain_utils import get_pretrained_weights, load_pretrained_weights, PRETRAIN_DATASETS
from utils.ade20k_labels import LABEL_MAPPING, ADE20K_CLASSES, OUR_CLASSES

logger = logging.getLogger(__name__)

class MultiPretrainMixer:
    """多预训练数据集权重混合器"""

    def __init__(self, target_classes: List[str], target_num_classes: int):
        """
        初始化权重混合器

        Args:
            target_classes: 目标数据集的类别名称列表
            target_num_classes: 目标数据集的类别数量
        """
        self.target_classes = target_classes
        self.target_num_classes = target_num_classes
        self.dataset_weights = {}  # 存储各数据集的权重
        self.label_mappings = {}   # 存储各数据集的标签映射
        self.similarity_scores = {}  # 存储相似度分数

        logger.info(f"初始化多预训练权重混合器，目标类别数: {target_num_classes}")

    def compute_label_similarity(self, source_classes: List[str], source_dataset: str) -> float:
        """
        计算源数据集与目标数据集的标签相似度

        Args:
            source_classes: 源数据集的类别名称列表
            source_dataset: 源数据集名称

        Returns:
            相似度分数 (0-1)
        """
        logger.info(f"计算 {source_dataset} 数据集的标签相似度...")

        # 简单的字符串匹配相似度
        matches = 0
        total_target = len(self.target_classes)

        for target_class in self.target_classes:
            target_lower = target_class.lower()
            best_match_score = 0

            for source_class in source_classes:
                source_lower = source_class.lower()

                # 完全匹配
                if target_lower == source_lower:
                    best_match_score = 1.0
                    break

                # 包含关系匹配
                elif target_lower in source_lower or source_lower in target_lower:
                    best_match_score = max(best_match_score, 0.8)

                # 语义相似度匹配（简化版）
                elif self._semantic_similarity(target_lower, source_lower) > 0.5:
                    best_match_score = max(best_match_score, 0.6)

            matches += best_match_score

        similarity = matches / total_target
        logger.info(f"{source_dataset} 标签相似度: {similarity:.3f}")
        return similarity

    def _semantic_similarity(self, class1: str, class2: str) -> float:
        """
        计算两个类别名称的语义相似度（简化版）
        """
        # 定义一些语义相似的词组
        semantic_groups = [
            ['person', 'people', 'human', 'man', 'woman'],
            ['car', 'vehicle', 'automobile', 'truck'],
            ['building', 'house', 'structure'],
            ['tree', 'plant', 'vegetation'],
            ['road', 'street', 'path'],
            ['sky', 'air', 'atmosphere'],
            ['water', 'sea', 'ocean', 'river'],
            ['ground', 'earth', 'soil', 'land'],
            ['wall', 'fence', 'barrier'],
            ['window', 'glass'],
            ['door', 'entrance'],
            ['table', 'desk', 'surface'],
            ['chair', 'seat'],
            ['bed', 'mattress'],
            ['light', 'lamp', 'illumination'],
            ['food', 'meal', 'dish'],
            ['animal', 'creature', 'pet'],
            ['cloth', 'fabric', 'textile'],
            ['metal', 'steel', 'iron'],
            ['wood', 'timber', 'wooden']
        ]

        for group in semantic_groups:
            if class1 in group and class2 in group:
                return 0.8

        # 简单的字符相似度
        common_chars = set(class1) & set(class2)
        total_chars = set(class1) | set(class2)
        if total_chars:
            return len(common_chars) / len(total_chars)

        return 0.0

    def load_multiple_pretrained_weights(self, datasets: List[str], backbone: str,
                                       weights_dir: str = "pretrained_weights") -> Dict[str, torch.Tensor]:
        """
        加载多个预训练数据集的权重

        Args:
            datasets: 数据集名称列表
            backbone: 骨干网络名称
            weights_dir: 权重保存目录

        Returns:
            数据集名称到权重字典的映射
        """
        weights_dict = {}

        for dataset in datasets:
            logger.info(f"加载 {dataset} 预训练权重...")

            # 获取权重文件路径
            weights_path = get_pretrained_weights(dataset, backbone, weights_dir)

            if weights_path and os.path.exists(weights_path):
                try:
                    # 加载权重
                    checkpoint = torch.load(weights_path, map_location='cpu')

                    # 处理不同格式的checkpoint
                    if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
                        state_dict = checkpoint['state_dict']
                    elif isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                        state_dict = checkpoint['model_state_dict']
                    else:
                        state_dict = checkpoint

                    weights_dict[dataset] = state_dict
                    logger.info(f"成功加载 {dataset} 权重")

                    # 计算标签相似度
                    if dataset.upper() in PRETRAIN_DATASETS:
                        dataset_info = PRETRAIN_DATASETS[dataset.upper()]
                        # 这里需要根据实际情况获取类别名称
                        # 暂时使用类别数量作为简化处理
                        num_classes = dataset_info['num_classes']
                        similarity = self._estimate_similarity_by_classes(num_classes)
                        self.similarity_scores[dataset] = similarity

                except Exception as e:
                    logger.error(f"加载 {dataset} 权重失败: {e}")
            else:
                logger.warning(f"未找到 {dataset} 预训练权重")

        return weights_dict

    def _estimate_similarity_by_classes(self, num_classes: int) -> float:
        """
        根据类别数量估算相似度（简化方法）
        """
        # 类别数量越接近，相似度越高
        target_classes = self.target_num_classes
        ratio = min(num_classes, target_classes) / max(num_classes, target_classes)

        # 添加一些启发式规则
        if num_classes == 150:  # ADE20K
            return 0.7  # 场景理解数据集，通常有较好的通用性
        elif num_classes == 19:  # Cityscapes
            return 0.6  # 城市场景，适中的通用性
        elif num_classes == 21:  # Pascal VOC
            return 0.5  # 物体检测数据集
        elif num_classes == 171:  # COCO-Stuff
            return 0.8  # 大规模数据集，通用性好
        else:
            return ratio * 0.5  # 默认基于比例的相似度

    def mix_weights_adaptive(self, weights_dict: Dict[str, torch.Tensor],
                           mixing_strategy: str = "similarity_weighted") -> torch.Tensor:
        """
        自适应混合多个预训练权重

        Args:
            weights_dict: 数据集名称到权重的映射
            mixing_strategy: 混合策略
                - "similarity_weighted": 基于相似度加权
                - "equal_weighted": 等权重混合
                - "entropy_weighted": 基于熵的加权
                - "attention_weighted": 基于注意力的加权

        Returns:
            混合后的权重
        """
        logger.info(f"使用 {mixing_strategy} 策略混合权重...")

        if not weights_dict:
            raise ValueError("没有可用的预训练权重")

        # 获取第一个权重作为参考
        first_weights = list(weights_dict.values())[0]

        if mixing_strategy == "similarity_weighted":
            return self._mix_weights_similarity_weighted(weights_dict)
        elif mixing_strategy == "equal_weighted":
            return self._mix_weights_equal(weights_dict)
        elif mixing_strategy == "entropy_weighted":
            return self._mix_weights_entropy_weighted(weights_dict)
        elif mixing_strategy == "attention_weighted":
            return self._mix_weights_attention_weighted(weights_dict)
        else:
            logger.warning(f"未知的混合策略: {mixing_strategy}，使用相似度加权")
            return self._mix_weights_similarity_weighted(weights_dict)

    def _mix_weights_similarity_weighted(self, weights_dict: Dict[str, torch.Tensor]) -> torch.Tensor:
        """基于相似度的加权混合"""
        mixed_weights = {}

        # 计算权重系数
        total_similarity = sum(self.similarity_scores.get(dataset, 0.1) for dataset in weights_dict.keys())
        weight_coeffs = {
            dataset: self.similarity_scores.get(dataset, 0.1) / total_similarity
            for dataset in weights_dict.keys()
        }

        logger.info(f"权重系数: {weight_coeffs}")

        # 混合每一层的权重
        for dataset, weights in weights_dict.items():
            coeff = weight_coeffs[dataset]

            for key, tensor in weights.items():
                if key not in mixed_weights:
                    mixed_weights[key] = torch.zeros_like(tensor)
                mixed_weights[key] += coeff * tensor

        return mixed_weights

    def _mix_weights_equal(self, weights_dict: Dict[str, torch.Tensor]) -> torch.Tensor:
        """等权重混合"""
        mixed_weights = {}
        num_datasets = len(weights_dict)

        for dataset, weights in weights_dict.items():
            for key, tensor in weights.items():
                if key not in mixed_weights:
                    mixed_weights[key] = torch.zeros_like(tensor)
                mixed_weights[key] += tensor / num_datasets

        return mixed_weights

    def _mix_weights_entropy_weighted(self, weights_dict: Dict[str, torch.Tensor]) -> torch.Tensor:
        """基于熵的加权混合"""
        mixed_weights = {}

        # 计算每个数据集权重的熵
        entropy_scores = {}
        for dataset, weights in weights_dict.items():
            total_entropy = 0
            total_params = 0

            for key, tensor in weights.items():
                if 'weight' in key and tensor.dim() > 1:  # 只考虑权重参数
                    # 计算权重分布的熵
                    flat_weights = tensor.flatten()
                    # 归一化到概率分布
                    probs = F.softmax(flat_weights, dim=0)
                    entropy = -torch.sum(probs * torch.log(probs + 1e-8))
                    total_entropy += entropy.item()
                    total_params += 1

            avg_entropy = total_entropy / max(total_params, 1)
            entropy_scores[dataset] = avg_entropy

        # 熵越高，权重越大（更多样化的特征）
        total_entropy = sum(entropy_scores.values())
        weight_coeffs = {
            dataset: score / total_entropy
            for dataset, score in entropy_scores.items()
        }

        logger.info(f"熵权重系数: {weight_coeffs}")

        # 混合权重
        for dataset, weights in weights_dict.items():
            coeff = weight_coeffs[dataset]

            for key, tensor in weights.items():
                if key not in mixed_weights:
                    mixed_weights[key] = torch.zeros_like(tensor)
                mixed_weights[key] += coeff * tensor

        return mixed_weights

    def _mix_weights_attention_weighted(self, weights_dict: Dict[str, torch.Tensor]) -> torch.Tensor:
        """基于注意力机制的加权混合"""
        mixed_weights = {}

        # 计算每个数据集的注意力权重
        attention_weights = self._compute_attention_weights(weights_dict)

        logger.info(f"注意力权重: {attention_weights}")

        # 混合权重
        for dataset, weights in weights_dict.items():
            coeff = attention_weights[dataset]

            for key, tensor in weights.items():
                if key not in mixed_weights:
                    mixed_weights[key] = torch.zeros_like(tensor)
                mixed_weights[key] += coeff * tensor

        return mixed_weights

    def _compute_attention_weights(self, weights_dict: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """计算注意力权重"""
        # 简化的注意力机制：基于权重的方差和相似度
        attention_scores = {}

        for dataset, weights in weights_dict.items():
            # 计算权重的统计特征
            total_variance = 0
            total_mean = 0
            param_count = 0

            for key, tensor in weights.items():
                if 'weight' in key and tensor.dim() > 1:
                    variance = torch.var(tensor).item()
                    mean = torch.mean(torch.abs(tensor)).item()

                    total_variance += variance
                    total_mean += mean
                    param_count += 1

            avg_variance = total_variance / max(param_count, 1)
            avg_mean = total_mean / max(param_count, 1)

            # 结合方差、均值和相似度计算注意力分数
            similarity = self.similarity_scores.get(dataset, 0.1)
            attention_score = (avg_variance * 0.3 + avg_mean * 0.3 + similarity * 0.4)
            attention_scores[dataset] = attention_score

        # 归一化注意力权重
        total_attention = sum(attention_scores.values())
        attention_weights = {
            dataset: score / total_attention
            for dataset, score in attention_scores.items()
        }

        return attention_weights

    def create_mixed_model(self, model_template, datasets: List[str], backbone: str,
                          mixing_strategy: str = "similarity_weighted",
                          weights_dir: str = "pretrained_weights") -> nn.Module:
        """
        创建混合了多个预训练权重的模型

        Args:
            model_template: 模型模板
            datasets: 要混合的数据集列表
            backbone: 骨干网络名称
            mixing_strategy: 混合策略
            weights_dir: 权重目录

        Returns:
            混合权重后的模型
        """
        logger.info(f"创建混合预训练权重模型，数据集: {datasets}")

        # 加载多个预训练权重
        weights_dict = self.load_multiple_pretrained_weights(datasets, backbone, weights_dir)

        if not weights_dict:
            logger.warning("没有成功加载任何预训练权重，返回原始模型")
            return model_template

        # 混合权重
        mixed_weights = self.mix_weights_adaptive(weights_dict, mixing_strategy)

        # 应用混合权重到模型
        model = deepcopy(model_template)

        # 处理分割头权重（需要调整类别数）
        self._adjust_segmentation_head(mixed_weights, model)

        # 加载混合权重
        missing_keys, unexpected_keys = model.load_state_dict(mixed_weights, strict=False)

        if missing_keys:
            logger.info(f"缺失的键: {len(missing_keys)}")
        if unexpected_keys:
            logger.info(f"意外的键: {len(unexpected_keys)}")

        logger.info("成功创建混合预训练权重模型")
        return model

    def _adjust_segmentation_head(self, mixed_weights: Dict[str, torch.Tensor], model: nn.Module):
        """调整分割头权重以匹配目标类别数"""
        # 查找分割头相关的权重
        seg_head_keys = [k for k in mixed_weights.keys() if 'segmentation_head' in k or 'classifier' in k]

        for key in seg_head_keys:
            if 'weight' in key and mixed_weights[key].dim() > 1:
                original_classes = mixed_weights[key].size(0)

                if original_classes != self.target_num_classes:
                    logger.info(f"调整分割头权重 {key}: {original_classes} -> {self.target_num_classes}")

                    # 如果目标类别数较少，选择前N个类别的权重
                    if self.target_num_classes < original_classes:
                        mixed_weights[key] = mixed_weights[key][:self.target_num_classes]

                    # 如果目标类别数较多，使用插值或复制
                    elif self.target_num_classes > original_classes:
                        # 使用最近邻插值扩展权重
                        original_weight = mixed_weights[key]
                        new_weight = torch.zeros(self.target_num_classes, *original_weight.shape[1:])

                        # 复制现有权重
                        new_weight[:original_classes] = original_weight

                        # 对剩余的类别使用平均权重
                        if self.target_num_classes > original_classes:
                            avg_weight = torch.mean(original_weight, dim=0, keepdim=True)
                            new_weight[original_classes:] = avg_weight.repeat(
                                self.target_num_classes - original_classes, *[1] * (avg_weight.dim() - 1)
                            )

                        mixed_weights[key] = new_weight

            elif 'bias' in key:
                original_classes = mixed_weights[key].size(0)

                if original_classes != self.target_num_classes:
                    if self.target_num_classes < original_classes:
                        mixed_weights[key] = mixed_weights[key][:self.target_num_classes]
                    elif self.target_num_classes > original_classes:
                        new_bias = torch.zeros(self.target_num_classes)
                        new_bias[:original_classes] = mixed_weights[key]
                        mixed_weights[key] = new_bias

    def save_mixed_weights(self, mixed_weights: Dict[str, torch.Tensor],
                          save_path: str, metadata: Dict = None):
        """
        保存混合后的权重

        Args:
            mixed_weights: 混合后的权重
            save_path: 保存路径
            metadata: 元数据信息
        """
        save_dict = {
            'state_dict': mixed_weights,
            'metadata': metadata or {},
            'target_num_classes': self.target_num_classes,
            'similarity_scores': self.similarity_scores
        }

        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        torch.save(save_dict, save_path)
        logger.info(f"混合权重已保存到: {save_path}")

    def load_mixed_weights(self, load_path: str) -> Tuple[Dict[str, torch.Tensor], Dict]:
        """
        加载混合后的权重

        Args:
            load_path: 加载路径

        Returns:
            (混合权重, 元数据)
        """
        checkpoint = torch.load(load_path, map_location='cpu')

        mixed_weights = checkpoint.get('state_dict', checkpoint)
        metadata = checkpoint.get('metadata', {})

        # 恢复相似度分数
        if 'similarity_scores' in checkpoint:
            self.similarity_scores = checkpoint['similarity_scores']

        logger.info(f"从 {load_path} 加载混合权重")
        return mixed_weights, metadata


def create_multi_pretrain_config(target_classes: List[str],
                                datasets: List[str] = None,
                                mixing_strategy: str = "similarity_weighted",
                                backbone: str = "resnet50") -> Dict:
    """
    创建多预训练数据集混合的配置

    Args:
        target_classes: 目标数据集的类别名称列表
        datasets: 要混合的数据集列表，默认为所有可用数据集
        mixing_strategy: 混合策略
        backbone: 骨干网络

    Returns:
        配置字典
    """
    if datasets is None:
        datasets = ["ADE20K", "Cityscapes", "COCO-Stuff", "Pascal-VOC"]

    config = {
        "multi_pretrain": {
            "enabled": True,
            "target_classes": target_classes,
            "target_num_classes": len(target_classes),
            "datasets": datasets,
            "mixing_strategy": mixing_strategy,
            "backbone": backbone,
            "weights_dir": "pretrained_weights",
            "save_mixed_weights": True,
            "mixed_weights_path": f"pretrained_weights/mixed_{mixing_strategy}_{backbone}.pth"
        }
    }

    return config


def apply_multi_pretrain_to_model(model, cfg: Dict, target_classes: List[str]) -> nn.Module:
    """
    将多预训练数据集混合应用到模型

    Args:
        model: 原始模型
        cfg: 配置字典
        target_classes: 目标类别列表

    Returns:
        应用了混合权重的模型
    """
    if not cfg.get('multi_pretrain', {}).get('enabled', False):
        logger.info("多预训练数据集混合未启用")
        return model

    multi_cfg = cfg['multi_pretrain']

    # 创建权重混合器
    mixer = MultiPretrainMixer(
        target_classes=target_classes,
        target_num_classes=multi_cfg['target_num_classes']
    )

    # 检查是否已有混合权重
    mixed_weights_path = multi_cfg.get('mixed_weights_path')
    if mixed_weights_path and os.path.exists(mixed_weights_path):
        logger.info(f"加载已有的混合权重: {mixed_weights_path}")
        mixed_weights, metadata = mixer.load_mixed_weights(mixed_weights_path)

        # 应用权重到模型
        missing_keys, unexpected_keys = model.load_state_dict(mixed_weights, strict=False)
        logger.info(f"加载混合权重完成，缺失键: {len(missing_keys)}, 意外键: {len(unexpected_keys)}")

        return model

    # 创建新的混合权重
    logger.info("创建新的混合预训练权重...")
    mixed_model = mixer.create_mixed_model(
        model_template=model,
        datasets=multi_cfg['datasets'],
        backbone=multi_cfg['backbone'],
        mixing_strategy=multi_cfg['mixing_strategy'],
        weights_dir=multi_cfg['weights_dir']
    )

    # 保存混合权重
    if multi_cfg.get('save_mixed_weights', False) and mixed_weights_path:
        # 获取混合权重
        weights_dict = mixer.load_multiple_pretrained_weights(
            multi_cfg['datasets'],
            multi_cfg['backbone'],
            multi_cfg['weights_dir']
        )

        if weights_dict:
            mixed_weights = mixer.mix_weights_adaptive(weights_dict, multi_cfg['mixing_strategy'])

            metadata = {
                'datasets': multi_cfg['datasets'],
                'mixing_strategy': multi_cfg['mixing_strategy'],
                'backbone': multi_cfg['backbone'],
                'target_classes': target_classes
            }

            mixer.save_mixed_weights(mixed_weights, mixed_weights_path, metadata)

    return mixed_model