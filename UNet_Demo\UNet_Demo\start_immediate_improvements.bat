@echo off
echo ========================================
echo 启动立即改进训练
echo 目标：从mIoU 0.36快速提升到0.4+
echo ========================================

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误：Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo 检查CUDA可用性...
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}'); print(f'CUDA设备数: {torch.cuda.device_count()}'); print(f'当前设备: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"CPU\"}')"

echo 检查配置文件...
if not exist "config_immediate_improvements.yaml" (
    echo 错误：配置文件 config_immediate_improvements.yaml 不存在
    pause
    exit /b 1
)

echo 检查数据集...
if not exist "VOCdevkit\VOC2025\ImageSets\Segmentation\train.txt" (
    echo 错误：训练数据集不存在
    pause
    exit /b 1
)

echo 创建日志目录...
if not exist "logs\immediate_improvements" mkdir "logs\immediate_improvements"

echo ========================================
echo 开始训练...
echo ========================================

python train_immediate_improvements.py

echo ========================================
echo 训练完成
echo ========================================

pause
